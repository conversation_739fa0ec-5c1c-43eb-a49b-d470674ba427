// file: /js/codeEditor.js
// محرر الكود (Monaco Editor)

// تعديل وظيفة فتح الملف لإضافة مساحة في نهاية المحرر
function openFile(fileId) {
    const file = workspace.files[fileId];
    if (!file) return;

    // تحديث الملف النشط
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة إذا لم يكن موجوداً بالفعل
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    // تحديث علامات تبويب الملفات
    updateFileTabs();

    // تحديث مستكشف الملفات لإظهار الملف النشط
    updateFileExplorer();

    // إظهار المحرر إذا كان مخفياً
    const executor = document.getElementById('code-executor');
    if (executor && !executor.classList.contains('visible')) {
        executor.classList.add('visible');
    }

    // تحديث محتوى المحرر
    const editorContainer = document.getElementById('editor-container');
    if (editorContainer) {
        try {
            // إذا كان المحرر موجوداً بالفعل، نقوم بتحديث المحتوى فقط
            if (monacoEditor) {
                const model = monaco.editor.createModel(file.content, file.language);
                monacoEditor.setModel(model);
            } else {
                // إنشاء محرر جديد
                monacoEditor = monaco.editor.create(editorContainer, {
                    value: file.content,
                    language: file.language,
                    theme: 'vs-dark',
                    automaticLayout: true,
                    minimap: {
                        enabled: true
                    },
                    scrollBeyondLastLine: true, // إضافة تمرير بعد السطر الأخير
                    padding: { // إضافة تباعد في المحرر
                        top: 10,
                        bottom: 20 // تباعد إضافي في الأسفل
                    },
                    lineNumbers: 'on',
                    roundedSelection: true,
                    scrollbar: {
                        useShadows: true,
                        verticalHasArrows: true,
                        horizontalHasArrows: true,
                        vertical: 'visible',
                        horizontal: 'visible',
                        verticalScrollbarSize: 12,
                        horizontalScrollbarSize: 12
                    }
                });

                // Set direction to LTR for code
                if (monacoEditor.updateOptions) {
                    monacoEditor.updateOptions({ direction: 'ltr' });
                }

                if (monacoEditor.getDomNode) {
                    monacoEditor.getDomNode().style.direction = 'ltr';
                }

                // Update content when changed
                if (monacoEditor.onDidChangeModelContent) {
                    monacoEditor.onDidChangeModelContent(function () {
                        if (fileId && workspace.files[fileId]) {
                            workspace.files[fileId].content = monacoEditor.getValue();
                        }
                    });
                }

                // Add cursor position tracking (VS Code status bar)
                monacoEditor.onDidChangeCursorPosition(function (e) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 0) {
                        statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                    }
                });

                // Add quick action buttons (like VS Code)
                const quickActions = document.createElement('div');
                quickActions.className = 'quick-actions';
                quickActions.innerHTML = `
                    <div class="quick-action" title="Split Editor" onclick="splitEditor()"><i class="fas fa-columns"></i></div>
                    <div class="quick-action" title="More Options" onclick="showEditorOptions(event)"><i class="fas fa-ellipsis-v"></i></div>
                `;
                editorContainer.appendChild(quickActions);
            }

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    }

    // تحديث شريط الحالة
    updateStatusBar(file);
}

// تقسيم المحرر
function splitEditor() {
    alert('Editor split functionality will be available in the next update.');
}

// عرض خيارات المحرر
function showEditorOptions(event) {
    event.stopPropagation();

    let optionsMenu = document.getElementById('editor-options-menu');

    if (optionsMenu) {
        optionsMenu.remove();
        return;
    }

    optionsMenu = document.createElement('div');
    optionsMenu.id = 'editor-options-menu';
    optionsMenu.className = 'editor-options-menu';

    optionsMenu.innerHTML = `
        <div class="editor-option" onclick="changeEditorTheme()">Change Theme</div>
        <div class="editor-option" onclick="changeFontSize()">Change Font Size</div>
        <div class="editor-option" onclick="toggleMinimap()">Toggle Minimap</div>
        <div class="editor-option" onclick="formatDocument()">Format Document</div>
    `;

    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    document.body.appendChild(optionsMenu);

    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    setTimeout(() => {
        document.addEventListener('click', closeEditorOptions);
    }, 10);
}

function closeEditorOptions() {
    const optionsMenu = document.getElementById('editor-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeEditorOptions);
}

// وظائف خيارات المحرر
function changeEditorTheme() {
    const themes = ['vs-dark', 'vs', 'hc-black'];
    const theme = prompt(`Select editor theme (0-${themes.length - 1}):\n0: Dark\n1: Light\n2: High Contrast`, '0');

    if (theme !== null && monacoEditor) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            monaco.editor.setTheme(themes[themeIndex]);
            localStorage.setItem('editorTheme', themes[themeIndex]);
        }
    }
    closeEditorOptions();
}

function changeFontSize() {
    const fontSize = prompt('Enter editor font size (px):', '14');
    if (fontSize && monacoEditor) {
        const size = parseInt(fontSize);
        if (!isNaN(size) && size > 0) {
            monacoEditor.updateOptions({ fontSize: size });
            localStorage.setItem('editorFontSize', size);
        }
    }
    closeEditorOptions();
}

function toggleMinimap() {
    if (monacoEditor) {
        const currentState = monacoEditor.getOption(monaco.editor.EditorOption.minimap).enabled;
        monacoEditor.updateOptions({ minimap: { enabled: !currentState } });
        localStorage.setItem('editorMinimapEnabled', !currentState);
    }
    closeEditorOptions();
}

function formatDocument() {
    if (monacoEditor) {
        monacoEditor.getAction('editor.action.formatDocument').run();
    }
    closeEditorOptions();
}

// تحديث شريط الحالة
function updateStatusBar(file) {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const languageIndicator = statusBar.querySelector('.language-indicator .status-item-text');
    if (languageIndicator && file) {
        languageIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
    }
}

// دالة إعادة فتح المحرر
function reopenEditor(event) {
    if (event) event.stopPropagation();

    const executor = document.getElementById('code-executor');
    if (executor && !executor.classList.contains('visible')) {
        executor.classList.add('visible');

        // إذا كان هناك ملف نشط، نعيد فتحه
        if (activeFileId && workspace.files[activeFileId]) {
            openFile(activeFileId);
        }
    }
}

// دالة إخفاء المحرر
function hideCodeExecutor() {
    const executor = document.getElementById('code-executor');
    if (executor) {
        executor.classList.remove('visible');
    }
}

// دالة إغلاق ملف
function closeFile(fileId, event) {
    if (event) event.stopPropagation();

    // إزالة الملف من قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));

    // إذا كان هذا هو الملف النشط، نحتاج لتحديد ملف آخر كنشط
    if (activeFileId === fileId) {
        if (updatedOpenFiles.length > 0) {
            // فتح آخر ملف في القائمة
            activeFileId = updatedOpenFiles[updatedOpenFiles.length - 1];
            openFile(activeFileId);
        } else {
            // إذا لم تعد هناك ملفات مفتوحة، نخفي المحرر
            activeFileId = null;
            hideCodeExecutor();
        }
    }

    updateFileTabs();
    updateFileExplorer();
}

// دالة تحديث علامات تبويب الملفات
function updateFileTabs() {
    const tabsContainer = document.querySelector('.editor-tabs');
    if (!tabsContainer) return;

    tabsContainer.innerHTML = '';

    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');

    openFiles.forEach(fileId => {
        if (workspace.files[fileId]) {
            const file = workspace.files[fileId];
            const tab = document.createElement('div');
            tab.className = `editor-tab ${activeFileId === fileId ? 'active' : ''}`;
            tab.innerHTML = `
                <span class="tab-icon">${getFileIcon(file.name)}</span>
                <span class="tab-name">${file.name}</span>
                <button class="tab-close" onclick="closeFile('${fileId}', event)">
                    <i class="fas fa-times"></i>
                </button>
            `;

            tab.onclick = (e) => {
                if (!e.target.closest('.tab-close')) {
                    openFile(fileId);
                }
            };

            tabsContainer.appendChild(tab);
        }
    });
}
