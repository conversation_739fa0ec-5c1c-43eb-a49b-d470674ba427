// file: /js/terminal.js
// إدارة التيرمنال

// إنشاء تيرمنال جديد
function createNewTerminal() {
    // حفظ محتوى التيرمنال الحالي
    saveTerminalContent();

    // إنشاء تيرمنال جديد
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        terminalElem.innerHTML = '<div class="terminal-welcome">New terminal session started.</div>';
    }

    // تنشيط تبويب Terminal
    const terminalTab = document.querySelector('.terminal-tab[data-tab="terminal"]');
    if (terminalTab) {
        const allTabs = document.querySelectorAll('.terminal-tab');
        allTabs.forEach(tab => tab.classList.remove('active'));
        terminalTab.classList.add('active');
    }
}

// تقسيم التيرمنال
function splitTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // تحقق إذا كان التيرمنال مقسم بالفعل
    if (executorFooter.classList.contains('split')) {
        // إذا كان مقسم بالفعل، نعيده إلى الوضع العادي
        executorFooter.classList.remove('split');

        // إزالة التيرمنال الثاني
        const secondTerminal = document.getElementById('second-terminal');
        if (secondTerminal) {
            secondTerminal.remove();
        }
    } else {
        // إضافة صنف split للتيرمنال
        executorFooter.classList.add('split');

        // إنشاء تيرمنال ثاني
        const terminalContainer = document.createElement('div');
        terminalContainer.className = 'terminal second-terminal';
        terminalContainer.id = 'second-terminal';
        terminalContainer.innerHTML = '<div class="terminal-welcome">Split terminal ready.</div>';

        // إضافة التيرمنال الثاني بعد التيرمنال الأول
        const firstTerminal = document.getElementById('executor-result');
        if (firstTerminal && firstTerminal.parentNode) {
            firstTerminal.parentNode.insertBefore(terminalContainer, firstTerminal.nextSibling);
        }
    }
}

// عرض خيارات إضافية للتيرمنال
function showTerminalOptions(event) {
    // إيقاف انتشار الحدث لمنع إغلاق القائمة فورًا
    event.stopPropagation();

    // التحقق مما إذا كانت القائمة موجودة بالفعل
    let optionsMenu = document.getElementById('terminal-options-menu');

    if (optionsMenu) {
        // إذا كانت القائمة مفتوحة بالفعل، نغلقها
        optionsMenu.remove();
        return;
    }

    // إنشاء قائمة الخيارات
    optionsMenu = document.createElement('div');
    optionsMenu.id = 'terminal-options-menu';
    optionsMenu.className = 'terminal-options-menu';

    // إضافة خيارات القائمة
    optionsMenu.innerHTML = `
        <div class="terminal-option" onclick="changeTerminalFont()">Change Font Size</div>
        <div class="terminal-option" onclick="changeTerminalTheme()">Change Terminal Theme</div>
        <div class="terminal-option" onclick="clearTerminalHistory()">Clear Terminal History</div>
        <div class="terminal-option" onclick="configureTerminal()">Terminal Settings</div>
    `;

    // تحديد موضع القائمة بالنسبة للزر
    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    // إضافة القائمة إلى DOM
    document.body.appendChild(optionsMenu);

    // تحديد موضع القائمة
    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    // إضافة مستمع حدث لإغلاق القائمة عند النقر في أي مكان آخر
    setTimeout(() => {
        document.addEventListener('click', closeTerminalOptions);
    }, 10);
}

// إغلاق قائمة خيارات التيرمنال
function closeTerminalOptions() {
    const optionsMenu = document.getElementById('terminal-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeTerminalOptions);
}

// وظائف قائمة الخيارات
function changeTerminalFont() {
    const fontSize = prompt('Enter terminal font size (px):', '13');
    if (fontSize) {
        const terminal = document.getElementById('executor-result');
        if (terminal) {
            terminal.style.fontSize = `${fontSize}px`;
            localStorage.setItem('terminalFontSize', fontSize);
        }
    }
    closeTerminalOptions();
}

function changeTerminalTheme() {
    const themes = ['Dark (Default)', 'Light', 'Blue', 'Green', 'Amber'];
    const theme = prompt(`Select terminal theme (0-${themes.length - 1}):\n${themes.map((t, i) => `${i}: ${t}`).join('\n')}`, '0');

    if (theme !== null) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            const terminal = document.getElementById('executor-result');
            if (terminal) {
                // إزالة جميع أصناف السمات السابقة
                terminal.classList.remove('theme-dark', 'theme-light', 'theme-blue', 'theme-green', 'theme-amber');

                // إضافة صنف السمة الجديدة
                const themeClass = `theme-${themes[themeIndex].toLowerCase().split(' ')[0]}`;
                terminal.classList.add(themeClass);
                localStorage.setItem('terminalTheme', themeClass);
            }
        }
    }
    closeTerminalOptions();
}

function clearTerminalHistory() {
    if (confirm('Are you sure you want to clear all terminal history?')) {
        window._terminalContent = null;
        localStorage.removeItem('terminalContent');

        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal history cleared.</div>';
        }
    }
    closeTerminalOptions();
}

function configureTerminal() {
    alert('Terminal settings will be available in the next update.');
    closeTerminalOptions();
}

// دالة تبديل رؤية التيرمنال
function toggleTerminal() {
    const executor = document.getElementById('code-executor');
    const terminalSection = document.querySelector('.executor-footer');
    
    if (!executor || !terminalSection) return;

    // إذا كان المحرر مخفي، نظهره أولاً
    if (!executor.classList.contains('visible')) {
        executor.classList.add('visible');
    }

    // تبديل رؤية قسم التيرمنال
    terminalSection.classList.toggle('hidden');
    
    // حفظ الحالة
    const isHidden = terminalSection.classList.contains('hidden');
    localStorage.setItem('terminalHidden', isHidden);
}

// دالة تحميل إعدادات التيرمنال
function loadTerminalSettings() {
    const terminal = document.getElementById('executor-result');
    if (!terminal) return;

    // تحميل حجم الخط
    const fontSize = localStorage.getItem('terminalFontSize');
    if (fontSize) {
        terminal.style.fontSize = `${fontSize}px`;
    }

    // تحميل السمة
    const theme = localStorage.getItem('terminalTheme');
    if (theme) {
        terminal.classList.add(theme);
    }

    // تحميل حالة الإخفاء
    const isHidden = localStorage.getItem('terminalHidden') === 'true';
    const terminalSection = document.querySelector('.executor-footer');
    if (isHidden && terminalSection) {
        terminalSection.classList.add('hidden');
    }

    // تحميل المحتوى المحفوظ
    const savedContent = localStorage.getItem('terminalContent');
    if (savedContent) {
        window._terminalContent = savedContent;
        terminal.innerHTML = savedContent;
    }
}

// دالة حفظ محتوى التيرمنال في التخزين المحلي
function saveTerminalToStorage() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem && terminalElem.innerHTML.trim() !== '') {
        localStorage.setItem('terminalContent', terminalElem.innerHTML);
    }
}

// دالة تنفيذ أمر في التيرمنال
function executeTerminalCommand(command) {
    const terminalElem = document.getElementById('executor-result');
    if (!terminalElem) return;

    // إضافة الأمر إلى التيرمنال
    terminalElem.innerHTML += `<div class="terminal-command">$ ${command}</div>`;

    // معالجة الأوامر الأساسية
    switch (command.toLowerCase().trim()) {
        case 'clear':
            clearTerminal();
            break;
        case 'ls':
        case 'dir':
            listFiles();
            break;
        case 'pwd':
            showCurrentPath();
            break;
        case 'help':
            showTerminalHelp();
            break;
        default:
            terminalElem.innerHTML += `<div class="terminal-error">Command not found: ${command}</div>`;
            terminalElem.innerHTML += `<div class="terminal-info">Type 'help' for available commands</div>`;
    }

    terminalElem.scrollTop = terminalElem.scrollHeight;
    saveTerminalContent();
}

// دالة عرض الملفات
function listFiles() {
    const terminalElem = document.getElementById('executor-result');
    if (!terminalElem) return;

    const currentFolder = workspace.folders[workspace.currentPath] || workspace.folders['root'];
    
    if (currentFolder && currentFolder.children && currentFolder.children.length > 0) {
        terminalElem.innerHTML += `<div class="terminal-output">Contents of ${workspace.currentPath}:</div>`;
        
        currentFolder.children.forEach(childId => {
            if (childId.startsWith('folder_')) {
                const folderObj = Object.values(workspace.folders).find(f => f && f.id === childId);
                if (folderObj) {
                    terminalElem.innerHTML += `<div class="terminal-output">📁 ${folderObj.name}/</div>`;
                }
            } else {
                const file = workspace.files[childId];
                if (file) {
                    terminalElem.innerHTML += `<div class="terminal-output">📄 ${file.name}</div>`;
                }
            }
        });
    } else {
        terminalElem.innerHTML += `<div class="terminal-output">Directory is empty</div>`;
    }
}

// دالة عرض المسار الحالي
function showCurrentPath() {
    const terminalElem = document.getElementById('executor-result');
    if (!terminalElem) return;

    terminalElem.innerHTML += `<div class="terminal-output">${workspace.currentPath}</div>`;
}

// دالة عرض مساعدة التيرمنال
function showTerminalHelp() {
    const terminalElem = document.getElementById('executor-result');
    if (!terminalElem) return;

    const helpText = `
        <div class="terminal-output">Available commands:</div>
        <div class="terminal-output">  clear    - Clear terminal</div>
        <div class="terminal-output">  ls/dir   - List files and folders</div>
        <div class="terminal-output">  pwd      - Show current path</div>
        <div class="terminal-output">  help     - Show this help</div>
    `;
    
    terminalElem.innerHTML += helpText;
}
