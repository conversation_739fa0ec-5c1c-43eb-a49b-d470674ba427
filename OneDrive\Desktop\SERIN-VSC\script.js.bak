// حالة المحادثات
let conversations = [];
let currentConversationId = null;
const codeContexts = {};
let monacoEditor = null;
let activeFileId = null;

// نظام الملفات
let workspace = {
    files: {},
    folders: {
        'root': {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        }
    },
    currentPath: '/'
};

// وظائف نظام الملفات
function createFile(name, content, language, path = '/') {
    // تنظيف المسار
    path = path.replace(/\/+/g, '/');
    if (!path.endsWith('/')) path += '/';
    if (!path.startsWith('/')) path = '/' + path;

    // التحقق من وجود ملف بنفس الاسم في نفس المجلد وإضافة رقم إذا كان موجودًا
    let finalName = name;
    let counter = 1;

    // البحث عن الملفات الموجودة بنفس الاسم في نفس المجلد
    const filesInFolder = Object.values(workspace.files).filter(file =>
        file.path.startsWith(path) && file.path.substring(path.length) === name
    );

    // إذا وجد ملف بنفس الاسم، نضيف رقم للملف الجديد
    if (filesInFolder.length > 0) {
        // استخراج اسم الملف واللاحقة
        const lastDotIndex = name.lastIndexOf('.');
        const baseName = lastDotIndex !== -1 ? name.substring(0, lastDotIndex) : name;
        const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

        // البحث عن أعلى رقم موجود في الملفات المتشابهة
        const regex = new RegExp(`^${escapeRegExp(baseName)} \\((\\d+)\\)${escapeRegExp(extension)}$`);

        Object.values(workspace.files).forEach(file => {
            if (file.path.startsWith(path)) {
                const fileName = file.path.substring(path.length);
                const match = fileName.match(regex);
                if (match) {
                    const num = parseInt(match[1]);
                    if (num >= counter) {
                        counter = num + 1;
                    }
                }
            }
        });

        // إنشاء اسم الملف الجديد مع الرقم
        finalName = `${baseName} (${counter})${extension}`;
    }

    // إضافة تعليق الملف إذا لم يكن موجودًا
    content = ensureFileComment(content, language, finalName, path);

    const fileId = 'file_' + Date.now() + Math.random().toString(36).substr(2, 5);
    const filePath = path + finalName;

    workspace.files[fileId] = {
        id: fileId,
        name: finalName,
        path: filePath,
        content: content,
        language: language,
        type: 'file'
    };

    // إضافة الملف إلى المجلد مع التحقق من وجود المجلد
    if (!workspace.folders[path]) {
        createFolder(path);
    }
    
    // تحقق مرة أخرى من وجود المجلد بعد محاولة إنشائه
    if (workspace.folders[path] && workspace.folders[path].children) {
        workspace.folders[path].children.push(fileId);
    } else {
        // إذا لم يكن المجلد موجودًا، أضف الملف إلى المجلد الجذر
        if (!workspace.folders['root']) {
            workspace.folders['root'] = {
                id: 'root',
                name: 'root',
                path: '/',
                type: 'folder',
                children: []
            };
        }
        if (!workspace.folders['root'].children) {
            workspace.folders['root'].children = [];
        }
        workspace.folders['root'].children.push(fileId);
    }

    // حفظ التغييرات مباشرة
    saveWorkspace();

    return fileId;
}

function ensureFileComment(content, language, fileName, filePath) {
    // احذف أي تعليق مسار في بداية الملف (JS, Python, HTML, CSS)
    content = content.replace(/^\s*(\/\/|#)\s*file:.*\n?/i, ''); // JS/Python
    content = content.replace(/^\s*<!--\s*file:.*?-->\s*\n?/i, ''); // HTML
    content = content.replace(/^\s*\/\*\s*file:.*?\*\/\s*\n?/is, ''); // CSS/JS block
        return content;
}

function createFolder(path) {
    const parts = path.split('/').filter(p => p);
    let currentPath = '/';

    for (const part of parts) {
        const newPath = currentPath + part + '/';
        if (!workspace.folders[newPath]) {
            const folderId = 'folder_' + Date.now() + Math.random().toString(36).substr(2, 5);
            workspace.folders[newPath] = {
                id: folderId,
                name: part,
                path: newPath,
                type: 'folder',
                children: []
            };

            // إضافة المجلد إلى المجلد الأب مع التحقق
            if (currentPath !== '/') {
                if (workspace.folders[currentPath] && workspace.folders[currentPath].children) {
                    workspace.folders[currentPath].children.push(folderId);
                }
            } else if (workspace.folders['root'] && workspace.folders['root'].children) {
                workspace.folders['root'].children.push(folderId);
            }
        }
        currentPath = newPath;
    }

    // حفظ التغييرات مباشرة
    saveWorkspace();
}

function updateFileExplorer() {
    const explorerContent = document.getElementById('explorer-content');
    explorerContent.innerHTML = '';

    // Breadcrumb Navigation
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'breadcrumb';

    const paths = workspace.currentPath.split('/').filter(p => p);
    let currentPath = '/';

    breadcrumb.innerHTML = `<span class="breadcrumb-item" onclick="navigateToFolder('/')">root</span>`;

    paths.forEach((part, index) => {
        currentPath += part + '/';
        breadcrumb.innerHTML += `
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" onclick="navigateToFolder('${currentPath}')">${part}</span>
                `;
    });

    explorerContent.appendChild(breadcrumb);

    // Explorer Sections - VS Code style
    const openEditorsSection = document.createElement('div');
    openEditorsSection.className = 'explorer-section';
    openEditorsSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>المحررات المفتوحة</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" title="حفظ الكل"><i class="fas fa-save"></i></button>
                        <button class="explorer-section-action" title="إغلاق الكل" onclick="closeAllFiles()"><i class="fas fa-times"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="open-editors-content"></div>
            `;
    explorerContent.appendChild(openEditorsSection);

    // Populate Open Editors section - عرض جميع الملفات المفتوحة وليس فقط الملف النشط
    const openEditorsContent = document.getElementById('open-editors-content');
    if (openEditorsContent) {
        // الحصول على قائمة الملفات المفتوحة
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');

        if (openFiles.length > 0) {
            openFiles.forEach(fileId => {
                if (workspace.files[fileId]) {
                    const file = workspace.files[fileId];
                    const fileItem = document.createElement('div');
                    fileItem.className = `explorer-item file ${activeFileId === fileId ? 'active' : ''}`;

                    // تحقق مما إذا كان المحرر مفتوحًا حاليًا
                    const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

                    fileItem.innerHTML = `
                                <div class="explorer-item-content">
                                    <span class="explorer-item-icon">
                                        ${getFileIcon(file.name)}
                                    </span>
                                    <span class="explorer-item-name">${file.name}</span>
                                </div>
                                <div class="explorer-item-actions">
                                    ${!isEditorVisible && activeFileId === fileId ?
                            `<button class="explorer-item-action" onclick="reopenEditor(event)" title="إعادة فتح المحرر">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>` : ''
                        }
                                    <button class="explorer-item-action" onclick="closeFile('${file.id}', event)" title="إغلاق">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            `;
                    fileItem.onclick = (e) => {
                        if (!e.target.closest('.explorer-item-action')) {
                            // تنشيط هذا الملف
                            activeFileId = file.id;
                            updateFileTabs();

                            // إذا كان المحرر مغلقًا والملف هو النشط، نعيد فتحه
                            if (!isEditorVisible && activeFileId === fileId) {
                                reopenEditor();
                            } else {
                                // إذا كان المحرر مفتوحًا أو الملف ليس هو النشط، نفتح الملف
                                openFile(file.id);
                            }
                        }
                    };
                    openEditorsContent.appendChild(fileItem);
                }
            });
        } else {
            // إذا لم يكن هناك ملف مفتوح، نعرض رسالة
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-editors-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">لا توجد ملفات مفتوحة</span>`;
            openEditorsContent.appendChild(emptyMessage);
        }
    }

    // Project Files Section
    const projectSection = document.createElement('div');
    projectSection.className = 'explorer-section';
    projectSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>${paths.length > 0 ? paths[paths.length - 1].toUpperCase() : 'مساحة العمل'}</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" onclick="createNewFile()" title="ملف جديد"><i class="fas fa-file"></i></button>
                        <button class="explorer-section-action" onclick="createNewFolder()" title="مجلد جديد"><i class="fas fa-folder"></i></button>
                        <button class="explorer-section-action" onclick="refreshFileExplorer()" title="تحديث المستكشف"><i class="fas fa-sync"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="project-files-content"></div>
            `;
    explorerContent.appendChild(projectSection);

    // Populate Project Files section
    const projectFilesContent = document.getElementById('project-files-content');
    const currentFolder = workspace.folders[workspace.currentPath] || workspace.folders['root'];

    if (currentFolder && Array.isArray(currentFolder.children)) {
        // تحقق من وجود أي ملفات أو مجلدات في المسار الحالي
        if (currentFolder.children.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-folder-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">المجلد فارغ</span>`;
            projectFilesContent.appendChild(emptyMessage);
            return;
        }

        // Sort: Folders first, then files alphabetically
        const sortedItems = [...currentFolder.children].sort((a, b) => {
            const isAFolder = a.startsWith('folder_');
            const isBFolder = b.startsWith('folder_');

            if (isAFolder && !isBFolder) return -1;
            if (!isAFolder && isBFolder) return 1;

            // Sort by name if both are folders or both are files
            const itemA = isAFolder ?
                Object.values(workspace.folders).find(f => f && f.id === a) :
                workspace.files[a];
            const itemB = isBFolder ?
                Object.values(workspace.folders).find(f => f && f.id === b) :
                workspace.files[b];

            if (itemA && itemB) {
                return itemA.name.localeCompare(itemB.name);
            }
            return 0;
        });

        sortedItems.forEach(childId => {
            let item;
            if (childId.startsWith('folder_')) {
                const folderObj = Object.values(workspace.folders).find(f => f && f.id === childId);
                item = folderObj ? workspace.folders[folderObj.path] : null;

                // تحقق إضافي من صحة المجلد
                if (!item) {
                    console.warn('تم العثور على معرف مجلد غير صالح:', childId);
                    return;
                }
            } else {
                item = workspace.files[childId];

                // تحقق من وجود الملف
                if (!item) {
                    console.warn('تم العثور على معرف ملف غير صالح:', childId);
                    return;
                }
            }

            const itemElement = document.createElement('div');
            itemElement.className = `explorer-item ${item.type} ${activeFileId === item.id ? 'active' : ''}`;
            itemElement.innerHTML = `
                        <div class="explorer-item-content">
                            <span class="explorer-item-icon" style="order: -1">
                                ${item.type === 'folder' ? '<i class="fas fa-folder"></i>' : getFileIcon(item.name)}
                        </span>
                        <span class="explorer-item-name">${item.name}</span>
                        </div>
                        <div class="explorer-item-actions">
                            <button class="explorer-item-action" onclick="deleteFile('${item.id}', event)" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                            ${item.type === 'file' ? `
                            <button class="explorer-item-action" onclick="renameFile('${item.id}', event)" title="Rename">
                                <i class="fas fa-edit"></i>
                            </button>
                            ` : ''}
                        </div>
                    `;

            itemElement.onclick = (e) => {
                if (!e.target.closest('.explorer-item-action')) {
                    if (item.type === 'folder') {
                        navigateToFolder(item.path);
                    } else {
                        openFile(item.id);
                    }
                }
            };

            projectFilesContent.appendChild(itemElement);
        });
    } else {
        // إذا كان المجلد غير موجود، نعرض رسالة
        console.warn('المجلد غير موجود:', workspace.currentPath);
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-folder-message';
        emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">المجلد غير موجود</span>`;
        projectFilesContent.appendChild(emptyMessage);
    }
}

// Helper function to get appropriate file icon based on extension
function getFileIcon(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'html': '<i class="fas fa-file-code" style="color: #e44d26;"></i>',
        'css': '<i class="fas fa-file-code" style="color: #264de4;"></i>',
        'js': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'json': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'ts': '<i class="fas fa-file-code" style="color: #007acc;"></i>',
        'py': '<i class="fas fa-file-code" style="color: #306998;"></i>',
        'php': '<i class="fas fa-file-code" style="color: #777bb4;"></i>',
        'md': '<i class="fas fa-file-alt" style="color: #03a9f4;"></i>',
        'txt': '<i class="fas fa-file-alt" style="color: #9e9e9e;"></i>',
        'jpg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'jpeg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'png': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'gif': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'svg': '<i class="fas fa-file-image" style="color: #ff9800;"></i>',
        'pdf': '<i class="fas fa-file-pdf" style="color: #f44336;"></i>',
        'zip': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
        'rar': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>'
    };

    return iconMap[extension] || '<i class="fas fa-file-code" style="color: #75beff;"></i>';
}

// Function to refresh the file explorer
function refreshFileExplorer() {
    updateFileExplorer();
}

function navigateToFolder(path) {
    // تنظيف المسار
    if (!path.startsWith('/')) {
        path = '/' + path;
    }
    if (!path.endsWith('/')) {
        path += '/';
    }

    // تأكد من وجود المجلد
    if (path !== '/' && !workspace.folders[path]) {
        console.error('المجلد غير موجود:', path);
        // إذا كان المجلد غير موجود، نعود للمجلد الأب
        const parentPath = path.substring(0, path.lastIndexOf('/', path.length - 2) + 1);
        if (parentPath && workspace.folders[parentPath]) {
            path = parentPath;
        } else {
            path = '/';
        }
    }

    workspace.currentPath = path;

    // حفظ المسار الحالي في التخزين المحلي
    localStorage.setItem('currentPath', path);

    updateFileExplorer();
}

// Add the VSCode-style status bar
function addStatusBar() {
    // Check if status bar already exists
    if (document.querySelector('.status-bar')) return;

    const codeExecutor = document.getElementById('code-executor');
    if (!codeExecutor) return;

    const statusBar = document.createElement('div');
    statusBar.className = 'status-bar';
    statusBar.innerHTML = `
                <div class="status-items-left">
            <div class="status-item icon-only-on-small">
                <i class="fas fa-code-branch"></i>
                <span class="status-item-text">main</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-sync"></i>
            </div>
            <div class="status-item terminal-toggle-btn">
                <i class="fas fa-terminal"></i>
                <span class="status-item-text">Terminal</span>
            </div>
            <div class="status-item hide-on-tiny">
                <i class="fas fa-bell"></i>
            </div>
                </div>
                <div class="status-items-right">
            <div class="status-item cursor-position always-show-text">
                <span class="status-item-text">Ln 1, Col 1</span>
            </div>
            <div class="status-item indent-setting icon-only-on-small">
                <i class="fas fa-indent"></i>
                <span class="status-item-text">Spaces: 4</span>
            </div>
            <div class="status-item hide-on-small">UTF-8</div>
            <div class="status-item language-indicator">
                <i class="fas fa-file-code"></i>
                <span class="status-item-text">JavaScript</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-check-circle"></i>
                <span class="status-item-text">Prettier</span>
            </div>
                </div>
            `;
    codeExecutor.appendChild(statusBar);

    // Make status items interactive
    statusBar.querySelectorAll('.status-item').forEach(item => {
        item.addEventListener('click', function() {
            // Show a tooltip or perform an action when clicked
            if (this.classList.contains('indent-setting')) {
                const options = ['Spaces: 2', 'Spaces: 4', 'Tabs: 4'];
                const currentIndex = options.findIndex(opt => {
                    const text = this.querySelector('.status-item-text');
                    return text && opt === text.textContent;
                });
                const nextIndex = (currentIndex + 1) % options.length;
                const text = this.querySelector('.status-item-text');
                if (text) {
                    text.textContent = options[nextIndex];
                }

                // Also update editor if available
                if (monacoEditor && monacoEditor.updateOptions) {
                    const tabSize = parseInt(options[nextIndex].split(':')[1]);
                    monacoEditor.updateOptions({
                        tabSize,
                        insertSpaces: options[nextIndex].startsWith('Spaces')
                    });
                }
            } else if (this.classList.contains('terminal-toggle-btn')) {
                // Toggle terminal visibility
                const executorFooter = document.querySelector('.executor-footer');
                if (executorFooter) {
                    if (executorFooter.classList.contains('hidden')) {
                        executorFooter.classList.remove('hidden');
                        localStorage.setItem('terminalState', 'open');
                    } else if (executorFooter.classList.contains('collapsed')) {
                        executorFooter.classList.remove('collapsed');
                        localStorage.setItem('terminalState', 'open');
                    } else {
                        executorFooter.classList.add('hidden');
                        localStorage.setItem('terminalState', 'hidden');
                    }
                }
            }
        });
    });

    // Update the language indicator based on current file
    const currentFile = workspace.files[activeFileId];
    if (currentFile && currentFile.language) {
        const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = currentFile.language.charAt(0).toUpperCase() + currentFile.language.slice(1);
        }
    }

    // Adjust status bar for screen size
    updateStatusBarResponsiveness();

    // Add window resize listener for responsive status bar
    window.addEventListener('resize', updateStatusBarResponsiveness);
}

// Function to update status bar based on screen width
function updateStatusBarResponsiveness() {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const width = window.innerWidth;

    // Very small screens - show only essential items
    if (width < 400) {
        statusBar.querySelectorAll('.status-item:not(.always-show-text)').forEach(item => {
            const text = item.querySelector('.status-item-text');
            if (text) text.style.display = 'none';
        });
    }
    // Small screens - show icons and some text
    else if (width < 600) {
        statusBar.querySelectorAll('.status-item.icon-only-on-small .status-item-text').forEach(text => {
            text.style.display = 'none';
        });
        statusBar.querySelectorAll('.status-item:not(.icon-only-on-small):not(.hide-on-small) .status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
    // Larger screens - show everything
    else {
        statusBar.querySelectorAll('.status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
}

function openFile(fileId) {
    const file = workspace.files[fileId];
    if (!file) return;

    // تحديث الملف النشط
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة إذا لم يكن موجودًا
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    const editorContainer = document.getElementById('editor-container');

    if (typeof monaco === 'undefined') {
        console.error('Monaco Editor not loaded. Please wait...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';
        setTimeout(() => openFile(fileId), 1000);
        return;
    }

    if (!monacoEditor) {
        try {
            const model = monaco.editor.createModel(
                file.content || '',
                file.language || 'plaintext'
            );

            // VS Code-like options
            monacoEditor = monaco.editor.create(editorContainer, {
                model: model,
                theme: 'vs-dark',
                automaticLayout: true,
                fontSize: 14,
                lineNumbers: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                renderLineHighlight: 'all',
                cursorBlinking: 'smooth',
                cursorSmoothCaretAnimation: true,
                smoothScrolling: true,
                wordWrap: 'on',
                formatOnPaste: true,
                formatOnType: true,
                suggest: {
                    showMethods: true,
                    showFunctions: true,
                    showConstructors: true,
                    showFields: true,
                    showVariables: true,
                    showClasses: true,
                    showStructs: true,
                    showInterfaces: true,
                    showModules: true,
                    showProperties: true,
                    showEvents: true,
                    showOperators: true,
                    showUnits: true,
                    showValues: true,
                    showConstants: true,
                    showEnums: true,
                    showEnumMembers: true,
                    showKeywords: true,
                    showWords: true,
                    showColors: true,
                    showFiles: true,
                    showReferences: true,
                    showFolders: true,
                    showTypeParameters: true,
                    showIssues: true,
                    showUsers: true,
                    showSnippets: true
                }
            });

            // Set direction to LTR for code
            if (monacoEditor.updateOptions) {
                monacoEditor.updateOptions({ direction: 'ltr' });
            }

            if (monacoEditor.getDomNode) {
                monacoEditor.getDomNode().style.direction = 'ltr';
            }

            // Update content when changed
            if (monacoEditor.onDidChangeModelContent) {
                // إزالة المستمعين السابقة لتجنب التكرار
                if (monacoEditor.getModel()._contentChangedHandler) {
                    monacoEditor.getModel()._contentChangedHandler.dispose();
                }

                monacoEditor.getModel()._contentChangedHandler = monacoEditor.onDidChangeModelContent(function () {
                    // تأكد من أن التغييرات تُحفظ فقط للملف النشط الحالي
                    if (activeFileId && workspace.files[activeFileId] && activeFileId === fileId) {
                        workspace.files[activeFileId].content = monacoEditor.getValue();
                        // حفظ التغييرات عند تعديل المحتوى
                        saveWorkspace();
                    }
                });
            }

            // Add cursor position tracking (VS Code status bar)
            monacoEditor.onDidChangeCursorPosition(function (e) {
                const statusItems = document.querySelectorAll('.status-items-right .status-item');
                if (statusItems.length > 0) {
                    statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                }
            });

            // Add quick action buttons (like VS Code)
            const quickActions = document.createElement('div');
            quickActions.className = 'quick-actions';
            quickActions.innerHTML = `
                        <div class="quick-action" title="Split Editor"><i class="fas fa-columns"></i></div>
                        <div class="quick-action" title="More Options"><i class="fas fa-ellipsis-v"></i></div>
                    `;
            editorContainer.appendChild(quickActions);

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    } else {
        try {
            // تحقق من وجود النماذج
            let model = null;
            const modelUri = monaco.Uri.parse('inmemory://' + fileId);

            // البحث عن النموذج الحالي
            const existingModels = monaco.editor.getModels();
            model = existingModels.find(m => m.uri && m.uri.toString() === modelUri.toString());

            // إنشاء نموذج جديد إذا لم يكن موجودًا
            if (!model) {
                try {
                    model = monaco.editor.createModel(
                        file.content || '',
                        file.language || 'plaintext',
                        modelUri
                    );
                } catch (e) {
                    console.warn('Error creating model, trying to reuse existing model:', e);
                    // محاولة استخدام النموذج الحالي للمحرر
                    model = monacoEditor.getModel();
                    if (model) {
                        model.setValue(file.content || '');
                        try {
                            if (file.language) {
                                monaco.editor.setModelLanguage(model, file.language);
                            }
                        } catch (langError) {
                            console.warn('Could not set language:', langError);
                        }
                    } else {
                        // إنشاء نموذج جديد بدون URI محدد
                        model = monaco.editor.createModel(file.content || '');
                    }
                }
            } else {
                // تحديث النموذج الموجود
                model.setValue(file.content || '');
                try {
                    if (file.language) {
                        monaco.editor.setModelLanguage(model, file.language);
                    }
                } catch (langError) {
                    console.warn('Could not set language:', langError);
                }
            }

            // تعيين النموذج للمحرر
            monacoEditor.setModel(model);

            // تحديث مؤشر اللغة في شريط الحالة
            try {
                if (file.language) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 3) {
                        statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                    }
                }
            } catch (e) {
                console.warn('Could not update language indicator:', e);
            }

            // تسجيل حدث تغيير المحتوى للحفظ التلقائي
            if (monacoEditor.onDidChangeModelContent) {
                // إزالة المستمعين السابقة لتجنب التكرار
                if (monacoEditor.getModel()._contentChangedHandler) {
                    monacoEditor.getModel()._contentChangedHandler.dispose();
                }

                monacoEditor.getModel()._contentChangedHandler = monacoEditor.onDidChangeModelContent(function () {
                    // تأكد من أن التغييرات تُحفظ فقط للملف النشط الحالي
                    if (activeFileId && workspace.files[activeFileId] && activeFileId === fileId) {
                        workspace.files[activeFileId].content = monacoEditor.getValue();
                        // حفظ التغييرات عند تعديل المحتوى
                        saveWorkspace();
                    }
                });
            }
        } catch (e) {
            console.error('Error changing editor model:', e);
        }
    }

    updateFileTabs();
    document.getElementById('code-executor').classList.add('visible');

    // Add VS Code status bar
    addStatusBar();

    // Focus editor after opening
    setTimeout(() => {
        if (monacoEditor && monacoEditor.focus) {
            try {
                monacoEditor.focus();
            } catch (e) {
                console.warn('Could not focus editor:', e);
            }
        }
    }, 100);
}

function renameFile(fileId, event) {
    event.stopPropagation();
    const file = workspace.files[fileId];
    if (!file) return;

    const newName = prompt('أدخل الاسم الجديد:', file.name);
    if (newName && newName !== file.name) {
        file.name = newName;

        // تحديث مسار الملف أيضًا
        const pathParts = file.path.split('/');
        pathParts.pop(); // إزالة اسم الملف القديم
        pathParts.push(newName); // إضافة اسم الملف الجديد
        file.path = pathParts.join('/');

        updateFileExplorer();

        // حفظ التغييرات مباشرة
        saveWorkspace();
    }
}

function deleteFile(itemId, event) {
    event.stopPropagation();
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;

    if (itemId.startsWith('folder_')) {
        const folderObj = Object.values(workspace.folders).find(f => f && f.id === itemId);
        if (folderObj) {
            // حذف جميع الملفات والمجلدات داخل هذا المجلد
            const folderPath = folderObj.path;

            // حذف الملفات داخل المجلد
            for (const fileId in workspace.files) {
                const file = workspace.files[fileId];
                if (file.path.startsWith(folderPath)) {
                    delete workspace.files[fileId];

                    // إزالة من قائمة الملفات المفتوحة
                    if (activeFileId === fileId) {
                        hideCodeExecutor();
                    }
                    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
                    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
                    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));
                }
            }

            // حذف المجلدات الفرعية
            for (const path in workspace.folders) {
                if (path !== folderPath && path.startsWith(folderPath)) {
                    delete workspace.folders[path];
                }
            }

            // حذف المجلد نفسه
            delete workspace.folders[folderPath];
        }
    } else {
        // إذا كان الملف المحذوف هو الملف المفتوح حاليًا، نغلق المحرر
        if (activeFileId === itemId) {
            hideCodeExecutor();
        }

        delete workspace.files[itemId];

        // حذف الملف من قائمة الملفات المفتوحة إذا كان موجودًا
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
        const updatedOpenFiles = openFiles.filter(id => id !== itemId);
        localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));
    }

    // إزالة العنصر من المجلد الأب مع التحقق
    for (const folder of Object.values(workspace.folders)) {
        if (folder && Array.isArray(folder.children)) {
            const index = folder.children.indexOf(itemId);
            if (index !== -1) {
                folder.children.splice(index, 1);
                break;
            }
        }
    }

    updateFileExplorer();
    updateFileTabs();

    // حفظ التغييرات مباشرة
    saveWorkspace();
}

function toggleExplorer() {
    const explorer = document.getElementById('file-explorer');
    const toggle = document.getElementById('explorer-toggle');

    explorer.classList.toggle('visible');
    toggle.classList.toggle('active');
}

function createNewFile() {
    const fileName = prompt('أدخل اسم الملف (مثال: index.html):');
    if (fileName) {
        const lang = fileName.split('.').pop();
        const fileId = createFile(fileName, '', lang, workspace.currentPath);
        openFile(fileId);
        updateFileExplorer();
    }
}

function createNewFolder() {
    const folderName = prompt('أدخل اسم المجلد:');
    if (folderName) {
        // تحقق من صحة اسم المجلد
        if (folderName.includes('/')) {
            alert('اسم المجلد لا يمكن أن يحتوي على الرمز "/"');
            return;
        }

        // إنشاء المسار الجديد
        let newPath = workspace.currentPath;
        if (!newPath.endsWith('/')) newPath += '/';
        newPath += folderName + '/';

        // تحقق من عدم وجود مجلد بنفس الاسم
        if (workspace.folders[newPath]) {
            alert('يوجد مجلد بنفس الاسم بالفعل!');
            return;
        }

        // إنشاء المجلد
        createFolder(newPath);

        // تحديث المستكشف
        updateFileExplorer();

        // الانتقال إلى المجلد الجديد
        navigateToFolder(newPath);
    }
}

function uploadFile() {
    document.getElementById('file-upload').click();
}

function handleFileUpload(event) {
    const files = event.target.files;
    if (!files.length) return;

    Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            const lang = file.name.split('.').pop();
            createFile(file.name, content, lang, workspace.currentPath);
            updateFileExplorer();
        };
        reader.readAsText(file);
    });
}

// وظائف لواجهة تشغيل الأكواد
function showCodeExecutor(codeBlock, suggestedName, lang, content) {
    const executor = document.getElementById('code-executor');
    const editorContainer = document.getElementById('editor-container');

    // Create file if it doesn't exist
    const fileId = createFile(suggestedName, content.trim(), lang || 'javascript');
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    if (typeof monaco === 'undefined') {
        console.log('Monaco Editor not loaded yet. Waiting...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';

        setTimeout(() => {
            showCodeExecutor(codeBlock, suggestedName, lang, content);
        }, 1000);
        return;
    }

    openFile(fileId);

    // Ensure terminal element exists
    let terminalContainer = document.querySelector('.executor-footer');
    if (!terminalContainer) {
        console.warn('Terminal container not found, creating it');
        terminalContainer = document.createElement('div');
        terminalContainer.className = 'executor-footer';
        executor.appendChild(terminalContainer);

        // Create terminal header
        const terminalHeader = document.createElement('div');
        terminalHeader.className = 'terminal-header';
        terminalHeader.innerHTML = `
            <span>النتائج</span>
            <button onclick="clearExecutorResult()">مسح</button>
        `;
        terminalContainer.appendChild(terminalHeader);

        // Create terminal output area
        const terminalOutput = document.createElement('div');
        terminalOutput.className = 'terminal';
        terminalOutput.id = 'executor-result';
        terminalContainer.appendChild(terminalOutput);
    }

    updateTerminalHeader();
    addStatusBar();

    // Update language indicator in status bar
    if (lang) {
        const langIndicator = document.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = lang.charAt(0).toUpperCase() + lang.slice(1);
        }
    }

    executor.classList.add('visible');
}

// تحسين وظائف التيرمنال
function updateTerminalHeader() {
    const terminalHeader = document.querySelector('.terminal-header');
    if (terminalHeader) {
        terminalHeader.innerHTML = `
                    <div class="terminal-tabs">
                <div class="terminal-tab active" data-tab="terminal">TERMINAL</div>
                <div class="terminal-tab" data-tab="output">OUTPUT</div>
                <div class="terminal-tab" data-tab="problems">PROBLEMS</div>
                <div class="terminal-tab" data-tab="debug">DEBUG CONSOLE</div>
                    </div>
                    <div class="terminal-actions">
                        <button title="Clear Terminal" onclick="clearExecutorResult()"><i class="fas fa-trash-alt"></i></button>
                <button title="Kill Terminal" onclick="killTerminal()"><i class="fas fa-times-circle"></i></button>
                        <button title="New Terminal" onclick="createNewTerminal()"><i class="fas fa-plus"></i></button>
                        <button title="Split Terminal" onclick="splitTerminal()"><i class="fas fa-columns"></i></button>
                        <button title="Toggle Terminal" onclick="toggleTerminal()"><i class="fas fa-chevron-down"></i></button>
                <button title="Maximize Terminal" onclick="maximizeTerminal()"><i class="fas fa-expand-alt"></i></button>
                <button title="More Options" onclick="showTerminalOptions(event)"><i class="fas fa-ellipsis-v"></i></button>
                <button title="Close Terminal" onclick="hideTerminal()"><i class="fas fa-times"></i></button>
                    </div>
                `;

        // جعل التبويبات قابلة للنقر
        const tabs = terminalHeader.querySelectorAll('.terminal-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                tabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // تحديث محتوى التيرمنال بناءً على التبويب النشط
                const tabName = this.dataset.tab;
                const terminalElem = document.getElementById('executor-result');

                switch(tabName) {
                    case 'problems':
                        terminalElem.innerHTML = '<div class="terminal-message success">No problems have been detected in the workspace.</div>';
                        break;
                    case 'output':
                        terminalElem.innerHTML = '<div class="terminal-message">Output channel is empty.</div>';
                        break;
                    case 'debug':
                        terminalElem.innerHTML = '<div class="terminal-message">Debug console is available in debug mode.</div>';
                        break;
                    case 'terminal':
                    default:
                        // استعادة محتوى التيرمنال الأصلي إذا كان موجوداً
                        if (window._terminalContent) {
                            terminalElem.innerHTML = window._terminalContent;
                        } else {
                            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal ready.</div>';
                        }
                }
            });
        });
    }
}

// حفظ محتوى التيرمنال قبل تغيير التبويب
function saveTerminalContent() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem && terminalElem.innerHTML.trim() !== '') {
        window._terminalContent = terminalElem.innerHTML;
    }
}

// إخفاء/إظهار التيرمنال بشكل كامل
function toggleTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        if (executorFooter.classList.contains('collapsed')) {
            // إذا كان مطوياً، نفتحه
            executorFooter.classList.remove('collapsed');
            localStorage.setItem('terminalState', 'open');
        } else if (executorFooter.classList.contains('hidden')) {
            // إذا كان مخفياً، نظهره
            executorFooter.classList.remove('hidden');
            localStorage.setItem('terminalState', 'open');
        } else {
            // إذا كان مفتوحاً، نطويه
            executorFooter.classList.add('collapsed');
            localStorage.setItem('terminalState', 'collapsed');
        }
    }
}

// إخفاء التيرمنال تماماً
function hideTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.add('hidden');
        localStorage.setItem('terminalState', 'hidden');
    }
}

// تكبير التيرمنال
function maximizeTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.toggle('maximized');
        if (executorFooter.classList.contains('maximized')) {
            localStorage.setItem('terminalState', 'maximized');
        } else {
            localStorage.setItem('terminalState', 'open');
        }
    }
}

// إيقاف عمليات التيرمنال
function killTerminal() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        saveTerminalContent();
        terminalElem.innerHTML += '<div class="terminal-message error">Process terminated.</div>';
    }
}

// استعادة حالة التيرمنال عند تحميل الصفحة
function restoreTerminalState() {
    const state = localStorage.getItem('terminalState') || 'open';
    const executorFooter = document.querySelector('.executor-footer');

    if (executorFooter) {
        // إزالة جميع الحالات أولاً
        executorFooter.classList.remove('collapsed', 'hidden', 'maximized');
        executorFooter.style.height = '';

        // تطبيق الحالة المحفوظة
        switch (state) {
            case 'collapsed':
                executorFooter.classList.add('collapsed');
                break;
            case 'hidden':
                executorFooter.classList.add('hidden');
                break;
            case 'maximized':
                executorFooter.classList.add('maximized');
                break;
            case 'custom':
                const savedHeight = localStorage.getItem('terminalHeight');
                if (savedHeight) {
                    executorFooter.style.height = savedHeight + 'px';
                }
                break;
        }
    }
}

// إضافة زر لفتح/إغلاق التيرمنال في شريط الحالة
function addTerminalToggleButton() {
    // تحقق مما إذا كان شريط الحالة موجوداً
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    // تحقق مما إذا كان الزر موجوداً بالفعل
    if (statusBar.querySelector('.terminal-toggle-btn')) return;

    // إنشاء زر التبديل
    const toggleBtn = document.createElement('div');
    toggleBtn.className = 'status-item terminal-toggle-btn';
    toggleBtn.innerHTML = '<i class="fas fa-terminal"></i> Terminal';
    toggleBtn.title = 'Toggle Terminal (Ctrl+`)';
    toggleBtn.onclick = function() {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else if (executorFooter.classList.contains('collapsed')) {
                executorFooter.classList.remove('collapsed');
                localStorage.setItem('terminalState', 'open');
            } else {
                executorFooter.classList.add('hidden');
                localStorage.setItem('terminalState', 'hidden');
            }
        }
    };

    // إضافة الزر إلى شريط الحالة
    const leftItems = statusBar.querySelector('.status-items-left');
    if (leftItems) {
        leftItems.appendChild(toggleBtn);
    } else {
        statusBar.appendChild(toggleBtn);
    }
}

// إضافة مقبض تغيير حجم التيرمنال
function addTerminalResizer() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // تحقق مما إذا كان المقبض موجوداً بالفعل
    if (executorFooter.querySelector('.terminal-resizer')) return;

    // إنشاء مقبض التغيير
    const resizer = document.createElement('div');
    resizer.className = 'terminal-resizer';
    executorFooter.appendChild(resizer);

    // تفعيل وظيفة السحب
    let startY, startHeight;

    resizer.addEventListener('mousedown', function(e) {
        startY = e.clientY;
        startHeight = parseInt(getComputedStyle(executorFooter).height);
        document.addEventListener('mousemove', doDrag, false);
        document.addEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = 'row-resize';
        e.preventDefault();
    });

    function doDrag(e) {
        // حساب الارتفاع الجديد (السحب لأعلى يقلل الارتفاع)
        const newHeight = startHeight - (e.clientY - startY);
        // تحديد حد أدنى وأقصى للارتفاع
        const minHeight = 100; // الحد الأدنى للارتفاع
        const maxHeight = window.innerHeight * 0.8; // 80% من ارتفاع النافذة

        if (newHeight > minHeight && newHeight < maxHeight) {
            executorFooter.style.height = newHeight + 'px';
            executorFooter.classList.remove('collapsed', 'maximized');
            localStorage.setItem('terminalHeight', newHeight);
            localStorage.setItem('terminalState', 'custom');
        }
    }

    function stopDrag() {
        document.removeEventListener('mousemove', doDrag, false);
        document.removeEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = '';
    }
}

// تحديث دالة openFile لإضافة مقبض تغيير الحجم
const originalOpenFile = openFile;
openFile = function(fileId) {
    originalOpenFile(fileId);

    // إضافة زر التيرمنال ومقبض تغيير الحجم بعد فتح الملف
    setTimeout(() => {
        addTerminalToggleButton();
        addTerminalResizer();
        restoreTerminalState();
        updateTerminalHeader();
    }, 200);
};

// تحديث دالة addStatusBar لتضيف زر التيرمنال
const originalAddStatusBar = addStatusBar;
addStatusBar = function() {
    originalAddStatusBar();
    addTerminalToggleButton();
};

// تعديل دالة clearExecutorResult للحفاظ على تنسيق التيرمنال
function clearExecutorResult() {
    const resultElement = document.getElementById('executor-result');
    if (resultElement) {
        resultElement.innerHTML = '<div class="terminal-welcome">Terminal cleared.</div>';
        window._terminalContent = resultElement.innerHTML;
    }
}

// إضافة اختصار لوحة المفاتيح للتيرمنال (Ctrl+`)
document.addEventListener('keydown', function(e) {
    // Ctrl+` لفتح/إغلاق التيرمنال
    if (e.ctrlKey && (e.key === '`' || e.key === 'Backquote')) {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else {
        executorFooter.classList.toggle('collapsed');
                localStorage.setItem('terminalState',
                    executorFooter.classList.contains('collapsed') ? 'collapsed' : 'open');
    }
            e.preventDefault();
}
    }
});

function updateFileTabs() {
    const tabsContainer = document.getElementById('file-tabs');
    tabsContainer.innerHTML = '';

    // تعديل: عرض جميع الملفات المفتوحة بدلاً من الملف النشط فقط
    // نحتفظ بقائمة من الملفات المفتوحة
    const openFiles = [];

    // إذا كان هناك ملف نشط، نضيفه أولاً
    if (activeFileId && workspace.files[activeFileId]) {
        openFiles.push(activeFileId);
    }

    // نضيف باقي الملفات المفتوحة من localStorage
    const savedOpenFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    savedOpenFiles.forEach(fileId => {
        if (workspace.files[fileId] && !openFiles.includes(fileId)) {
            openFiles.push(fileId);
        }
    });

    // عرض جميع الملفات المفتوحة كتبويبات
    openFiles.forEach(fileId => {
        const file = workspace.files[fileId];
        const tab = document.createElement('div');
        tab.className = `file-tab ${fileId === activeFileId ? 'active' : ''}`;

        // تحقق مما إذا كان المحرر مفتوحاً حاليًا
        const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

        tab.innerHTML = `
                    <span class="file-tab-name">${file.name}</span>
                    ${!isEditorVisible && fileId === activeFileId ?
                `<span class="file-tab-open" onclick="reopenEditor(event)" title="إعادة فتح المحرر">
                            <i class="fas fa-external-link-alt"></i>
                        </span>` : ''
            }
                    <span class="file-tab-close" onclick="closeFile('${file.id}', event)">×</span>
                `;
        tab.onclick = (e) => {
            if (!e.target.closest('.file-tab-close') && !e.target.closest('.file-tab-open')) {
                // تنشيط هذا الملف
                activeFileId = file.id;
                updateFileTabs();

                // إذا كان المحرر مغلقًا، نعيد فتحه
                if (!document.getElementById('code-executor').classList.contains('visible')) {
                    reopenEditor();
                } else {
                    // إذا كان المحرر مفتوحًا، نقوم بتحميل هذا الملف
                    openFile(file.id);
                }
            }
        };
        tabsContainer.appendChild(tab);
    });

    // حفظ الملفات المفتوحة في localStorage
    localStorage.setItem('openFiles', JSON.stringify(openFiles));

    // تمرير التبويب النشط إلى منطقة العرض
    setTimeout(() => {
        const activeTab = tabsContainer.querySelector('.file-tab.active');
        if (activeTab) {
            activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }, 100);

    // Call our addStatusBar function instead of duplicating code
    addStatusBar();
}

// دالة لإعادة فتح المحرر للملف النشط
function reopenEditor(event) {
    if (event) {
        event.stopPropagation();
    }

    if (activeFileId && workspace.files[activeFileId]) {
        const file = workspace.files[activeFileId];

        // إعادة فتح المحرر بالملف النشط
        document.getElementById('code-executor').classList.add('visible');

        // تحديث المحرر بالمحتوى الحالي
        if (monacoEditor) {
            try {
                // إيجاد أو إنشاء نموذج
                let model = null;
                const existingModels = monaco.editor.getModels();

                if (existingModels && existingModels.length > 0) {
                    model = existingModels.find(m => m.uri && m.uri.path === '/' + activeFileId);
                }

                if (!model) {
                    try {
                        model = monaco.editor.createModel(
                            file.content || '',
                            file.language || 'plaintext',
                            monaco.Uri.parse('inmemory://' + activeFileId)
                        );
                    } catch (e) {
                        console.error('Error creating model:', e);
                        model = monaco.editor.createModel(file.content || '');
                    }
                }

                // تعيين النموذج وتحديثه
                monacoEditor.setModel(model);
                monacoEditor.setValue(file.content || '');

                // محاولة تعيين اللغة
                try {
                    if (file.language && monaco.editor.setModelLanguage) {
                        monaco.editor.setModelLanguage(model, file.language);

                        // تحديث مؤشر اللغة في شريط الحالة
                        const statusItems = document.querySelectorAll('.status-items-right .status-item');
                        if (statusItems.length > 3) {
                            statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                        }
                    }
                } catch (e) {
                    console.warn('Could not set language:', e);
                }

                // تركيز المحرر
                setTimeout(() => {
                    if (monacoEditor && monacoEditor.focus) {
                        try {
                            monacoEditor.focus();
                        } catch (e) {
                            console.warn('Could not focus editor:', e);
                        }
                    }
                }, 100);
            } catch (e) {
                console.error('Error reopening editor:', e);
            }
        } else {
            // إذا لم يكن المحرر موجودًا، نستخدم openFile
            openFile(activeFileId);
        }

        // تحديث علامات التبويب
        updateFileTabs();
    }
}

// إضافة أنماط CSS للزر الجديد
document.addEventListener('DOMContentLoaded', function () {
    const style = document.createElement('style');
    style.textContent = `
                .file-tab-open {
                    margin-left: 8px;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 3px;
                    color: var(--text-dim);
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    cursor: pointer;
                }

                .file-tab:hover .file-tab-open {
                    opacity: 1;
                }

                .file-tab-open:hover {
                    color: white;
                    background: rgba(94, 53, 177, 0.5);
                }
            `;
    document.head.appendChild(style);
});

function closeFile(fileId, event) {
    if (event) {
        event.stopPropagation();
    }

    // إزالة الملف من قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));

    // إذا كان الملف المغلق هو الملف النشط
    if (activeFileId === fileId) {
        // إذا كان هناك ملفات مفتوحة أخرى، نجعل الملف الأول هو النشط
        if (updatedOpenFiles.length > 0) {
            activeFileId = updatedOpenFiles[0];

            // إذا كان المحرر مفتوحًا، نفتح الملف الجديد
            if (document.getElementById('code-executor').classList.contains('visible')) {
                openFile(activeFileId);
            }
        } else {
            // إذا لم يكن هناك ملفات مفتوحة أخرى، نغلق المحرر
            activeFileId = null;
            document.getElementById('code-executor').classList.remove('visible');

            // تنظيف iframe التنفيذ
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
                window._executorIframe = null;
            }
        }
    }

    updateFileTabs();
    updateFileExplorer();
}

function hideCodeExecutor() {
    document.getElementById('code-executor').classList.remove('visible');
    // لا نقوم بإلغاء تعيين activeFileId هنا لكي تبقى التبويبة نشطة
    // activeFileId = null;

    // تنظيف iframe التنفيذ
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    // تحديث مستكشف الملفات لإظهار الملف النشط في قسم المحررات المفتوحة
    updateFileExplorer();
}

async function runCodeInExecutor() {
    const resultElement = document.getElementById('executor-result');
    resultElement.textContent = 'جاري التشغيل...\n';

    // Debug: Check if the terminal element exists
    if (!resultElement) {
        console.error('Terminal element not found (executor-result)');
        return;
    }

    if (!activeFileId || !workspace.files[activeFileId]) {
        resultElement.textContent += 'لا يوجد ملف نشط للتنفيذ.\n';
        return;
    }
    const file = workspace.files[activeFileId];
    const lang = (file.language || '').toLowerCase();

    // Log debugging info
    console.log(`Executing file: ${file.name}, Language: ${lang}`);

    // دعم HTML: عرض النتيجة في نافذة جانبية (web-preview-sidebar)
    if (lang === 'html') {
        resultElement.textContent += 'جاري تحميل المعاينة...\n';

        // احصل على محتوى الملف
        const htmlContent = file.content;

        // تحقق من المصادر المرتبطة (CSS, JS, صور)
        const linkedFiles = findLinkedFiles(htmlContent);
        console.log('الملفات المرتبطة:', linkedFiles);

        // إنشاء خريطة للمصادر المضمنة
        const resourceMap = createResourceMap(linkedFiles);
        console.log('خريطة المصادر:', resourceMap);

        // استبدال روابط المصادر بالمحتوى المضمن أو البيانات المشفرة base64
        const processedHtml = replaceLinkedResources(htmlContent, resourceMap);

        // عرض نافذة المعاينة
        const sidebar = document.getElementById('web-preview-sidebar');
        const iframe = document.getElementById('web-preview-iframe');

        if (sidebar && iframe) {
            // حفظ إعدادات العرض الحالية إذا كانت النافذة مفتوحة
            let currentDeviceType = 'responsive';
            let wasVisible = sidebar.style.display !== 'none';

            if (wasVisible) {
                // حفظ إعدادات الجهاز الحالي
                const deviceSelector = document.getElementById('device-selector');
                if (deviceSelector) {
                    currentDeviceType = deviceSelector.value;
                }

                // حفظ موقع وحجم النافذة
                saveWebPreviewPosition();
            }

            // إظهار النافذة إذا كانت مخفية
            if (sidebar.style.display === 'none') {
                sidebar.style.display = 'flex';
            }

            // تهيئة نافذة المعاينة إذا لم تكن مهيأة بعد
            if (!sidebar.dataset.initialized) {
                initWebPreviewSidebar();
                sidebar.dataset.initialized = 'true';
            }

            // تعيين المحتوى في iframe
            iframe.setAttribute('srcdoc', processedHtml);

            // إضافة مستمع للأحداث لتطبيق إعدادات الجهاز بعد التحميل
            iframe.onload = () => {
                // استرجاع إعدادات الجهاز
                if (wasVisible) {
                    // استخدام نفس إعدادات الجهاز
        setTimeout(() => {
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            deviceSelector.value = currentDeviceType;
                        }
                        changeDeviceView(currentDeviceType);

                        // ضمان ظهور الإطار بالكامل
                        scrollDeviceFrameIntoView();
        }, 100);
                } else {
                    // استرجاع الإعدادات المحفوظة أو استخدام الافتراضية
                    const savedDeviceType = localStorage.getItem('webPreviewDeviceType') || 'responsive';
                    setTimeout(() => {
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            deviceSelector.value = savedDeviceType;
                        }
                        changeDeviceView(savedDeviceType);

                        // ضمان ظهور الإطار بالكامل
                        scrollDeviceFrameIntoView();
                    }, 100);
                }

                // إضافة خيارات للتفاعل مع iframe
                try {
                    // إضافة مستمع للنقرات على الروابط داخل iframe
                    iframe.contentDocument.addEventListener('click', (e) => {
                        const link = e.target.closest('a');
                        if (link && link.href) {
                            // منع الانتقال خارج iframe
                            e.preventDefault();

                            // عرض رسالة في التيرمنال
                            resultElement.textContent += `تم النقر على رابط: ${link.href}\n`;
                            resultElement.textContent += `(الروابط محظورة في وضع المعاينة للأمان)\n`;
                        }
                    });

                    // تعطيل وظائف التحذير والخطأ من داخل iframe
                    iframe.contentWindow.alert = (msg) => {
                        resultElement.textContent += `[تنبيه]: ${msg}\n`;
                    };

                    iframe.contentWindow.onerror = (msg, source, line, col, error) => {
                        resultElement.textContent += `[خطأ في المعاينة]: ${msg} (سطر ${line})\n`;
                        return true; // منع الظهور في وحدة التحكم
                    };

                    // استمع لرسائل console.log
                    const originalConsoleLog = iframe.contentWindow.console.log;
                    iframe.contentWindow.console.log = function() {
                        originalConsoleLog.apply(this, arguments);
                        const args = Array.from(arguments).join(' ');
                        resultElement.textContent += `[console.log]: ${args}\n`;
                    };
                } catch (e) {
                    console.warn('لا يمكن إضافة مستمعي الأحداث إلى iframe:', e);
                }
            };

            resultElement.textContent += 'تم فتح المعاينة!\n';
        } else {
            resultElement.textContent += 'خطأ: لا يمكن العثور على عناصر المعاينة.\n';
        }

        return;
    }

    // دعم جافاسكريبت
    if (["js", "javascript"].includes(lang)) {
        try {
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
            }
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            window._executorIframe = iframe;
            let context = {
                iframe,
                win: iframe.contentWindow,
                consoleOutput: []
            };

            // Override console methods in the iframe
            try {
            context.win.console.log = function (...args) {
                context.consoleOutput.push(args.join(' '));
                console.log(...args);
            };
            context.win.console.error = function (...args) {
                context.consoleOutput.push('ERROR: ' + args.join(' '));
                console.error(...args);
            };
            context.win.__files = {};
            for (const [id, f] of Object.entries(workspace.files)) {
                context.win.__files[f.name] = f.content;
            }
            } catch (e) {
                resultElement.textContent += `خطأ في تهيئة بيئة التنفيذ: ${e.message}\n`;
                console.error('Error setting up execution environment:', e);
                return;
            }

            codeContexts[currentConversationId] = context;
            context.consoleOutput = [];

            const wrappedCode = `
                        try {
                            function require(name) {
                                if (__files[name]) {
                                    const module = { exports: {} };
                                    const func = new Function('module', 'exports', __files[name]);
                                    func(module, module.exports);
                                    return module.exports;
                                }
                                throw new Error('Module not found: ' + name);
                            }
                            ${file.content}
                        } catch(e) {
                            console.error('Error:', e);
                        }
                    `;

            console.log('Executing JavaScript code');
            let result;
            try {
                result = context.win.eval(wrappedCode);
            } catch (e) {
                context.consoleOutput.push(`ERROR: ${e.message}`);
                console.error('Error during evaluation:', e);
            }

            if (result !== undefined) {
                context.consoleOutput.push(result.toString());
            }

            resultElement.textContent += context.consoleOutput.join('\n') || 'تم التنفيذ بنجاح بدون إخراج.\n';
        } catch (e) {
            resultElement.textContent += `خطأ: ${e.message}\n`;
            console.error('Error executing code:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // دعم بايثون
    if (lang === 'python' || lang === 'py') {
        try {
            resultElement.textContent = 'جاري تحميل بايثون (Pyodide)...\n';

            // Check if Pyodide is available
            if (typeof loadPyodide !== 'function') {
                resultElement.textContent += 'خطأ: مكتبة Pyodide غير متوفرة! تأكد من تضمين الملف في الصفحة.\n';
                console.error('Pyodide library not available. Make sure to include the script.');
                return;
            }

            if (!window.pyodide) {
                try {
                window.pyodide = await loadPyodide();
                } catch (e) {
                    resultElement.textContent += `خطأ في تحميل Pyodide: ${e.message}\n`;
                    console.error('Error loading Pyodide:', e);
                    return;
            }
            }

            resultElement.textContent = 'جاري تنفيذ كود بايثون...\n';
            let output = '';

            try {
            window.pyodide.setStdout({
                batched: (s) => { output += s; }
            });
            window.pyodide.setStderr({
                batched: (s) => { output += s; }
            });
            } catch (e) {
                resultElement.textContent += `خطأ في تهيئة المخرجات: ${e.message}\n`;
                console.error('Error setting up stdout/stderr:', e);
            }

            try {
            await window.pyodide.runPythonAsync(file.content);
            resultElement.textContent += output || 'تم التنفيذ بنجاح بدون إخراج.\n';
        } catch (e) {
            resultElement.textContent += `خطأ بايثون: ${e.message}\n`;
                console.error('Python execution error:', e);
            }
        } catch (e) {
            resultElement.textContent += `خطأ: ${e.message}\n`;
            console.error('General error in Python execution:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // لغة غير مدعومة
    resultElement.textContent += `اللغة "${lang}" غير مدعومة. تنفيذ الكود متاح فقط لجافاسكريبت، بايثون، وHTML حالياً.\n`;
}

function clearExecutorResult() {
    document.getElementById('executor-result').textContent = '';
}

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function (e) {
    const executor = document.getElementById('code-executor');
    if (executor.classList.contains('visible')) {
        if (e.key === 'Escape') {
            hideCodeExecutor(); // فقط إخفاء النافذة دون إغلاق التبويب
            e.preventDefault();
        }

        if (e.key === 'F5') {
            runCodeInExecutor();
            e.preventDefault();
        }
    }

    // اختصار لإرسال الرسالة
    if (e.key === 'Enter' && !e.shiftKey && document.activeElement.id === 'chat-input') {
        e.preventDefault();
        sendMessage();
    }
});

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const menuToggle = document.getElementById('menu-toggle');

    sidebar.classList.toggle('visible');
    menuToggle.classList.toggle('active');

    // إزالة أي تأثير على المحتوى الرئيسي
    const mainContent = document.getElementById('main-content');
    mainContent.classList.remove('sidebar-visible');
}

function createNewChat() {
    currentConversationId = Date.now().toString();
    const newConversation = {
        id: currentConversationId,
        title: 'محادثة جديدة',
        messages: [],
        timestamp: new Date().toISOString()
    };

    conversations.unshift(newConversation);
    saveConversations();
    renderConversations();
    loadConversation(currentConversationId);
    toggleSidebar();
}

// تحديث دالة saveConversations لتحفظ مساحة العمل بشكل منفصل
function saveConversations() {
    localStorage.setItem('conversations', JSON.stringify(conversations));
    saveWorkspace();
}

// دالة جديدة لحفظ مساحة العمل بشكل منفصل
function saveWorkspace() {
    localStorage.setItem('workspace', JSON.stringify(workspace));
    console.log('تم حفظ مساحة العمل:', Object.keys(workspace.files).length, 'ملفات');
}

function loadConversations() {
    const saved = localStorage.getItem('conversations');
    if (saved) {
        conversations = JSON.parse(saved);
        // فرز المحادثات حسب التاريخ من الأحدث
        conversations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        renderConversations();

        // إذا كان هناك محادثات، نفتح الأخيرة
        if (conversations.length > 0) {
            currentConversationId = conversations[0].id;
            loadConversation(currentConversationId);
        }
    }

    // تحميل مساحة العمل
    const savedWorkspace = localStorage.getItem('workspace');
    if (savedWorkspace) {
        workspace = JSON.parse(savedWorkspace);

        // استعادة آخر مسار تم زيارته
        const lastPath = localStorage.getItem('currentPath');
        if (lastPath) {
            workspace.currentPath = lastPath;
        }
    } else {
        // إنشاء هيكل افتراضي للمشروع فقط إذا لم يكن هناك مساحة عمل محفوظة
        createDefaultWorkspace();
    }

    updateFileExplorer();
}

// دالة جديدة لإنشاء هيكل افتراضي للمشروع
function createDefaultWorkspace() {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
}

function renderConversations() {
    const list = document.getElementById('conversations-list');
    list.innerHTML = '';

    conversations.forEach(conv => {
        // إضافة أيقونة للرسائل الغنية
        const hasRichContent = conv.messages.some(m => m.content.includes('<div class="code-block">'));
        const icon = hasRichContent ? '📄' : '💬';
        const item = document.createElement('div');
        item.className = 'conversation-item';
        item.innerHTML = `
            <div class="conv-title">${icon} ${conv.title}</div>
            <div class="conversation-actions">
                <button class="conversation-action" onclick="deleteConversation('${conv.id}', event)">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="conversation-action" onclick="shareConversation('${conv.id}', event)">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        `;
        item.addEventListener('click', (e) => {
            if (!e.target.closest('.conversation-action')) {
                loadConversation(conv.id);
            }
        });
        list.appendChild(item);
    });
}

function deleteConversation(id, event) {
    event.stopPropagation();
    if (confirm('هل تريد حذف هذه المحادثة بشكل دائم؟')) {
        conversations = conversations.filter(conv => conv.id !== id);
        if (currentConversationId === id) {
            currentConversationId = null;
            document.getElementById('chat-window').innerHTML = '';
        }
        saveConversations();
        renderConversations();
    }
}

function shareConversation(id, event) {
    event.stopPropagation();
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        const shareContent = conversation.messages
            .map(m => `${m.role === 'user' ? 'أنت' : 'المساعد'}: ${m.content}`)
            .join('\n\n');

        if (navigator.share) {
            navigator.share({
                title: `محادثة: ${conversation.title}`,
                text: shareContent,
                url: window.location.href
            });
        } else {
            const textArea = document.createElement('textarea');
            textArea.value = shareContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('تم نسخ المحادثة إلى الحافظة!');
        }
    }
}

function loadConversation(id) {
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        currentConversationId = id;
        const chatWindow = document.getElementById('chat-window');
        chatWindow.innerHTML = '';
        conversation.messages.forEach(msg => {
            const div = document.createElement('div');
            div.className = `message ${msg.role}`;

            if (msg.role === 'user') {
                div.innerHTML = `<div class="message-content">${DOMPurify.sanitize(msg.content, { KEEP_CONTENT: true })}</div>`;
            } else {
                div.innerHTML = msg.content;

                setTimeout(() => {
                    Prism.highlightAllUnder(div);
                    MathJax.typeset([div]);
                    div.querySelectorAll('.copy-button').forEach(btn => {
                        btn.onclick = () => copyCode(btn);
                    });
                    div.querySelectorAll('.run-code-btn').forEach(btn => {
                        btn.onclick = function (e) {
                            e.stopPropagation();
                            const codeBlock = this.closest('.code-block');
                            const lang = codeBlock.querySelector('.language-label').textContent.trim();
                            const content = codeBlock.querySelector('code').textContent;
                            const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                                content.match(/#\s*file:\s*([^\n]+)/i);
                            const fileName = fileNameMatch && fileNameMatch[1] ?
                                fileNameMatch[1].trim() :
                                `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                            showCodeExecutor(codeBlock, fileName, lang, content);
                        };
                    });
                    fixNestedLists();
                }, 100);
            }

            chatWindow.appendChild(div);
        });
    }
}

function showErrorAnimation() {
    const menuToggle = document.getElementById('menu-toggle');
    menuToggle.classList.add('error');
    setTimeout(() => menuToggle.classList.remove('error'), 400);
}

function fixNestedLists() {
    document.querySelectorAll('.message').forEach(message => {
        let lists = message.querySelectorAll('ol, ul');

        lists.forEach(list => {
            list.querySelectorAll('li').forEach(li => {
                if (li.children.length === 0 ||
                    Array.from(li.children).every(child => child.nodeName === 'SPAN' || child.nodeName === 'EM' || child.nodeName === 'STRONG')) {
                } else {
                    let newElements = [];
                    Array.from(li.childNodes).forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE &&
                            !['OL', 'UL', 'LI', 'SPAN', 'EM', 'STRONG', 'A', 'CODE'].includes(node.nodeName)) {
                            newElements.push(node);
                        }
                    });

                    if (newElements.length > 0) {
                        const parent = li.parentNode;
                        newElements.forEach(el => {
                            parent.insertBefore(el, li.nextSibling);
                        });
                    }
                }
            });
        });

        // إصلاح القوائم المرقمة المكسورة
        message.querySelectorAll('ol').forEach(ol => {
            if (!ol.hasAttribute('data-fixed')) {
                const items = ol.innerHTML.split('</li>');
                if (items.length > 1) {
                    ol.innerHTML = items.map(item => {
                        if (item.trim() && !item.includes('<ol') && !item.includes('<ul')) {
                            return item + '</li>';
                        }
                        return item;
                    }).join('');
                }
                ol.setAttribute('data-fixed', 'true');
            }
        });
    });
}

////////////////////////////////////////////////////////////////////////////////
const promptContent = document.getElementById("chat-input");
const maxH = 80;
promptContent.addEventListener('input', function () {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
    if (this.scrollHeight <= maxH) {
        this.style.overflowY = 'hidden';
    } else {
        this.style.height = maxH + 'px';
        this.style.overflowY = 'auto';
    }
});

function sanitizeText(text) {
    return text;
}

function escapeHtml(text) {
    return text
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;");
}

function toggleThink(header) {
    const container = header.parentElement;
    const content = container.querySelector('.think-content');
    const toggle = container.querySelector('.think-toggle');

    const isOpen = content.style.display !== 'none';

    content.dataset.state = isOpen ? 'closed' : 'open';
    container.dataset.state = isOpen ? 'closed' : 'open';

    toggle.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(90deg)';

    content.style.display = isOpen ? 'none' : 'block';

    if (isOpen) {
        content.style.animation = 'think-close 0.3s ease forwards';
    } else {
        content.style.animation = 'think-open 0.3s ease forwards';

        setTimeout(() => {
            const codeBlocks = content.querySelectorAll('pre code');
            if (codeBlocks.length > 0) {
                Prism.highlightAllUnder(content);
            }

            if (content.querySelector('.math-inline, .math-block')) {
                MathJax.typeset([content]);
            }
        }, 300);
    }
}

function renderMarkdown(text) {
    const thinkProcess = [];
    const thinkRegex = /^&lt;think&gt;([\s\S]*?)&lt;\/think&gt;/gm;
    codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    inlineCodeRegex = /`([^`]+)`/g;

    const mathStore = [];
    const mathBlockRegex = /\\?(\$\$)([^\$]*?[^\\])(\\?\$\$)/g;
    const mathInlineRegex = /\\?(\$)(?!\$)([^\$]*?[^\\])(\\?\$)/g;
    const codeStore = [];

    text = text
        .replace(codeBlockRegex, (match, lang, content) => {
            codeStore.push({ type: 'block', lang, content });
            return `@@CODE_B_${codeStore.length - 1}@@`;
        });

    text = text
        .replace(mathBlockRegex, (_, o, c, cl) => {
            mathStore.push({ type: 'block', content: c });
            return `@@MATH_B_${mathStore.length - 1}@@`;
        })
        .replace(mathInlineRegex, (_, o, c, cl) => {
            mathStore.push({ type: 'inline', content: c });
            return `@@MATH_I_${mathStore.length - 1}@@`;
        });

    text = escapeHtml(text);
    text = DOMPurify.sanitize(text, { ALLOWED_TAGS: [], KEEP_CONTENT: true });

    text = text.replace(/@@CODE_B_(\d+)@@/g, (_, id) => {
        const { lang, content } = codeStore[id];

        // استخراج اسم الملف من التعليق المحسن
        let fileName = `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
        let filePath = '/';
        const fileMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
            content.match(/#\s*file:\s*([^\n]+)/i);

        if (fileMatch && fileMatch[1]) {
            const fullPath = fileMatch[1].trim();
            const pathParts = fullPath.split('/');
            fileName = pathParts.pop();
            filePath = pathParts.join('/') + '/';

            // تنظيف المسار إذا كان يبدأ بـ /
            if (filePath.startsWith('/')) {
                filePath = filePath.substring(1);
            }
            if (!filePath.endsWith('/')) {
                filePath += '/';
            }
        }

        // إنشاء الملف مع المسار الصحيح
        const fileId = createFile(fileName, content.trim(), lang || 'javascript', filePath);

        // إضافة زر التشغيل
        const runButton = (lang === 'javascript' || lang === 'js' || lang === 'python')
            ? ``
            : '';

        // إضافة زر فتح في المحرر
        const openButton = `<button class="open-code-btn">
                    <i class="fas fa-code"></i> فتح في المحرر
                </button>`;

        // تخزين المعلومات في data attributes لاستخدامها لاحقًا
        const dataAttrs = `data-filename="${fileName}" data-lang="${lang || 'javascript'}" data-file-id="${fileId}"`;

        let codeDiv = `<div class="code-block" ${dataAttrs}>`;
        codeDiv += `<div class="code-header">`;
        codeDiv += `<span class="language-label">${lang || 'bash'}</span>`;
        codeDiv += `<button class="copy-button" onclick="copyCode(this)">نسخ</button>`;
        codeDiv += openButton;
        codeDiv += runButton;
        codeDiv += `</div>`;
        codeDiv += `<div class="code-content">`;
        codeDiv += `<pre><code class="language-${lang || 'bash'} line-numbers">${content.trim()}</code></pre>`;
        codeDiv += `</div>`;
        codeDiv += `</div>`;
        return codeDiv;
    });

    text = text.replace(inlineCodeRegex, function (match, codeContent) {
        return `<code class="inline-code">${codeContent}</code>`;
    });

    text = text.replace(thinkRegex, function (match, thinkContent) {
        let processedContent = renderMarkdown(thinkContent);
        processedContent = DOMPurify.sanitize(processedContent, {
            ALLOWED_TAGS: ['div', 'span', 'p', 'br', 'b', 'i', 'strong', 'em', 'code', 'pre', 'ul', 'ol', 'li', 'a', 'hr'],
            ALLOWED_ATTR: ['class', 'style', 'href', 'target', 'rel', 'data-animated', 'data-number', 'data-level', 'data-icon', 'data-enhanced'],
        });

        const formattedContent = processedContent.split('\n').map(line => {
            line = line.trim();
            if (!line) return '';
            if (line.startsWith('---')) {
                return '<div class="think-divider"></div>';
            }
            return `<div class="think-step">${line}</div>`;
        }).filter(line => line).join('');

        return `
    <div class="think-container">
        <div class="think-header" onclick="toggleThink(this)">
            <div class="think-toggle">▼</div>
            <div class="think-title">Thinking Process</div>
        </div>
        <div class="think-content">
            ${formattedContent}
        </div>
    </div>`;
    });

    const markdownProcessors = [
        {
            regex: /^(#{1,6})\s(.+?)(?:\n|$)/gm,
            handler: (_, hashes, content) =>
                `<h${hashes.length}>${content}</h${hashes.length}>`
        },
        {
            regex: /\*\*(.+?)\*\*/g,
            handler: (_, content) => `<b>${content}</b>`
        },
        {
            regex: /\*(.+?)\*/g,
            handler: (_, content) => `<em>${content}</em>`
        },
        {
            regex: /^[-*_]{3,}$/gm,
            handler: () => '<hr class="divider">'
        },
        {
            regex: /^(\d+)\.\s(.+)$/gm,
            handler: (match, num, content) => {
                return `<ol><li>${content}</li></ol>`;
            }
        },
        {
            regex: /^[-*+]\s(.+)$/gm,
            handler: (match, content) => {
                return `<ul><li>${content}</li></ul>`;
            }
        },
        {
            regex: /^>\s(.+)/gm,
            handler: (_, content) => `<blockquote class="blockquote">${content}</blockquote>`
        },
        {
            regex: /\[([^\]]+)\]\(([^)]+)\)/g,
            handler: (_, text, url) => `<a href="${url}" class="link">${text}</a>`
        },
        {
            regex: /!\[([^\]]+)\]\(([^)]+)\)/g,
            handler: (_, alt, src) => `<img src="${src}" alt="${alt}" class="image">`
        },
        {
            regex: /^(\|?[^\n]+\|)(\n\s*\|?[ :-]+\|?[^\n]*)(\n(\|?[^\n]+\|)(\n\|?[^\n]+\|)*)+/gm,
            handler: (_, headers, alignLine, rows) => {
                const alignments = alignLine.split('|')
                    .slice(1, -1)
                    .map(col => {
                        if (/^:-+:$/.test(col)) return 'center';
                        if (/^:-+/.test(col)) return 'left';
                        if (/-+:$/.test(col)) return 'right';
                        return 'left';
                    });

                let html = `
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  ${headers.split('|').slice(1, -1).map((h, i) => `
                    <th style="text-align:${alignments[i]}">${h.trim()}</th>
                  `).join('')}
                </tr>
              </thead>
              <tbody>
                ${rows.trim().split('\n').map(row => `
                  <tr>
                    ${row.split('|').slice(1, -1).map((cell, i) => `
                      <td style="text-align:${alignments[i]}">${cell.trim()}</td>
                    `).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>`;

                return html;
            }
        }
    ];

    markdownProcessors.forEach(({ regex, handler }) => {
        text = text.replace(regex, handler);
    });

    text = text
        .replace(/^@@MATH_B_(\d+)@@/gm, (_, id) =>
            `<div class="math-block">\\[${mathStore[id].content}\\]</div>`)
        .replace(/^@@MATH_I_(\d+)@@/gm, (_, id) =>
            `<span class="math-inline">\\(${mathStore[id].content}\\)</span>`);

    // إصلاح القوائم المتداخلة
    text = text.replace(/<li>([^<]+)<(ul|ol)/g, '<li>$1</li><$2');

    // إغلاق أي قوائم مفتوحة في نهاية النص
    if (text.indexOf('<ol') !== -1 && text.lastIndexOf('<ol') > text.lastIndexOf('</ol>')) {
        text += '</ol>';
    }
    if (text.indexOf('<ul') !== -1 && text.lastIndexOf('<ul') > text.lastIndexOf('</ul>')) {
        text += '</ul>';
    }

    return text
        .replace(/\\\{/g, '{')
        .replace(/\\\}/g, '}')
        .replace(/\\times/g, '×')
        .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '\\frac{$1}{$2}');
}

async function typeRegularMessage(text, elem) {
    let currentText = "";
    const speed = 1;
    for (let i = 0; i < text.length; i++) {
        currentText += text[i];
        elem.innerHTML = DOMPurify.sanitize(renderMarkdown(currentText), { KEEP_CONTENT: true });
        await new Promise(resolve => setTimeout(resolve, speed));
    }
    Prism.highlightAllUnder(elem);
    MathJax.typeset();
    fixNestedLists();
}

async function typeCodeBlock(content, lang, container) {
    // استخراج اسم الملف من التعليقات إن وجد
    let fileName = `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
    let filePath = '/';
    const fileMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
        content.match(/#\s*file:\s*([^\n]+)/i);

    if (fileMatch && fileMatch[1]) {
        const fullPath = fileMatch[1].trim();
        const pathParts = fullPath.split('/');
        fileName = pathParts.pop();
        filePath = pathParts.join('/') + '/';

        // تنظيف المسار إذا كان يبدأ بـ /
        if (filePath.startsWith('/')) {
            filePath = filePath.substring(1);
        }
        if (!filePath.endsWith('/')) {
            filePath += '/';
        }
    }

    // إنشاء الملف فقط دون فتحه تلقائيًا
    const fileId = createFile(fileName, content.trim(), lang || 'javascript', filePath);

    // إضافة زر التشغيل إذا كانت اللغة مدعومة
    const runButton = (lang === 'javascript' || lang === 'js' || lang === 'python')
        ? `<button class="run-code-btn">
                      <i class="fas fa-play"></i> تشغيل الكود
                   </button>`
        : '';

    // إضافة زر فتح في المحرر
    const openButton = `<button class="open-code-btn">
                <i class="fas fa-code"></i> فتح في المحرر
            </button>`;

    let codeDiv = `<div class="code-block" data-file-id="${fileId}">`;
    codeDiv += `<div class="code-header">`;
    codeDiv += `<span class="language-label">${lang || 'bash'}</span>`;
    codeDiv += `<button class="copy-button" onclick="copyCode(this)">نسخ</button>`;
    codeDiv += openButton;
    codeDiv += runButton;
    codeDiv += `</div>`;
    codeDiv += `<div class="code-content">`;
    codeDiv += `<pre><code class="language-${lang || 'bash'} line-numbers"></code></pre>`;
    codeDiv += `</div>`;
    codeDiv += `</div>`;

    container.innerHTML += codeDiv;

    let codeElement = container.querySelector('.code-block:last-child code');

    // اضافة استجابة لزر التشغيل
    let runBtn = container.querySelector('.code-block:last-child .run-code-btn');
    if (runBtn) {
        runBtn.onclick = function (e) {
            e.stopPropagation();
            const codeBlock = this.closest('.code-block');
            const langVal = codeBlock.querySelector('.language-label').textContent.trim();
            const contentVal = codeBlock.querySelector('code').textContent;
            showCodeExecutor(codeBlock, fileName, langVal, contentVal);
        };
    }

    // اضافة استجابة لزر فتح في المحرر
    let openBtn = container.querySelector('.code-block:last-child .open-code-btn');
    if (openBtn) {
        openBtn.onclick = function (e) {
            e.stopPropagation();
            const codeBlock = this.closest('.code-block');
            const fileId = codeBlock.getAttribute('data-file-id');
            if (fileId) {
                openFile(fileId);
            }
        };
    }

    let currentText = "";
    const speed = 1;
    for (let i = 0; i < content.length; i++) {
        currentText += content[i];
        codeElement.textContent = currentText;
        await new Promise(resolve => setTimeout(resolve, speed));
    }
    Prism.highlightElement(codeElement);
}

async function typeMessage(message, container) {
    const thinkTagRegex = /<think>([\s\S]+?)<\/think>/g;
    let parts = [];
    let lastIndex = 0, match;
    while ((match = thinkTagRegex.exec(message)) !== null) {
        if (match.index > lastIndex) {
            parts.push({ type: 'normal', content: message.substring(lastIndex, match.index) });
        }
        parts.push({ type: 'think', content: match[1] });
        lastIndex = thinkTagRegex.lastIndex;
    }
    if (lastIndex < message.length) {
        parts.push({ type: 'normal', content: message.substring(lastIndex) });
    }

    for (const part of parts) {
        if (part.type === 'normal') {
            const codeRegex = /```(\w+)?\n([\s\S]*?)```/g;
            let idx = 0, codeMatch;
            while ((codeMatch = codeRegex.exec(part.content)) !== null) {
                if (codeMatch.index > idx) {
                    const normalText = part.content.substring(idx, codeMatch.index);
                    const p = document.createElement('p');
                    container.appendChild(p);
                    await typeRegularMessage(normalText, p);
                }
                await typeCodeBlock(codeMatch[2], codeMatch[1] || 'bash', container);
                idx = codeRegex.lastIndex;
            }
            if (idx < part.content.length) {
                const remaining = part.content.substring(idx);
                const p = document.createElement('p');
                container.appendChild(p);
                await typeRegularMessage(remaining, p);
            }
        } else if (part.type === 'think') {
            const thinkBlock = document.createElement("div");
            thinkBlock.className = "think-container";
            thinkBlock.dataset.state = "open";

            thinkBlock.innerHTML = `
    <div class="think-header" onclick="toggleThink(this)">
        <div class="think-toggle">▼</div>
        <div class="think-title">Thinking Process</div>
    </div>
    <div class="think-content" style="display: block;"></div>
`;
            container.appendChild(thinkBlock);

            const thinkContentContainer = thinkBlock.querySelector(".think-content");

            const cleanedContent = part.content
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .trim();

            await typeRegularMessage(cleanedContent, thinkContentContainer);

            setTimeout(() => {
                const steps = thinkContentContainer.innerHTML.split('\n');
                let formattedContent = '';

                steps.forEach(step => {
                    step = step.trim();
                    if (!step) return;

                    if (step.startsWith('---')) {
                        formattedContent += '<div class="think-divider"></div>';
                    } else {
                        formattedContent += `<div class="think-step">${step}</div>`;
                    }
                });

                thinkContentContainer.innerHTML = formattedContent;

                Prism.highlightAllUnder(thinkContentContainer);
                MathJax.typeset([thinkContentContainer]);
            }, 100);
        }
    }
    setTimeout(() => {
        Prism.highlightAllUnder(container);
        MathJax.typeset([container]);
        container.querySelectorAll('.copy-button').forEach(btn => {
            btn.onclick = () => copyCode(btn);
        });

        // تحديث سلوك أزرار التشغيل والفتح في المحرر
        container.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });

        // اضافة استجابة لزر فتح في المحرر
        container.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        fixNestedLists();
    }, 1);
}

function createMessageDiv(content, role) {
    const div = document.createElement('div');
    div.className = `message ${role}`;
    return div;
}

function createTypingIndicator() {
    const div = document.createElement('div');
    div.className = 'message bot typing-indicator';
    div.innerHTML = `
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            `;
    return div;
}


function hideTypingIndicator() {
    var typingIndicator = document.getElementById("typing-indicator");
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const codeContent = codeBlock.querySelector('code').innerText;
    navigator.clipboard.writeText(codeContent).then(() => {
        const originalText = button.textContent;
        button.textContent = 'تم النسخ!';
        setTimeout(() => button.textContent = originalText, 2000);
    });
}

async function queryGroqAI(userInput) {
    const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${"********************************************************"}`,
        },
        body: JSON.stringify({
            model: "deepseek-r1-distill-llama-70b",
            messages: [
                {
                    role: "system",
                    content:
                    `أنت مساعد ذكي متخصص في تصميم وتطوير واجهات المستخدم (UI/UX). مهامك الأساسية:

                    ## تخصصك الأساسي:
                    1. **تصميم واجهات المستخدم**: إنشاء تصاميم حديثة وجذابة
                    2. **تجربة المستخدم**: تحسين سهولة الاستخدام والتفاعل
                    3. **التصميم التجاوبي**: ضمان عمل التصميم على جميع الأجهزة
                    4. **إمكانية الوصول**: جعل التصميم متاحاً للجميع
                    5. **الأداء**: تحسين سرعة التحميل والأداء

                    ## عند إنشاء أي كود:
                    - أضف تعليقًا في السطر الأول بالصيغة: // file: [مسار/اسم_ملف.لغة]
                    - استخدم أحدث معايير HTML5, CSS3, JavaScript ES6+
                    - طبق مبادئ التصميم الحديث (Material Design, Fluent Design)
                    - اهتم بالألوان والخطوط والتباعد
                    - استخدم CSS Grid و Flexbox للتخطيط
                    - أضف تأثيرات وانتقالات سلسة
                    - تأكد من التجاوب مع جميع أحجام الشاشات

                    ## أدوات مجانية ستستخدمها:
                    - CSS Frameworks: Bootstrap, Tailwind CSS
                    - Icons: Font Awesome, Feather Icons
                    - Fonts: Google Fonts
                    - Colors: Coolors.co, Adobe Color
                    - Images: Unsplash, Pexels
                    - Animations: Animate.css, AOS

                    ## نصائح التصميم:
                    - استخدم نظام ألوان متناسق
                    - اهتم بالتسلسل الهرمي البصري
                    - استخدم المساحات البيضاء بفعالية
                    - اجعل التصميم بديهي وسهل الاستخدام
                    - اختبر التصميم على أجهزة مختلفة`
                },
                {
                    role: "user",
                    content: userInput
                }],
            temperature: 0.3,
            top_p: 0.9,
            frequency_penalty: 0.2
        }),
    });
    const data = await response.json();
    return data.choices[0].message.content;
}

async function sendMessage() {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;

    const chatWindow = document.getElementById("chat-window");

    if (!currentConversationId) {
        currentConversationId = Date.now().toString();
        conversations.unshift({
            id: currentConversationId,
            title: message.substring(0, 20),
            messages: [],
            timestamp: new Date().toISOString()
        });
        renderConversations();
    }

    const userDiv = document.createElement("div");
    userDiv.className = "message user";
    userDiv.innerHTML = `<div class="message-content">${DOMPurify.sanitize(message, { KEEP_CONTENT: true })}</div>`;
    chatWindow.appendChild(userDiv);

    const currentConv = conversations.find(c => c.id === currentConversationId);
    if (currentConv) {
        currentConv.messages.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });
    }

    userDiv.scrollIntoView({ behavior: "smooth" });
    inputElem.value = "";
    inputElem.style.height = "40px";

    const typingDiv = document.createElement("div");
    typingDiv.className = "message bot typing-indicator";
    typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
    chatWindow.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: "smooth" });

    try {
        const botResponse = await queryGroqAI(message);
        typingDiv.remove();

        const botDiv = document.createElement("div");
        botDiv.className = "message bot";
        chatWindow.appendChild(botDiv);
        await typeMessage(botResponse, botDiv);

        if (currentConv) {
            currentConv.messages.push({
                role: 'bot',
                content: botDiv.innerHTML,
                timestamp: new Date().toISOString()
            });
            currentConv.title = message.substring(0, 20);
        }

        saveConversations();
        renderConversations();
        botDiv.scrollIntoView({ behavior: "smooth" });

    } catch (error) {
        typingDiv.remove();
        console.error("Error:", error);

        if (currentConv) {
            currentConv.messages = currentConv.messages.filter(m => m.content !== message);
        }

        showErrorMessage();
    }
    smartScroll(chatWindow);
}

function smartScroll(element) {
    const threshold = 100;
    const isNearBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + threshold;

    if (isNearBottom) {
        element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
        });
    }
}

window.addEventListener('resize', () => {
    const activeElement = document.activeElement;
    if (activeElement.tagName === 'TEXTAREA') {
        smartScroll(document.getElementById('chat-window'));
    }
});

function showErrorMessage() {
    const chatWindow = document.getElementById('chat-window');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message bot error';
    errorDiv.innerHTML = `<div class="message-content">عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.</div>`;
    chatWindow.appendChild(errorDiv);
    smartScroll(chatWindow);
}

// تهيئة النظام عند التحميل
window.addEventListener('load', () => {
    loadConversations();

    // التحقق من وجود العناصر الأساسية للواجهة
    const requiredElements = ['file-explorer', 'sidebar', 'pluginbar', 'memorybar', 'taskbar'];
    let missingElements = [];

    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            missingElements.push(id);
            console.error(`عنصر أساسي مفقود: ${id}`);
        }
    });

    // إنشاء عنصر taskbar إذا كان مفقوداً
    if (missingElements.includes('taskbar')) {
        console.log('جاري إنشاء عنصر taskbar المفقود...');
        const taskbar = document.createElement('div');
        taskbar.className = 'taskbar';
        taskbar.id = 'taskbar';
        taskbar.innerHTML = `
                    <div class="taskbar-header">
                        <div class="taskbar-title">المهام</div>
                        <button class="explorer-action" onclick="toggleTaskbarToolbar()" title="إغلاق المهام">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="taskbar-list" id="taskbar-list"></div>
                `;
        document.body.appendChild(taskbar);
        missingElements = missingElements.filter(el => el !== 'taskbar');
    }

    // إضافة عنصر floating-toolbar إذا كان مفقوداً
    if (!document.getElementById('floating-toolbar')) {
        console.log('جاري إنشاء عنصر floating-toolbar المفقود...');
        const floatingToolbar = document.createElement('div');
        floatingToolbar.className = 'floating-toolbar';
        floatingToolbar.id = 'floating-toolbar';
        document.body.appendChild(floatingToolbar);
    }

    // إنشاء هيكل افتراضي للمشروع إذا لم يكن موجوداً
    if (!workspace.folders['root'] || !workspace.folders['root'].children || workspace.folders['root'].children.length === 0) {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
    }

    // تهيئة قائمة الملفات المفتوحة إذا لم تكن موجودة
    if (!localStorage.getItem('openFiles')) {
        localStorage.setItem('openFiles', JSON.stringify([]));
    }

    // استعادة حالة المستكشف من التخزين المحلي
    const explorerVisible = localStorage.getItem('explorerVisible') === 'true';
    if (explorerVisible) {
        toggleExplorerToolbar(true); // فتح المستكشف بدون تبديل الحالة
    } else {
        // التأكد من أن المستكشف مغلق
        document.getElementById('file-explorer').classList.remove('visible');
        // تحديث العناصر الأخرى للتكيف مع حالة المستكشف المغلق
        document.getElementById('main-content').classList.remove('explorer-visible');
        document.querySelector('.header').classList.remove('explorer-visible');
        document.querySelector('.footer').classList.remove('explorer-visible');
        if (document.getElementById('code-executor')) {
            document.getElementById('code-executor').classList.remove('explorer-visible');
        }
    }

    updateFileExplorer();

    // تهيئة التيرمنال
    initializeTerminal();

    // إضافة المستمعين لأحداث أزرار الكود
    function addCodeButtonListeners() {
        // إضافة المستمعين لأزرار "فتح في المحرر"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // إضافة المستمعين لأزرار "تشغيل الكود"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    // تهيئة Tippy.js بأسلوب آمن
    function initTippy() {
        if (typeof tippy !== 'undefined') {
            tippy('.message ol li', {
                content: 'عنصر قائمة مرقمة',
                placement: 'right',
                animation: 'scale',
                theme: 'light',
                delay: [500, 0]
            });

            tippy('.message ul li', {
                content: 'عنصر قائمة نقطية',
                placement: 'right',
                animation: 'scale',
                theme: 'light',
                delay: [500, 0]
            });

            // إضافة tippy لأزرار الكود
            tippy('.open-code-btn', {
                content: 'فتح الملف في المحرر',
                placement: 'top',
                animation: 'scale',
                theme: 'light',
                delay: [300, 0]
            });

            tippy('.run-code-btn', {
                content: 'تشغيل الكود',
                placement: 'top',
                animation: 'scale',
                theme: 'light',
                delay: [300, 0]
            });
        }
    }

    function initSortable() {
        const lists = document.querySelectorAll('.message ul, .message ol');

        lists.forEach(list => {
            if (typeof Sortable !== 'undefined') {
                new Sortable(list, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    disabled: true,
                    onEnd: function (evt) {
                        if (evt.to.tagName.toLowerCase() === 'ol') {
                            const items = evt.to.querySelectorAll('li');
                            items.forEach((item, index) => {
                                item.setAttribute('data-number', index + 1);
                            });
                        }
                    }
                });
            }
        });
    }

    setTimeout(() => {
        initTippy();
        initSortable();
        fixNestedLists();
        addCodeButtonListeners(); // إضافة مستمعي الأحداث لأزرار الكود
    }, 1000);

    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.querySelectorAll) {
                        const lists = node.querySelectorAll('ul, ol');
                        const codeButtons = node.querySelectorAll('.open-code-btn, .run-code-btn');
                        if (lists.length || codeButtons.length) {
                            setTimeout(() => {
                                initTippy();
                                initSortable();
                                fixNestedLists();
                                addCodeButtonListeners(); // إضافة مستمعين عند إضافة عناصر جديدة
                            }, 500);
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.getElementById('chat-window'), {
        childList: true,
        subtree: true
    });

    // تهيئة نافذة المعاينة إذا كانت مفتوحة
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar && sidebar.style.display !== 'none') {
        initWebPreviewSidebar();
        sidebar.dataset.initialized = 'true';
    }

    // إضافة المستمعين لأحداث أزرار الكود
    function addCodeButtonListeners() {
        // إضافة المستمعين لأزرار "فتح في المحرر"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // إضافة المستمعين لأزرار "تشغيل الكود"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    setTimeout(() => {
        addCodeButtonListeners();
    }, 1000);
});

// تم إزالة دالة toggleDirection

// نظام المهام الذكية
let tasks = [];
function addTask(title) {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 5);
    const task = {
        id,
        title,
        status: 'قيد التنفيذ',
        time: new Date().toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })
    };
    tasks.unshift(task);
    renderTasks();
    return id;
}
function updateTask(id, status) {
    const task = tasks.find(t => t.id === id);
    if (task) {
        task.status = status;
        renderTasks();
    }
}
function renderTasks() {
    const list = document.getElementById('taskbar-list');
    if (!list) return;
    list.innerHTML = '';
    tasks.forEach(task => {
        const item = document.createElement('div');
        item.className = 'task-item';
        item.innerHTML = `
            <div class="task-title">${task.title}</div>
            <div class="task-status">${task.status}</div>
            <div class="task-time">${task.time}</div>
        `;
        list.appendChild(item);
    });
}
function toggleTaskbar() {
    const bar = document.getElementById('taskbar');
    bar.classList.toggle('visible');
}
// دمج المهام مع إرسال الرسائل
const oldSendMessage = sendMessage;
sendMessage = async function () {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;
    const taskId = addTask(message.substring(0, 30));
    try {
        await oldSendMessage.apply(this, arguments);
        updateTask(taskId, 'تم');
    } catch (e) {
        updateTask(taskId, 'فشل');
    }
}

// نظام الإضافات (Plugins)
let plugins = [
    {
        name: '🎨 مولد لوحة الألوان',
        description: 'إنشاء لوحة ألوان متناسقة للتصميم. استخدم: أنشئ لوحة ألوان [نوع/لون أساسي]',
        enabled: true,
        match: (msg) => /^أنشئ لوحة ألوان|^اعمل لوحة ألوان|^مولد ألوان|^color palette/i.test(msg),
        execute: async (msg) => {
            const colorPalettes = {
                'modern': ['#2563eb', '#3b82f6', '#60a5fa', '#93c5fd', '#dbeafe'],
                'warm': ['#dc2626', '#ef4444', '#f87171', '#fca5a5', '#fecaca'],
                'nature': ['#059669', '#10b981', '#34d399', '#6ee7b7', '#a7f3d0'],
                'sunset': ['#ea580c', '#f97316', '#fb923c', '#fdba74', '#fed7aa'],
                'ocean': ['#0891b2', '#06b6d4', '#22d3ee', '#67e8f9', '#a5f3fc'],
                'purple': ['#7c3aed', '#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe'],
                'gradient': ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
            };

            const type = msg.match(/ألوان\s+(\w+)/)?.[1] || 'modern';
            const palette = colorPalettes[type] || colorPalettes['modern'];

            let response = `<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">🎨 لوحة ألوان ${type}</h3>`;
            response += `<div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0;">`;

            palette.forEach((color, index) => {
                response += `<div style="display: flex; flex-direction: column; align-items: center; background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px;">`;
                response += `<div style="width: 60px; height: 60px; background: ${color}; border-radius: 8px; border: 2px solid rgba(255,255,255,0.3); margin-bottom: 8px;"></div>`;
                response += `<code style="background: rgba(0,0,0,0.3); padding: 4px 8px; border-radius: 4px; font-size: 12px;">${color}</code>`;
                response += `</div>`;
            });

            response += `</div>`;
            response += `<details style="margin-top: 15px;"><summary style="cursor: pointer; font-weight: bold;">📋 كود CSS</summary>`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin-top: 10px; overflow-x: auto;"><code>:root {\n`;
            palette.forEach((color, index) => {
                response += `  --color-${index + 1}: ${color};\n`;
            });
            response += `}</code></pre></details>`;
            response += `<p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">💡 الأنواع المتاحة: modern, warm, nature, sunset, ocean, purple, gradient</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: '🧩 مولد مكونات UI',
        description: 'إنشاء مكونات UI جاهزة (أزرار، بطاقات، نماذج). استخدم: أنشئ مكون [نوع المكون]',
        enabled: true,
        match: (msg) => /^أنشئ مكون|^اعمل مكون|^component|^مكون/i.test(msg),
        execute: async (msg) => {
            const componentType = msg.match(/مكون\s+(\w+)/)?.[1] || 'button';

            const components = {
                'button': {
                    name: 'زر حديث',
                    html: `<button class="modern-btn">انقر هنا</button>`,
                    css: `.modern-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modern-btn:active {
  transform: translateY(0);
}`
                },
                'card': {
                    name: 'بطاقة عصرية',
                    html: `<div class="modern-card">
  <div class="card-image">
    <img src="https://via.placeholder.com/300x200" alt="صورة">
  </div>
  <div class="card-content">
    <h3>عنوان البطاقة</h3>
    <p>وصف مختصر للمحتوى...</p>
    <div class="card-actions">
      <button class="btn-primary">إجراء رئيسي</button>
      <button class="btn-secondary">إجراء ثانوي</button>
    </div>
  </div>
</div>`,
                    css: `.modern-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 350px;
}

.modern-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 24px;
}

.card-content h3 {
  margin: 0 0 12px 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 700;
}

.card-content p {
  color: #666;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.btn-primary, .btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
}`
                }
            };

            const component = components[componentType] || components['button'];

            let response = `<div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); padding: 20px; border-radius: 12px; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0; color: #1e293b;">🧩 ${component.name}</h3>`;

            response += `<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e2e8f0;">`;
            response += `<h4 style="margin: 0 0 10px 0; color: #475569;">HTML:</h4>`;
            response += `<pre style="background: #f8fafc; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e2e8f0;"><code>${component.html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
            response += `</div>`;

            response += `<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e2e8f0;">`;
            response += `<h4 style="margin: 0 0 10px 0; color: #475569;">CSS:</h4>`;
            response += `<pre style="background: #f8fafc; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e2e8f0;"><code>${component.css}</code></pre>`;
            response += `</div>`;

            response += `<p style="margin: 15px 0 0 0; color: #64748b; font-size: 14px;">💡 المكونات المتاحة: button, card, form, navbar, modal, input</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: '📱 مولد تصميم تجاوبي',
        description: 'إنشاء كود CSS للتصميم التجاوبي. استخدم: أنشئ تصميم تجاوبي [نوع التخطيط]',
        enabled: true,
        match: (msg) => /^أنشئ تصميم تجاوبي|^responsive design|^تجاوبي/i.test(msg),
        execute: async (msg) => {
            const layoutType = msg.match(/تجاوبي\s+(\w+)/)?.[1] || 'grid';

            const layouts = {
                'grid': {
                    name: 'تخطيط شبكي',
                    css: `/* تخطيط شبكي تجاوبي */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

/* للشاشات الكبيرة */
@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
  }
}

/* للشاشات المتوسطة */
@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 15px;
  }
}

/* للشاشات الصغيرة */
@media (max-width: 480px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }
}`
                },
                'flexbox': {
                    name: 'تخطيط مرن',
                    css: `/* تخطيط مرن تجاوبي */
.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
}

.flex-item {
  flex: 1 1 300px;
  min-width: 0;
}

/* للشاشات الكبيرة */
@media (min-width: 1200px) {
  .flex-item {
    flex: 1 1 calc(25% - 15px);
  }
}

/* للشاشات المتوسطة */
@media (max-width: 768px) {
  .responsive-flex {
    gap: 15px;
    padding: 15px;
  }

  .flex-item {
    flex: 1 1 calc(50% - 7.5px);
  }
}

/* للشاشات الصغيرة */
@media (max-width: 480px) {
  .responsive-flex {
    gap: 10px;
    padding: 10px;
  }

  .flex-item {
    flex: 1 1 100%;
  }
}`
                }
            };

            const layout = layouts[layoutType] || layouts['grid'];

            let response = `<div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">📱 ${layout.name} تجاوبي</h3>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin: 15px 0;">`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap;"><code>${layout.css}</code></pre>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">📏 نقاط التوقف المستخدمة:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px;">`;
            response += `<li>📱 الهواتف: أقل من 480px</li>`;
            response += `<li>📱 التابلت: 481px - 768px</li>`;
            response += `<li>💻 الحاسوب: 769px - 1199px</li>`;
            response += `<li>🖥️ الشاشات الكبيرة: 1200px فأكثر</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">💡 الأنواع المتاحة: grid, flexbox, navbar, sidebar</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: '🎯 محلل إمكانية الوصول',
        description: 'فحص وتحسين إمكانية الوصول للتصميم. استخدم: فحص إمكانية الوصول',
        enabled: true,
        match: (msg) => /^فحص إمكانية الوصول|^accessibility check|^a11y/i.test(msg),
        execute: async (msg) => {
            let response = `<div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">🎯 دليل إمكانية الوصول</h3>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">✅ قائمة التحقق الأساسية:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px; line-height: 1.6;">`;
            response += `<li>استخدام نص بديل للصور (alt text)</li>`;
            response += `<li>تباين ألوان كافي (4.5:1 للنص العادي)</li>`;
            response += `<li>إمكانية التنقل بلوحة المفاتيح</li>`;
            response += `<li>استخدام عناوين هرمية (h1, h2, h3...)</li>`;
            response += `<li>تسميات واضحة للنماذج</li>`;
            response += `<li>حجم أهداف اللمس 44px على الأقل</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">🛠️ أدوات مجانية للفحص:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px; line-height: 1.6;">`;
            response += `<li><strong>WAVE:</strong> wave.webaim.org</li>`;
            response += `<li><strong>axe DevTools:</strong> إضافة متصفح مجانية</li>`;
            response += `<li><strong>Lighthouse:</strong> مدمج في Chrome DevTools</li>`;
            response += `<li><strong>Color Contrast Analyzer:</strong> لفحص التباين</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">📝 مثال على كود يدعم إمكانية الوصول:</h4>`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap;"><code>&lt;button
  aria-label="إغلاق النافذة"
  class="close-btn"
  tabindex="0"&gt;
  &lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;
&lt;/button&gt;

&lt;img
  src="image.jpg"
  alt="وصف مفصل للصورة"
  role="img"&gt;

&lt;form&gt;
  &lt;label for="email"&gt;البريد الإلكتروني&lt;/label&gt;
  &lt;input
    type="email"
    id="email"
    required
    aria-describedby="email-help"&gt;
  &lt;div id="email-help"&gt;سنستخدم بريدك للتواصل&lt;/div&gt;
&lt;/form&gt;</code></pre>`;
            response += `</div>`;

            response += `</div>`;

            return response;
        }
    },
    {
        name: 'بحث DuckDuckGo',
        description: 'ابحث في الإنترنت بسرعة عبر DuckDuckGo (نتائج فورية مختصرة). اكتب: ابحث في جوجل عن ...',
        enabled: true,
        match: (msg) => /^ابحث في جوجل عن (.+)$/i.test(msg),
        execute: async (msg) => {
            const query = msg.match(/^ابحث في جوجل عن (.+)$/i)[1];
            // DuckDuckGo Instant Answer API (مجاني)
            const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1&skip_disambig=1`;
            const res = await fetch(url);
            const data = await res.json();
            let answer = data.AbstractText || data.Answer || data.Heading || '';
            let link = data.AbstractURL || '';
            let results = '';
            if (answer) results += `<b>النتيجة:</b> ${answer}<br>`;
            if (link) results += `<a href='${link}' target='_blank'>رابط</a><br>`;
            if (!results) results = 'لم يتم العثور على نتيجة مباشرة. جرب سؤالاً آخر.';
            return results;
        }
    },
    {
        name: 'أوامر الملفات الذكية',
        description: 'نفذ أوامر على الملفات والمجلدات مباشرة من الشات: حذف، إنشاء، فتح، إعادة تسمية، عرض محتوى... (مثال: احذف ملف main.js)',
        enabled: true,
        match: (msg) => /^(احذف ملف|أنشئ مجلد|افتح ملف|أعد تسمية ملف|اعرض محتوى ملف)\s+/i.test(msg),
        execute: async (msg) => {
            // حذف ملف
            if (/^احذف ملف (.+)$/i.test(msg)) {
                const fileName = msg.match(/^احذف ملف (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    delete workspace.files[file.id];
                    for (const folder of Object.values(workspace.folders)) {
                        if (folder && Array.isArray(folder.children)) {
                            const idx = folder.children.indexOf(file.id);
                            if (idx !== -1) folder.children.splice(idx, 1);
                        }
                    }
                    updateFileExplorer();
                    return `✅ تم حذف الملف <b>${fileName}</b> بنجاح.`;
                } else {
                    return `❌ الملف <b>${fileName}</b> غير موجود.`;
                }
            }
            // أنشئ مجلد
            if (/^أنشئ مجلد (.+)$/i.test(msg)) {
                const folderName = msg.match(/^أنشئ مجلد (.+)$/i)[1].trim();
                const newPath = workspace.currentPath + folderName + '/';
                createFolder(newPath);
                updateFileExplorer();
                return `✅ تم إنشاء المجلد <b>${folderName}</b> بنجاح.`;
            }
            // افتح ملف
            if (/^افتح ملف (.+)$/i.test(msg)) {
                const fileName = msg.match(/^افتح ملف (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    openFile(file.id);
                    return `✅ تم فتح الملف <b>${fileName}</b> في المحرر.`;
                } else {
                    return `❌ الملف <b>${fileName}</b> غير موجود.`;
                }
            }
            // أعد تسمية ملف
            if (/^أعد تسمية ملف (.+) إلى (.+)$/i.test(msg)) {
                const [_, oldName, newName] = msg.match(/^أعد تسمية ملف (.+) إلى (.+)$/i);
                const file = Object.values(workspace.files).find(f => f.name === oldName.trim());
                if (file) {
                    file.name = newName.trim();
                    updateFileExplorer();
                    return `✅ تم تغيير اسم الملف من <b>${oldName}</b> إلى <b>${newName}</b>.`;
                } else {
                    return `❌ الملف <b>${oldName}</b> غير موجود.`;
                }
            }
            // اعرض محتوى ملف
            if (/^اعرض محتوى ملف (.+)$/i.test(msg)) {
                const fileName = msg.match(/^اعرض محتوى ملف (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    return `<b>محتوى الملف ${fileName}:</b><pre><code>${escapeHtml(file.content)}</code></pre>`;
                } else {
                    return `❌ الملف <b>${fileName}</b> غير موجود.`;
                }
            }
            return '⚠️ لم يتم التعرف على الأمر. جرب: احذف ملف ...، أنشئ مجلد ...، افتح ملف ...، أعد تسمية ملف ...، اعرض محتوى ملف ...';
        }
    },
    {
        name: 'استعراض الملفات',
        description: 'استعرض الملفات المتاحة في المشروع واسمح للمستخدم بفتح ملف مباشرة. استخدم: اعرض الملفات المتاحة',
        enabled: true,
        match: (msg) => /^اعرض الملفات المتاحة$|^اظهر قائمة الملفات$|^عرض الملفات$/i.test(msg),
        execute: async (msg) => {
            const files = Object.values(workspace.files);
            if (files.length === 0) {
                return '❌ لا توجد ملفات في المشروع حاليًا.';
            }

            let result = '<b>الملفات المتاحة في المشروع:</b><br><ul>';
            // ترتيب الملفات حسب المسار ثم الاسم
            files.sort((a, b) => {
                if (a.path !== b.path) return a.path.localeCompare(b.path);
                return a.name.localeCompare(b.name);
            }).forEach(file => {
                // إضافة زر لفتح الملف مباشرة
                result += `<li>${file.path}${file.name} -
                            <button onclick="openFile('${file.id}')"
                                style="background: #5e35b1; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; cursor: pointer;">
                                <i class="fas fa-code"></i> فتح
                            </button>
                        </li>`;
            });
            result += '</ul>';
            return result;
        }
    }
];
function renderPlugins() {
    const list = document.getElementById('pluginbar-list');
    if (!list) return;
    list.innerHTML = '';
    plugins.forEach((plugin, idx) => {
        const item = document.createElement('div');
        item.className = 'plugin-item';
        item.innerHTML = `
            <div class='plugin-name'>${plugin.name}</div>
            <div class='plugin-desc'>${plugin.description}</div>
            <div class='plugin-switch'>
                <label>مفعل<input type='checkbox' ${plugin.enabled ? 'checked' : ''} onchange='togglePlugin(${idx})'></label>
            </div>
        `;
        list.appendChild(item);
    });
}
function togglePluginbar() {
    const bar = document.getElementById('pluginbar');
    bar.classList.toggle('visible');
    renderPlugins();
}
function togglePlugin(idx) {
    plugins[idx].enabled = !plugins[idx].enabled;
    renderPlugins();
}
// اعتراض إرسال الرسائل لتفعيل الإضافات
const oldSendMessagePlugin = sendMessage;
sendMessage = async function () {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;
    // تحقق من وجود إضافة مناسبة
    const plugin = plugins.find(p => p.enabled && p.match(message));
    if (plugin) {
        // أضف رسالة المستخدم
        const chatWindow = document.getElementById("chat-window");
        const userDiv = document.createElement("div");
        userDiv.className = "message user";
        userDiv.innerHTML = `<div class="message-content">${DOMPurify.sanitize(message, { KEEP_CONTENT: true })}</div>`;
        chatWindow.appendChild(userDiv);
        userDiv.scrollIntoView({ behavior: "smooth" });
        inputElem.value = "";
        inputElem.style.height = "40px";
        // أضف مؤشر انتظار
        const typingDiv = document.createElement("div");
        typingDiv.className = "message bot typing-indicator";
        typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
        chatWindow.appendChild(typingDiv);
        typingDiv.scrollIntoView({ behavior: "smooth" });
        try {
            const pluginResult = await plugin.execute(message);
            typingDiv.remove();
            const botDiv = document.createElement("div");
            botDiv.className = "message bot";
            botDiv.innerHTML = `<div class='message-content'>${pluginResult}</div>`;
            chatWindow.appendChild(botDiv);
            botDiv.scrollIntoView({ behavior: "smooth" });
        } catch (e) {
            typingDiv.remove();
            const botDiv = document.createElement("div");
            botDiv.className = "message bot error";
            botDiv.innerHTML = `<div class='message-content'>حدث خطأ أثناء تنفيذ الإضافة.</div>`;
            chatWindow.appendChild(botDiv);
            botDiv.scrollIntoView({ behavior: "smooth" });
        }
        return;
    }
    // إذا لم توجد إضافة مناسبة، نفذ السلوك الافتراضي
    await oldSendMessagePlugin.apply(this, arguments);
}

// نظام الذاكرة الذكية (الملاحظات)
let notes = JSON.parse(localStorage.getItem('notes') || '[]');
function saveNotes() {
    localStorage.setItem('notes', JSON.stringify(notes));
}
function addNote(title, content) {
    const note = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
        title: title || 'بدون عنوان',
        content: content || '',
        time: new Date().toLocaleString('ar-EG', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit' })
    };
    notes.unshift(note);
    saveNotes();
    renderNotes();
}
function editNote(id) {
    const note = notes.find(n => n.id === id);
    if (!note) return;
    const newTitle = prompt('تعديل العنوان:', note.title);
    if (newTitle === null) return;
    const newContent = prompt('تعديل المحتوى:', note.content);
    if (newContent === null) return;
    note.title = newTitle;
    note.content = newContent;
    saveNotes();
    renderNotes();
}
function deleteNote(id) {
    notes = notes.filter(n => n.id !== id);
    saveNotes();
    renderNotes();
}
function renderNotes() {
    const list = document.getElementById('memorybar-list');
    if (!list) return;
    list.innerHTML = '';
    notes.forEach(note => {
        const item = document.createElement('div');
        item.className = 'note-item';
        item.innerHTML = `
            <div class='note-title'>${note.title}</div>
            <div class='note-content'>${note.content}</div>
            <div class='note-time'>${note.time}</div>
            <div class='note-actions'>
                <button onclick="editNote('${note.id}')" title="تعديل">✏️</button>
                <button onclick="deleteNote('${note.id}')" title="حذف">🗑️</button>
            </div>
        `;
        list.appendChild(item);
    });
}
function toggleMemorybar() {
    const bar = document.getElementById('memorybar');
    bar.classList.toggle('visible');
    renderNotes();
}
function addNotePrompt() {
    const title = prompt('عنوان الملاحظة:');
    if (title === null) return;
    const content = prompt('محتوى الملاحظة:');
    if (content === null) return;
    addNote(title, content);
}
// حفظ رسالة كشريحة ملاحظة من الشات (زر يظهر عند تمرير الماوس على رسالة)
// تم إزالة هذه الوظيفة حسب طلب المستخدم
/*
document.addEventListener('mouseover', function(e) {
    const msg = e.target.closest('.message.bot, .message.user');
    if (msg && !msg.querySelector('.save-note-btn')) {
        const btn = document.createElement('button');
        btn.className = 'save-note-btn';
        btn.textContent = '💾';
        btn.title = 'حفظ كملاحظة';
        btn.style.position = 'absolute';
        btn.style.left = '8px';
        btn.style.top = '8px';
        btn.style.zIndex = 10;
        btn.onclick = function(ev) {
            ev.stopPropagation();
            const content = msg.textContent.trim();
            addNote('من المحادثة', content);
            btn.remove();
        };
        msg.style.position = 'relative';
        msg.appendChild(btn);
    }
});
*/
// عند تحميل الصفحة، عرض الملاحظات
window.addEventListener('load', renderNotes);

// توابع toolbar الجديدة
function closeAllSidebars(except) {
    const ids = ['file-explorer', 'sidebar', 'pluginbar'];
    ids.forEach(id => {
        if (id !== except) {
            const el = document.getElementById(id);
            if (el) el.classList.remove('visible');
            else console.warn(`العنصر ${id} غير موجود في DOM`);
        }
    });
}

// تحديث دالة تبديل المستكشف
function toggleExplorerToolbar(skipToggle) {
    closeAllSidebars('file-explorer');

    const explorer = document.getElementById('file-explorer');
    const container = document.getElementById('main-content');
    const codeExecutor = document.getElementById('code-executor');

    if (!skipToggle) {
        explorer.classList.toggle('visible');
    }
    const isExplorerVisible = explorer.classList.contains('visible');
    localStorage.setItem('explorerVisible', isExplorerVisible ? 'true' : 'false');

    // Still apply classes but don't shift content
    container.classList.toggle('explorer-visible', isExplorerVisible);
    if (codeExecutor) {
        codeExecutor.classList.toggle('explorer-visible', isExplorerVisible);
    }

    // Update toggle button state
    const explorerToggleBtn = document.getElementById('explorer-toggle');
    if (explorerToggleBtn) {
        explorerToggleBtn.classList.toggle('active', isExplorerVisible);
    }
}

function toggleSidebarToolbar() {
    closeAllSidebars('sidebar');
    document.getElementById('sidebar').classList.toggle('visible');
    // تفعيل/تعطيل الزر
    const menuToggleBtn = document.getElementById('menu-toggle');
    if (menuToggleBtn) {
        menuToggleBtn.classList.toggle('active', document.getElementById('sidebar').classList.contains('visible'));
    }
}

function togglePluginbarToolbar() {
    closeAllSidebars('pluginbar');
    document.getElementById('pluginbar').classList.toggle('visible');
    renderPlugins();
    // تفعيل/تعطيل الزر
    const pluginbarToggleBtn = document.getElementById('pluginbar-toggle');
    if (pluginbarToggleBtn) {
        pluginbarToggleBtn.classList.toggle('active', document.getElementById('pluginbar').classList.contains('visible'));
    }
}

function toggleMemorybarToolbar() {
    closeAllSidebars('memorybar');
    document.getElementById('memorybar').classList.toggle('visible');
    renderNotes();
    // تفعيل/تعطيل الزر
    const memorybarToggleBtn = document.getElementById('memorybar-toggle');
    if (memorybarToggleBtn) {
        memorybarToggleBtn.classList.toggle('active', document.getElementById('memorybar').classList.contains('visible'));
    }
}

function toggleTaskbarToolbar() {
    closeAllSidebars('taskbar');
    const taskbar = document.getElementById('taskbar');
    if (taskbar) {
        taskbar.classList.toggle('visible');
        // تفعيل/تعطيل الزر
        const taskbarToggleBtn = document.getElementById('taskbar-toggle');
        if (taskbarToggleBtn) {
            taskbarToggleBtn.classList.toggle('active', taskbar.classList.contains('visible'));
        }
    } else {
        console.error('عنصر taskbar غير موجود في الصفحة!');
    }
}

// إضافة دالة لإغلاق جميع الملفات
function closeAllFiles() {
    // تفريغ قائمة الملفات المفتوحة
    localStorage.setItem('openFiles', JSON.stringify([]));

    activeFileId = null;
    document.getElementById('code-executor').classList.remove('visible');

    // تنظيف iframe التنفيذ
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    updateFileTabs();
    updateFileExplorer();
}

// دالة إغلاق نافذة المعاينة الجانبية
function closeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.style.display = 'none';

        // إعادة تعيين حالة النافذة عند الإغلاق
        sidebar.classList.remove('minimized', 'maximized');

        // إيقاف المعاينة
        const iframe = document.getElementById('web-preview-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.src = 'about:blank';
        }

        // حفظ حالة الإغلاق
        localStorage.setItem('webPreviewMinimized', false);
        localStorage.setItem('webPreviewMaximized', false);
    }
}

// دالة تهيئة نافذة المعاينة الجانبية
function initWebPreviewSidebar() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // إضافة مستمعي الأحداث للأزرار
    document.getElementById('preview-refresh').addEventListener('click', refreshWebPreview);
    document.getElementById('preview-minimize').addEventListener('click', minimizeWebPreview);
    document.getElementById('preview-maximize').addEventListener('click', maximizeWebPreview);
    document.getElementById('preview-close').addEventListener('click', closeWebPreview);

    // مستمع حدث لتغيير الجهاز
    const deviceSelector = document.getElementById('device-selector');
    if (deviceSelector) {
        deviceSelector.addEventListener('change', function(e) {
            changeDeviceView(e);
            // تمرير الإطار إلى العرض بعد تغيير الجهاز
            scrollDeviceFrameIntoView();
        });
    }

    // جعل النافذة قابلة للسحب
    makeDraggable(sidebar, document.querySelector('.preview-drag-handle'));

    // إضافة مستمع لتغيير الحجم
    const resizeHandle = document.getElementById('preview-resize-handle');
    if (resizeHandle) {
        resizeHandle.addEventListener('mousedown', initResize);
    }

    // استعادة الإعدادات المحفوظة
    restoreWebPreviewSettings();

    // إضافة مستمع لتغيير حجم النافذة لضمان التجاوب
    window.addEventListener('resize', adjustWebPreviewForScreenSize);
}

// دالة تحديث معاينة الويب
function refreshWebPreview() {
    // إعادة توليد المعاينة كما في تشغيل كود HTML
    runCodeInExecutor();
}

// دالة ضبط حجم نافذة المعاينة بناءً على حجم الشاشة
function adjustWebPreviewForScreenSize() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar || sidebar.style.display === 'none') return;

    // إذا كانت النافذة في وضع التكبير أو التصغير، لا نقوم بأي تغيير
    if (sidebar.classList.contains('maximized') || sidebar.classList.contains('minimized')) {
        return;
    }

    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // إخفاء device-selector على الشاشات الصغيرة
    const deviceSelectWrapper = document.querySelector('.device-select-wrapper');
    if (deviceSelectWrapper) {
        if (windowWidth <= 700) {
            deviceSelectWrapper.style.display = 'none';
        } else {
            deviceSelectWrapper.style.display = '';
        }
    }

    // ضبط حجم وموضع النافذة بناءً على حجم الشاشة
    if (windowWidth <= 576) {
        // الأجهزة الصغيرة جدًا (الهواتف)
        sidebar.style.width = '100%';
        sidebar.style.height = '100%';
        sidebar.style.top = '0';
        sidebar.style.left = '0';
    } else if (windowWidth <= 768) {
        // الأجهزة الصغيرة (الهواتف الكبيرة)
        sidebar.style.width = '95%';
        sidebar.style.height = '90%';
        sidebar.style.top = '5%';
        sidebar.style.left = '2.5%';
    } else if (windowWidth <= 992) {
        // الأجهزة المتوسطة (التابلت)
        sidebar.style.width = '90%';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '5%';
    } else if (windowWidth <= 1200) {
        // الأجهزة الكبيرة (الحواسيب المحمولة)
        sidebar.style.width = '80%';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '10%';
    } else {
        // الأجهزة الكبيرة جدًا (الحواسيب المكتبية)
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
    }

    // جعل iframe يأخذ كل المساحة المتاحة دائماً
    const iframe = document.getElementById('web-preview-iframe');
    if (iframe) {
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
    }
}

// تعديل دالة تشغيل كود HTML لضبط حجم المعاينة بعد عرضها
async function runHtmlCode(code) {
    try {
        // إنشاء blob من كود HTML
        const blob = new Blob([code], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        // البحث عن الملفات المرتبطة (CSS، JS، الصور)
        const linkedFiles = findLinkedFiles(code);

        // إنشاء خريطة الموارد
        const resourceMap = createResourceMap(linkedFiles);

        // استبدال روابط الموارد في كود HTML
        const modifiedHtml = replaceLinkedResources(code, resourceMap);

        // إضافة CSS لضمان تجاوب المحتوى
        const responsiveMetaTag = '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
        const responsiveCSS = `
        <style>
            html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
            }
            * {
                box-sizing: border-box;
            }
        </style>
        `;

        // إدراج meta viewport و CSS في الكود
        let finalHtml = modifiedHtml;
        if (!finalHtml.includes('<meta name="viewport"')) {
            finalHtml = finalHtml.replace('</head>', `${responsiveMetaTag}\n</head>`);
        }
        if (!finalHtml.includes('box-sizing: border-box')) {
            finalHtml = finalHtml.replace('</head>', `${responsiveCSS}\n</head>`);
        }

        // إنشاء blob جديد بالكود المعدل
        const modifiedBlob = new Blob([finalHtml], { type: 'text/html' });
        const modifiedUrl = URL.createObjectURL(modifiedBlob);

        // عرض النتيجة في نافذة المعاينة الجانبية
        const sidebar = document.getElementById('web-preview-sidebar');
        if (sidebar) {
            sidebar.style.display = 'flex';

            // ضبط حجم النافذة بناءً على حجم الشاشة
            adjustWebPreviewForScreenSize();

            // تحميل المعاينة في الإطار
            const iframe = document.getElementById('web-preview-iframe');
            if (iframe) {
                // إظهار مؤشر التحميل
                document.getElementById('preview-loading').classList.add('active');

                // تعيين المحتوى في الإطار
                iframe.src = modifiedUrl;

                // إضافة مستمع لحدث تحميل الإطار
                iframe.onload = function() {
                    // إخفاء مؤشر التحميل
                    document.getElementById('preview-loading').classList.remove('active');

                    // إضافة مستمع لتغيير حجم النافذة داخل الإطار
                    try {
                        if (iframe.contentWindow) {
                            iframe.contentWindow.addEventListener('resize', function() {
                                // تحديث أبعاد الجهاز في واجهة المستخدم
                                updateDeviceDimensions();
                            });
                        }

                        // تطبيق إعدادات الجهاز الحالي
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            const currentDevice = deviceSelector.value;
                            setTimeout(() => {
                                changeDeviceView(currentDevice);
                            }, 100);
                        }
                    } catch (e) {
                        console.warn('خطأ في إضافة مستمع الحجم للإطار:', e);
                    }
                };

                // تعيين onerror للإطار
                iframe.onerror = function() {
                    document.getElementById('preview-loading').classList.remove('active');
                    console.error('فشل تحميل المعاينة');

                    // إضافة رسالة خطأ في التيرمنال
                    const terminalElem = document.getElementById('executor-result');
                    if (terminalElem) {
                        terminalElem.innerHTML += '<div class="terminal-message error">فشل تحميل المعاينة</div>';
                    }
                };
            }
        } else {
            // إذا لم تكن نافذة المعاينة موجودة، نعرض النتيجة في نافذة جديدة
            window.open(url, '_blank');
        }

        // إضافة رسالة نجاح في التيرمنال
        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML += '<div class="terminal-message success">تم تشغيل كود HTML بنجاح. تم فتح المعاينة.</div>';
        }
    } catch (error) {
        console.error('خطأ في تشغيل كود HTML:', error);

        // إضافة رسالة خطأ في التيرمنال
        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML += `<div class="terminal-message error">خطأ في تشغيل كود HTML: ${error.message}</div>`;
        }
    }
}

// دالة جعل العنصر قابل للسحب
function makeDraggable(element, handle) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

    if (handle) {
        // إذا تم تحديد مقبض، استخدمه للسحب
        handle.onmousedown = dragMouseDown;
        // استخدام addEventListener مع passive: false بدلاً من ontouchstart
        handle.addEventListener('touchstart', dragTouchStart, { passive: false });
    } else {
        // وإلا استخدم العنصر نفسه
        element.onmousedown = dragMouseDown;
        // استخدام addEventListener مع passive: false بدلاً من ontouchstart
        element.addEventListener('touchstart', dragTouchStart, { passive: false });
    }

    function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();

        // لا تسمح بالسحب إذا كانت النافذة مكبرة
        if (element.classList.contains('maximized')) {
            return;
        }

        // الحصول على موضع الماوس عند بدء السحب
        pos3 = e.clientX;
        pos4 = e.clientY;
        document.onmouseup = closeDragElement;
        document.onmousemove = elementDrag;
    }

    // دعم اللمس للأجهزة المحمولة
    function dragTouchStart(e) {
        // لا تسمح بالسحب إذا كانت النافذة مكبرة
        if (element.classList.contains('maximized')) {
            return;
        }

        // الحصول على موضع اللمس الأول
        const touch = e.touches[0];
        pos3 = touch.clientX;
        pos4 = touch.clientY;

        // إضافة مستمعي أحداث اللمس مع passive: false
        document.addEventListener('touchend', closeTouchDragElement, { passive: true });
        document.addEventListener('touchmove', elementTouchDrag, { passive: false });

        // منع السلوك الافتراضي (مثل التمرير)
        e.preventDefault();
    }

    function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();

        // حساب الموضع الجديد
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;

        // حساب الحدود
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = element.offsetWidth;
        const elementHeight = element.offsetHeight;

        // الحصول على الموضع الحالي
        let newTop = element.offsetTop - pos2;
        let newLeft = element.offsetLeft - pos1;

        // التأكد من أن العنصر لا يخرج من حدود النافذة
        newTop = Math.max(0, Math.min(newTop, windowHeight - 40));
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - 40));

        // تحديث موضع العنصر
        element.style.top = newTop + "px";
        element.style.left = newLeft + "px";

        // حفظ الموضع الجديد
        if (element.id === 'web-preview-sidebar') {
            saveWebPreviewPosition();
        }
    }

    // دعم السحب باللمس
    function elementTouchDrag(e) {
        // منع السلوك الافتراضي
        e.preventDefault();

        // الحصول على موضع اللمس الحالي
        const touch = e.touches[0];

        // حساب الموضع الجديد
        pos1 = pos3 - touch.clientX;
        pos2 = pos4 - touch.clientY;
        pos3 = touch.clientX;
        pos4 = touch.clientY;

        // حساب الحدود
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = element.offsetWidth;
        const elementHeight = element.offsetHeight;

        // الحصول على الموضع الحالي
        let newTop = element.offsetTop - pos2;
        let newLeft = element.offsetLeft - pos1;

        // التأكد من أن العنصر لا يخرج من حدود النافذة
        newTop = Math.max(0, Math.min(newTop, windowHeight - 40));
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - 40));

        // تحديث موضع العنصر
        element.style.top = newTop + "px";
        element.style.left = newLeft + "px";

        // حفظ الموضع الجديد
        if (element.id === 'web-preview-sidebar') {
            saveWebPreviewPosition();
        }
    }

    function closeDragElement() {
        // إيقاف تحريك العنصر عند ترك زر الماوس
        document.onmouseup = null;
        document.onmousemove = null;
    }

    // إيقاف السحب باللمس
    function closeTouchDragElement() {
        document.removeEventListener('touchend', closeTouchDragElement);
        document.removeEventListener('touchmove', elementTouchDrag);
    }
}

// دالة بدء تغيير الحجم
function initResize(e) {
    e.preventDefault();

    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar.classList.contains('maximized')) return;

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = sidebar.offsetWidth;
    const startHeight = sidebar.offsetHeight;

    document.addEventListener('mousemove', doResize);
    document.addEventListener('mouseup', stopResize);

    function doResize(e) {
        const newWidth = startWidth + (e.clientX - startX);
        const newHeight = startHeight + (e.clientY - startY);

        // تطبيق الحجم الجديد مع مراعاة الحد الأدنى
        sidebar.style.width = Math.max(320, newWidth) + 'px';
        sidebar.style.height = Math.max(200, newHeight) + 'px';

        // حفظ الحجم الجديد
        saveWebPreviewPosition();
    }

    function stopResize() {
        document.removeEventListener('mousemove', doResize);
        document.removeEventListener('mouseup', stopResize);
    }
}

// دالة حفظ موضع وحجم نافذة المعاينة
function saveWebPreviewPosition() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // حفظ الموقع والحجم
    localStorage.setItem('webPreviewTop', sidebar.style.top);
    localStorage.setItem('webPreviewLeft', sidebar.style.left);
    localStorage.setItem('webPreviewWidth', sidebar.style.width);
    localStorage.setItem('webPreviewHeight', sidebar.style.height);

    // حفظ حالة التصغير/التكبير
    localStorage.setItem('webPreviewMaximized', sidebar.classList.contains('maximized'));
    localStorage.setItem('webPreviewMinimized', sidebar.classList.contains('minimized'));

    console.log('تم حفظ موضع نافذة المعاينة:', {
        top: sidebar.style.top,
        left: sidebar.style.left,
        width: sidebar.style.width,
        height: sidebar.style.height,
        isMaximized: sidebar.classList.contains('maximized'),
        isMinimized: sidebar.classList.contains('minimized')
    });
}

// دالة إعادة تعيين موضع نافذة المعاينة
function resetWebPreviewPosition() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
    }
}

// دالة استعادة إعدادات نافذة المعاينة
function restoreWebPreviewSettings() {
    // استرجاع موضع النافذة
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    const savedTop = localStorage.getItem('webPreviewTop');
    const savedLeft = localStorage.getItem('webPreviewLeft');
    const savedWidth = localStorage.getItem('webPreviewWidth');
    const savedHeight = localStorage.getItem('webPreviewHeight');
    const deviceType = localStorage.getItem('webPreviewDeviceType') || 'responsive';

    // استرجاع الحجم والموضع إذا كانت محفوظة
    if (savedTop && savedLeft) {
        sidebar.style.top = savedTop;
        sidebar.style.left = savedLeft;
    } else {
        // وضع افتراضي إذا لم تكن محفوظة
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
    }

    if (savedWidth && savedHeight) {
        sidebar.style.width = savedWidth;
        sidebar.style.height = savedHeight;
    } else {
        // حجم افتراضي إذا لم يكن محفوظاً
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
    }

    // استرجاع حالة التصغير/التكبير
    const isMaximized = localStorage.getItem('webPreviewMaximized') === 'true';
    const isMinimized = localStorage.getItem('webPreviewMinimized') === 'true';

    if (isMaximized) {
        sidebar.classList.add('maximized');
    } else if (isMinimized) {
        sidebar.classList.add('minimized');
    }

    // استرجاع نوع الجهاز
    const deviceSelector = document.getElementById('device-selector');
    if (deviceSelector && deviceType) {
        deviceSelector.value = deviceType;

        // تطبيق إعدادات الجهاز
        setTimeout(() => {
            changeDeviceView(deviceType);

            // تمرير الإطار إلى العرض
            scrollDeviceFrameIntoView();
        }, 500);
    }
}

// دالة لضمان أن الإطار مرئي بالكامل
function scrollDeviceFrameIntoView() {
    setTimeout(() => {
        const deviceFrame = document.getElementById('device-frame');
        if (deviceFrame) {
            // استخدام scrollIntoView لضمان رؤية الإطار بالكامل
            deviceFrame.scrollIntoView({
                block: 'center',
                inline: 'center',
                behavior: 'smooth'
            });

            // تأكيد إضافي من عدم تغطية الجزء العلوي
            const container = document.getElementById('preview-container');
            if (container) {
                container.scrollTop = 0;
            }
        }
    }, 300);
}

// تحديث دالة openFile لتحديث مؤشر اللغة في شريط الحالة
const originalOpenFileWithStatusBar = openFile;
openFile = function(fileId) {
    originalOpenFileWithStatusBar(fileId);

    // تحديث مؤشر اللغة في شريط الحالة
    const file = workspace.files[fileId];
    if (file && file.language) {
        const statusBar = document.querySelector('.status-bar');
        if (statusBar) {
            const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
            if (langIndicator) {
                langIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
            }
        }
    }
};

// Window resize event listener
window.addEventListener('resize', function() {
    // Update Monaco editor layout
    if (monacoEditor) {
        monacoEditor.layout();
    }

    // Update status bar responsiveness
    updateStatusBarResponsiveness();

    // Other window resize handlers...
});

// دوال لدعم ربط الملفات في HTML

// العثور على الملفات المرتبطة في HTML
function findLinkedFiles(htmlContent) {
    const linkedFiles = [];

    // البحث عن روابط CSS
    const cssRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
    let cssMatch;
    while ((cssMatch = cssRegex.exec(htmlContent)) !== null) {
        const href = cssMatch[1];
        if (href.endsWith('.css') || cssMatch[0].includes('stylesheet')) {
            linkedFiles.push({
                path: href,
                type: 'css'
            });
        }
    }

    // البحث عن روابط JavaScript
    const jsRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let jsMatch;
    while ((jsMatch = jsRegex.exec(htmlContent)) !== null) {
        const src = jsMatch[1];
        linkedFiles.push({
            path: src,
            type: 'js'
        });
    }

    // البحث عن روابط الصور
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let imgMatch;
    while ((imgMatch = imgRegex.exec(htmlContent)) !== null) {
        const imgPath = imgMatch[1];
        // تجاهل الصور التي تبدأ بـ http:// أو https:// أو data:
        if (!imgPath.startsWith('http://') && !imgPath.startsWith('https://') && !imgPath.startsWith('data:')) {
            linkedFiles.push({
                path: imgPath,
                type: 'img'
            });
        }
    }

    return linkedFiles;
}

// البحث عن ملف حسب المسار
function findFileByPath(filePath) {
    // تنظيف المسار
    let normalizedPath = filePath.trim();

    // إذا كان المسار مطلقًا (يبدأ بـ /)، نزيل الـ / الأولى
    if (normalizedPath.startsWith('/')) {
        normalizedPath = normalizedPath.substring(1);
    }

    // البحث في جميع الملفات
    for (const fileId in workspace.files) {
        const file = workspace.files[fileId];

        // تحقق من تطابق الاسم مباشرة
        if (file.name === normalizedPath) {
            return file;
        }

        // تحقق من تطابق المسار الكامل
        const fullPath = file.path.startsWith('/') ? file.path.substring(1) : file.path;
        if (fullPath === normalizedPath || fullPath + file.name === normalizedPath) {
            return file;
        }

        // تحقق من تطابق الاسم فقط (للملفات في المجلد الحالي)
        if (normalizedPath.indexOf('/') === -1 && file.name === normalizedPath) {
            return file;
        }
    }

    return null;
}

// إنشاء خريطة الموارد (Blob URLs)
function createResourceMap(linkedFiles) {
    const resourceMap = {};

    for (const linkedFile of linkedFiles) {
        const file = findFileByPath(linkedFile.path);
        if (file) {
            // تحديد نوع المحتوى
            let mimeType = 'text/plain';
            if (linkedFile.type === 'css') {
                mimeType = 'text/css';
            } else if (linkedFile.type === 'js') {
                mimeType = 'application/javascript';
            } else if (linkedFile.type === 'img') {
                // تحديد نوع MIME للصورة بناءً على الامتداد
                const extension = linkedFile.path.split('.').pop().toLowerCase();
                switch (extension) {
                    case 'png':
                        mimeType = 'image/png';
                        break;
                    case 'jpg':
                    case 'jpeg':
                        mimeType = 'image/jpeg';
                        break;
                    case 'gif':
                        mimeType = 'image/gif';
                        break;
                    case 'svg':
                        mimeType = 'image/svg+xml';
                        break;
                    default:
                        mimeType = 'image/png';
                }
            }

            // إنشاء Blob URL
            const blob = new Blob([file.content], { type: mimeType });
            resourceMap[linkedFile.path] = URL.createObjectURL(blob);
        }
    }

    return resourceMap;
}

// استبدال روابط الموارد في HTML
function replaceLinkedResources(htmlContent, resourceMap) {
    let modifiedHTML = htmlContent;

    // استبدال روابط CSS
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.css')) {
            const regex = new RegExp(`href=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `href="${url}"`);
        }
    }

    // استبدال روابط JavaScript
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }

    // استبدال روابط الصور
    for (const [path, url] of Object.entries(resourceMap)) {
        if (!path.endsWith('.css') && !path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }

    return modifiedHTML;
}

// دالة لمعالجة الأحرف الخاصة في التعبيرات النمطية
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// تهيئة التيرمنال عند تحميل الصفحة
function initializeTerminal() {
    // إضافة مقبض تغيير الحجم
    addTerminalResizer();

    // استعادة حالة التيرمنال
    restoreTerminalState();

    // تحديث رأس التيرمنال
    updateTerminalHeader();

    // إضافة زر التيرمنال في شريط الحالة
    addTerminalToggleButton();

    // إضافة مستمع لمفتاح ESC لإغلاق التيرمنال
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.getElementById('code-executor').classList.contains('visible')) {
            const executorFooter = document.querySelector('.executor-footer');
            if (executorFooter && !executorFooter.classList.contains('collapsed') && !executorFooter.classList.contains('hidden')) {
                executorFooter.classList.add('collapsed');
                localStorage.setItem('terminalState', 'collapsed');
                e.preventDefault();
            }
        }
    });
}

// تحديث مستمع تحميل الصفحة لتهيئة نافذة المعاينة
window.addEventListener('load', function() {
    loadConversations();

    // التحقق من وجود العناصر الأساسية للواجهة
    const requiredElements = ['file-explorer', 'sidebar', 'pluginbar', 'memorybar', 'taskbar'];
    let missingElements = [];

    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            missingElements.push(id);
            console.error(`عنصر أساسي مفقود: ${id}`);
        }
    });

    // إنشاء عنصر taskbar إذا كان مفقوداً
    if (missingElements.includes('taskbar')) {
        console.log('جاري إنشاء عنصر taskbar المفقود...');
        const taskbar = document.createElement('div');
        taskbar.className = 'taskbar';
        taskbar.id = 'taskbar';
        taskbar.innerHTML = `
                    <div class="taskbar-header">
                        <div class="taskbar-title">المهام</div>
                        <button class="explorer-action" onclick="toggleTaskbarToolbar()" title="إغلاق المهام">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="taskbar-list" id="taskbar-list"></div>
                `;
        document.body.appendChild(taskbar);
        missingElements = missingElements.filter(el => el !== 'taskbar');
    }

    // إضافة عنصر floating-toolbar إذا كان مفقوداً
    if (!document.getElementById('floating-toolbar')) {
        console.log('جاري إنشاء عنصر floating-toolbar المفقود...');
        const floatingToolbar = document.createElement('div');
        floatingToolbar.className = 'floating-toolbar';
        floatingToolbar.id = 'floating-toolbar';
        document.body.appendChild(floatingToolbar);
    }

    // إنشاء هيكل افتراضي للمشروع إذا لم يكن موجوداً
    if (!workspace.folders['root'] || !workspace.folders['root'].children || workspace.folders['root'].children.length === 0) {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
    }

    // تهيئة قائمة الملفات المفتوحة إذا لم تكن موجودة
    if (!localStorage.getItem('openFiles')) {
        localStorage.setItem('openFiles', JSON.stringify([]));
    }

    // استعادة حالة المستكشف من التخزين المحلي
    const explorerVisible = localStorage.getItem('explorerVisible') === 'true';
    if (explorerVisible) {
        toggleExplorerToolbar(true); // فتح المستكشف بدون تبديل الحالة
    } else {
        // التأكد من أن المستكشف مغلق
        document.getElementById('file-explorer').classList.remove('visible');
        // تحديث العناصر الأخرى للتكيف مع حالة المستكشف المغلق
        document.getElementById('main-content').classList.remove('explorer-visible');
        document.querySelector('.header').classList.remove('explorer-visible');
        document.querySelector('.footer').classList.remove('explorer-visible');
        if (document.getElementById('code-executor')) {
            document.getElementById('code-executor').classList.remove('explorer-visible');
        }
    }

    updateFileExplorer();

    // تهيئة التيرمنال
    initializeTerminal();

    // تهيئة نافذة المعاينة إذا كانت مفتوحة
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar && sidebar.style.display !== 'none') {
        initWebPreviewSidebar();
        sidebar.dataset.initialized = 'true';
    }

    // إضافة المستمعين لأحداث أزرار الكود
    function addCodeButtonListeners() {
        // إضافة المستمعين لأزرار "فتح في المحرر"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // إضافة المستمعين لأزرار "تشغيل الكود"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    setTimeout(() => {
        addCodeButtonListeners();
    }, 1000);
});

// تحديث تلميح الأداة لزر اختيار الجهاز
function updateDeviceButtonTooltip(deviceType) {
    const deviceNames = {
        responsive: 'تجاوب كامل',
        desktop: 'سطح المكتب',
        laptop: 'لابتوب',
        tablet: 'تابلت',
        mobile: 'موبايل',
        custom: 'مخصص'
    };

    const deviceIcon = document.querySelector('.device-select-wrapper i');
    if (deviceIcon) {
        // تغيير الرمز حسب نوع الجهاز
        deviceIcon.className = 'fas';
        if (deviceType === 'mobile') {
            deviceIcon.classList.add('fa-mobile-alt');
        } else if (deviceType === 'tablet') {
            deviceIcon.classList.add('fa-tablet-alt');
        } else if (deviceType === 'laptop') {
            deviceIcon.classList.add('fa-laptop');
        } else if (deviceType === 'desktop') {
            deviceIcon.classList.add('fa-desktop');
        } else if (deviceType === 'custom') {
            deviceIcon.classList.add('fa-ruler-combined');
        } else {
            deviceIcon.classList.add('fa-expand-arrows-alt');
        }

        // تحديث نص التلميح
        deviceIcon.setAttribute('title', `العرض: ${deviceNames[deviceType] || 'تجاوب كامل'}`);
    }
}

// دالة تهيئة مفتش المتصفح (DevTools)
function initInspector() {
    // الحصول على العناصر
    const sidebar = document.getElementById('web-preview-sidebar');
    const iframe = document.getElementById('web-preview-iframe');
    const overlay = document.getElementById('inspector-overlay');
    const highlighter = document.getElementById('element-highlighter');
    const elementInfo = document.getElementById('element-info');
    const elementPath = document.getElementById('element-path');
    const sizeInfo = document.getElementById('selected-element-size');
    const mousePosition = document.getElementById('mouse-position');
    const gridOverlay = document.getElementById('grid-overlay');

    if (!sidebar || !iframe || !overlay) return;

    // تفعيل وضع المفتش
    sidebar.classList.add('inspector-mode');

    // إضافة زر التبديل بين الوضعين إذا لم يكن موجودًا
    if (!document.getElementById('inspector-mode-toggle')) {
        const toggleBtn = document.createElement('button');
        toggleBtn.id = 'inspector-mode-toggle';
        toggleBtn.className = 'inspector-mode-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-code"></i>';
        toggleBtn.title = 'تبديل وضع المفتش';
        toggleBtn.onclick = toggleInspectorMode;
        document.querySelector('.preview-container').appendChild(toggleBtn);
    }

    // تحديث أبعاد الجهاز
    updateDeviceDimensions();

    // أزرار شريط أدوات المفتش
    document.getElementById('inspector-cursor').addEventListener('click', () => setInspectorTool('cursor'));
    document.getElementById('inspector-inspect').addEventListener('click', () => setInspectorTool('inspect'));
    document.getElementById('inspector-responsive').addEventListener('click', () => setInspectorTool('responsive'));
    document.getElementById('inspector-ruler').addEventListener('click', () => setInspectorTool('ruler'));
    document.getElementById('inspector-toggle-grid').addEventListener('click', toggleGridOverlay);
    document.getElementById('inspector-toggle-outline').addEventListener('click', toggleElementsOutline);
    document.getElementById('rotate-device').addEventListener('click', rotateDevice);
    document.getElementById('device-zoom').addEventListener('change', changeDeviceZoom);
    document.getElementById('network-throttle').addEventListener('change', changeNetworkThrottle);

    // محاكاة المفتش - فحص العناصر
    let inspectMode = false;

    // دالة تفعيل أداة من أدوات المفتش
    function setInspectorTool(tool) {
        // إلغاء تنشيط جميع الأزرار
        document.querySelectorAll('.inspector-btn[data-active="true"]').forEach(btn => {
            btn.setAttribute('data-active', 'false');
        });

        // تنشيط الزر المختار
        document.getElementById(`inspector-${tool}`).setAttribute('data-active', 'true');

        // تنفيذ الإجراء المناسب للأداة
        switch (tool) {
            case 'cursor':
                disableInspectMode();
                break;
            case 'inspect':
                enableInspectMode();
                break;
            case 'responsive':
                enableResponsiveMode();
                break;
            case 'ruler':
                enableRulerMode();
                break;
        }
    }

    // تفعيل وضع الفحص
    function enableInspectMode() {
        inspectMode = true;
        overlay.style.display = 'block';
        iframe.style.pointerEvents = 'none';

        // إزالة مستمعي الأحداث السابقة إن وجدت
        document.querySelector('.preview-container').removeEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').removeEventListener('click', handleInspectClick);

        // إضافة مستمعي الأحداث
        document.querySelector('.preview-container').addEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').addEventListener('click', handleInspectClick);
    }

    // إلغاء وضع الفحص
    function disableInspectMode() {
        inspectMode = false;
        overlay.style.display = 'none';
        iframe.style.pointerEvents = 'auto';
        highlighter.style.display = 'none';
        elementInfo.style.display = 'none';

        // إزالة مستمعي الأحداث
        document.querySelector('.preview-container').removeEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').removeEventListener('click', handleInspectClick);

        // إعادة تعيين معلومات العنصر
        elementPath.textContent = '';
        sizeInfo.textContent = '';
    }

    // معالجة حدث تحريك الماوس في وضع الفحص
    function handleInspectMouseMove(e) {
        if (!inspectMode || !iframe.contentDocument) return;

        // حساب الموضع داخل الإطار
        const rect = iframe.getBoundingClientRect();
        const scale = parseFloat(iframe.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);

        // حساب إحداثيات الماوس داخل الإطار مع مراعاة المقياس
        const x = (e.clientX - rect.left) / scale;
        const y = (e.clientY - rect.top) / scale;

        // تحديث موضع الماوس في شريط المعلومات
        mousePosition.textContent = `${Math.round(x)}px × ${Math.round(y)}px`;

        // الحصول على العنصر تحت الماوس
        const element = getElementFromPoint(x, y);
        if (element) {
            highlightElement(element, x, y);
        }
    }

    // معالجة حدث النقر في وضع الفحص
    function handleInspectClick(e) {
        if (!inspectMode || !iframe.contentDocument) return;
        e.preventDefault();

        // حساب الموضع داخل الإطار
        const rect = iframe.getBoundingClientRect();
        const scale = parseFloat(iframe.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const x = (e.clientX - rect.left) / scale;
        const y = (e.clientY - rect.top) / scale;

        // الحصول على العنصر تحت الماوس
        const element = getElementFromPoint(x, y);
        if (element) {
            selectElement(element);
        }
    }

    // الحصول على العنصر عند نقطة معينة داخل الإطار
    function getElementFromPoint(x, y) {
        try {
            if (!iframe.contentDocument) return null;
            return iframe.contentDocument.elementFromPoint(x, y);
        } catch (e) {
            console.error('خطأ في الحصول على العنصر:', e);
            return null;
        }
    }

    // تظليل العنصر
    function highlightElement(element, x, y) {
        if (!element || element.nodeType !== 1) return;

        try {
            const rect = element.getBoundingClientRect();

            // ضبط موضع وحجم المظلل
            highlighter.style.display = 'block';
            highlighter.style.left = `${rect.left}px`;
            highlighter.style.top = `${rect.top}px`;
            highlighter.style.width = `${rect.width}px`;
            highlighter.style.height = `${rect.height}px`;

            // عرض معلومات العنصر
            elementInfo.style.display = 'block';
            elementInfo.style.left = `${x + 10}px`;
            elementInfo.style.top = `${y + 10}px`;

            // إعداد محتوى معلومات العنصر
            const tagName = element.tagName.toLowerCase();
            const classes = Array.from(element.classList).join('.');
            const id = element.id ? `#${element.id}` : '';

            // تنسيق العرض
            elementInfo.textContent = classes.length > 0 ?
                `${tagName}${id}.${classes}` :
                `${tagName}${id}`;

            // تحديث مسار العنصر في شريط المعلومات
            elementPath.textContent = getElementPath(element);

            // عرض أبعاد العنصر
            sizeInfo.textContent = `${Math.round(rect.width)} × ${Math.round(rect.height)}`;
        } catch (e) {
            console.error('خطأ في تظليل العنصر:', e);
        }
    }

    // اختيار عنصر للفحص
    function selectElement(element) {
        // يمكن إضافة مزيد من المنطق هنا
        console.log('العنصر المحدد:', element);

        // تحديث شريط المعلومات
        updateElementInfo(element);
    }

    // تحديث معلومات العنصر المحدد
    function updateElementInfo(element) {
        if (!element || element.nodeType !== 1) return;

        try {
            // تحديث مسار العنصر
            elementPath.textContent = getElementPath(element);

            // تحديث معلومات الحجم
            const rect = element.getBoundingClientRect();
            sizeInfo.textContent = `${Math.round(rect.width)} × ${Math.round(rect.height)}`;

            // تمييز العنصر بحدود
            const originalOutline = element.style.outline;
            element.style.outline = '2px solid #6464ff';

            // إزالة التمييز بعد ثانيتين
            setTimeout(() => {
                element.style.outline = originalOutline;
            }, 2000);
        } catch (e) {
            console.error('خطأ في تحديث معلومات العنصر:', e);
        }
    }

    // الحصول على مسار العنصر في DOM
    function getElementPath(element, maxDepth = 3) {
        if (!element || element.nodeType !== 1) return '';

        let path = [];
        let current = element;
        let depth = 0;

        while (current && current.nodeType === 1 && depth < maxDepth) {
            let selector = current.tagName.toLowerCase();

            if (current.id) {
                selector += `#${current.id}`;
            } else if (current.className) {
                const classList = Array.from(current.classList);
                if (classList.length > 0) {
                    selector += `.${classList[0]}`;
                }
            }

            path.unshift(selector);
            current = current.parentElement;
            depth++;
        }

        if (current && current.nodeType === 1) {
            path.unshift('...');
        }

        return path.join(' > ');
    }

    // تفعيل وضع التجاوب
    function enableResponsiveMode() {
        // تعيين الجهاز إلى "تجاوب كامل"
        const deviceSelector = document.getElementById('device-selector');
        if (deviceSelector) {
            deviceSelector.value = 'responsive';
            changeDeviceView('responsive');
        }
    }

    // تفعيل وضع القياس
    function enableRulerMode() {
        // تنفيذ منطق وضع القياس هنا
        alert('وضع القياس غير متاح حاليًا');
        setInspectorTool('cursor');
    }

    // تبديل إظهار شبكة التوجيه
    function toggleGridOverlay() {
        const button = document.getElementById('inspector-toggle-grid');
        const isActive = button.getAttribute('data-active') === 'true';

        button.setAttribute('data-active', !isActive);
        gridOverlay.style.display = isActive ? 'none' : 'block';
    }

    // تبديل إظهار حدود العناصر
    function toggleElementsOutline() {
        const button = document.getElementById('inspector-toggle-outline');
        const isActive = button.getAttribute('data-active') === 'true';

        button.setAttribute('data-active', !isActive);

        try {
            if (!iframe.contentDocument) return;

            if (isActive) {
                // إزالة الأنماط
                const style = iframe.contentDocument.getElementById('inspector-outline-style');
                if (style) style.remove();
            } else {
                // إضافة أنماط لإظهار حدود العناصر
                const style = document.createElement('style');
                style.id = 'inspector-outline-style';
                style.textContent = `
                    * {
                        outline: 1px dashed rgba(120, 120, 180, 0.4) !important;
                    }

                    *:hover {
                        outline: 1px dashed rgba(120, 120, 180, 0.8) !important;
                    }
                `;
                iframe.contentDocument.head.appendChild(style);
            }
        } catch (e) {
            console.error('خطأ في تبديل حدود العناصر:', e);
        }
    }

    // تدوير الجهاز
    function rotateDevice() {
        const deviceType = document.getElementById('device-selector').value;

        // لا نقوم بالتدوير في الوضع المتجاوب
        if (deviceType === 'responsive') return;

        // استبدال العرض بالارتفاع والعكس في المحاكي
        const deviceFrame = document.getElementById('device-frame');
        const tempWidth = deviceFrame.style.width;
        deviceFrame.style.width = deviceFrame.style.height;
        deviceFrame.style.height = tempWidth;

        // تحديث الأبعاد
        updateDeviceDimensions();

        // أعد حساب المقياس
        const containerWidth = document.querySelector('.preview-container').clientWidth - 80;
        const containerHeight = document.querySelector('.preview-container').clientHeight - 100;
        const deviceWidth = deviceFrame.offsetWidth;
        const deviceHeight = deviceFrame.offsetHeight;

        let scale = Math.min(
            containerWidth / deviceWidth,
            containerHeight / deviceHeight
        );

        scale = Math.min(Math.max(0.2, scale), 1);

        deviceFrame.style.transform = `scale(${scale})`;
    }

    // تغيير تكبير الجهاز
    function changeDeviceZoom(e) {
        const zoom = parseFloat(e.target.value);
        const deviceFrame = document.getElementById('device-frame');

        deviceFrame.style.transform = `scale(${zoom})`;
    }

    // تغيير محاكاة سرعة الشبكة
    function changeNetworkThrottle(e) {
        const throttle = e.target.value;
        console.log(`تم تغيير محاكاة الشبكة إلى: ${throttle}`);

        // يمكن إضافة منطق محاكاة السرعة هنا
        const networkIcon = document.querySelector('.network-select i');

        switch (throttle) {
            case 'online':
                networkIcon.className = 'fas fa-wifi';
                break;
            case 'fast3g':
                networkIcon.className = 'fas fa-signal';
                break;
            case 'slow3g':
                networkIcon.className = 'fas fa-signal';
                networkIcon.style.opacity = '0.6';
                break;
            case 'offline':
                networkIcon.className = 'fas fa-ban';
                break;
        }
    }

    // تحديث أبعاد الجهاز في العرض (دالة عامة)
    function updateDeviceDimensions() {
        const deviceFrame = document.getElementById('device-frame');
        const widthSpan = document.getElementById('device-width');
        const heightSpan = document.getElementById('device-height');
        if (deviceFrame && widthSpan && heightSpan) {
            // استخدام offsetWidth و offsetHeight لأنها تتضمن الحدود
            const width = deviceFrame.offsetWidth;
            const height = deviceFrame.offsetHeight;
            widthSpan.textContent = Math.round(width);
            heightSpan.textContent = Math.round(height);
        }
    }
}

// دالة تبديل وضع المفتش
function toggleInspectorMode() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // تبديل حالة وضع المفتش
    if (sidebar.classList.contains('inspector-mode')) {
        sidebar.classList.remove('inspector-mode');
        document.getElementById('inspector-overlay').style.display = 'none';
    } else {
        sidebar.classList.add('inspector-mode');
        initInspector();
    }
}

// دالة إظهار/إخفاء مفتش المتصفح في معاينة الويب
function toggleWebInspector() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // تبديل وضع المفتش
    if (sidebar.classList.contains('inspector-mode')) {
        sidebar.classList.remove('inspector-mode');
        document.getElementById('inspector-overlay').style.display = 'none';

        // حفظ الإعداد
        localStorage.setItem('inspectorModeActive', 'false');
    } else {
        sidebar.classList.add('inspector-mode');
        initInspector();

        // حفظ الإعداد
        localStorage.setItem('inspectorModeActive', 'true');
    }
}

// دالة لإضافة زر المفتش إلى شريط أدوات المعاينة
function addInspectorButton() {
    const previewControls = document.querySelector('.preview-controls');
    if (!previewControls) return;

    // تحقق من عدم وجود الزر مسبقًا
    if (document.getElementById('preview-inspector')) return;

    // إنشاء زر المفتش
    const inspectorBtn = document.createElement('button');
    inspectorBtn.id = 'preview-inspector';
    inspectorBtn.className = 'preview-btn';
    inspectorBtn.title = 'مفتش العناصر';
    inspectorBtn.innerHTML = '<i class="fas fa-search"></i>';
    inspectorBtn.onclick = toggleWebInspector;

    // إضافة الزر قبل زر الإغلاق
    const closeBtn = document.getElementById('preview-close');
    if (closeBtn) {
        previewControls.insertBefore(inspectorBtn, closeBtn);
    } else {
        previewControls.appendChild(inspectorBtn);
    }
}

// إضافة مستمع حدث DOMContentLoaded لتهيئة واجهة المفتش
document.addEventListener('DOMContentLoaded', function() {
    // إضافة زر المفتش إذا كانت نافذة المعاينة مفتوحة
    setTimeout(() => {
        const previewSidebar = document.getElementById('web-preview-sidebar');
        if (previewSidebar && previewSidebar.style.display !== 'none') {
            addInspectorButton();
        }

        // استعادة وضع المفتش إذا كان مفعلاً
        const inspectorMode = localStorage.getItem('inspectorModeActive') === 'true';
        if (inspectorMode && previewSidebar) {
            previewSidebar.classList.add('inspector-mode');
            initInspector();
        }
    }, 1000); // انتظر لتأكد من تحميل جميع العناصر
});

// إنشاء تيرمنال جديد
function createNewTerminal() {
    // حفظ محتوى التيرمنال الحالي
    saveTerminalContent();

    // إنشاء تيرمنال جديد
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        terminalElem.innerHTML = '<div class="terminal-welcome">New terminal session started.</div>';
    }

    // تنشيط تبويب Terminal
    const terminalTab = document.querySelector('.terminal-tab[data-tab="terminal"]');
    if (terminalTab) {
        const allTabs = document.querySelectorAll('.terminal-tab');
        allTabs.forEach(tab => tab.classList.remove('active'));
        terminalTab.classList.add('active');
    }
}

// تقسيم التيرمنال
function splitTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // تحقق إذا كان التيرمنال مقسم بالفعل
    if (executorFooter.classList.contains('split')) {
        // إذا كان مقسم بالفعل، نعيده إلى الوضع العادي
        executorFooter.classList.remove('split');

        // إزالة التيرمنال الثاني
        const secondTerminal = document.getElementById('second-terminal');
        if (secondTerminal) {
            secondTerminal.remove();
        }
    } else {
        // إضافة صنف split للتيرمنال
        executorFooter.classList.add('split');

        // إنشاء تيرمنال ثاني
        const terminalContainer = document.createElement('div');
        terminalContainer.className = 'terminal second-terminal';
        terminalContainer.id = 'second-terminal';
        terminalContainer.innerHTML = '<div class="terminal-welcome">Split terminal ready.</div>';

        // إضافة التيرمنال الثاني بعد التيرمنال الأول
        const firstTerminal = document.getElementById('executor-result');
        if (firstTerminal && firstTerminal.parentNode) {
            firstTerminal.parentNode.insertBefore(terminalContainer, firstTerminal.nextSibling);
        }
    }
}

// عرض خيارات إضافية للتيرمنال
function showTerminalOptions(event) {
    // إيقاف انتشار الحدث لمنع إغلاق القائمة فورًا
    event.stopPropagation();

    // التحقق مما إذا كانت القائمة موجودة بالفعل
    let optionsMenu = document.getElementById('terminal-options-menu');

    if (optionsMenu) {
        // إذا كانت القائمة مفتوحة بالفعل، نغلقها
        optionsMenu.remove();
        return;
    }

    // إنشاء قائمة الخيارات
    optionsMenu = document.createElement('div');
    optionsMenu.id = 'terminal-options-menu';
    optionsMenu.className = 'terminal-options-menu';

    // إضافة خيارات القائمة
    optionsMenu.innerHTML = `
        <div class="terminal-option" onclick="changeTerminalFont()">Change Font Size</div>
        <div class="terminal-option" onclick="changeTerminalTheme()">Change Terminal Theme</div>
        <div class="terminal-option" onclick="clearTerminalHistory()">Clear Terminal History</div>
        <div class="terminal-option" onclick="configureTerminal()">Terminal Settings</div>
    `;

    // تحديد موضع القائمة بالنسبة للزر
    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    // إضافة القائمة إلى DOM
    document.body.appendChild(optionsMenu);

    // تحديد موضع القائمة
    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    // إضافة مستمع حدث لإغلاق القائمة عند النقر في أي مكان آخر
    setTimeout(() => {
        document.addEventListener('click', closeTerminalOptions);
    }, 10);
}

// إغلاق قائمة خيارات التيرمنال
function closeTerminalOptions() {
    const optionsMenu = document.getElementById('terminal-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeTerminalOptions);
}

// وظائف قائمة الخيارات
function changeTerminalFont() {
    const fontSize = prompt('Enter terminal font size (px):', '13');
    if (fontSize) {
        const terminal = document.getElementById('executor-result');
        if (terminal) {
            terminal.style.fontSize = `${fontSize}px`;
            localStorage.setItem('terminalFontSize', fontSize);
        }
    }
    closeTerminalOptions();
}

function changeTerminalTheme() {
    const themes = ['Dark (Default)', 'Light', 'Blue', 'Green', 'Amber'];
    const theme = prompt(`Select terminal theme (0-${themes.length - 1}):\n${themes.map((t, i) => `${i}: ${t}`).join('\n')}`, '0');

    if (theme !== null) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            const terminal = document.getElementById('executor-result');
            if (terminal) {
                // إزالة جميع أصناف السمات السابقة
                terminal.classList.remove('theme-dark', 'theme-light', 'theme-blue', 'theme-green', 'theme-amber');

                // إضافة صنف السمة الجديدة
                const themeClass = `theme-${themes[themeIndex].toLowerCase().split(' ')[0]}`;
                terminal.classList.add(themeClass);
                localStorage.setItem('terminalTheme', themeClass);
            }
        }
    }
    closeTerminalOptions();
}

function clearTerminalHistory() {
    if (confirm('Are you sure you want to clear all terminal history?')) {
        window._terminalContent = null;
        localStorage.removeItem('terminalContent');

        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal history cleared.</div>';
        }
    }
    closeTerminalOptions();
}

function configureTerminal() {
    alert('Terminal settings will be available in the next update.');
    closeTerminalOptions();
}

// تعديل وظيفة فتح الملف لإضافة مساحة في نهاية المحرر
function openFile(fileId) {
    const file = workspace.files[fileId];
    if (!file) return;

    // ... existing code ...

    // تحديث الملف النشط
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة إذا لم يكن موجوداً بالفعل
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    // تحديث علامات تبويب الملفات
    updateFileTabs();

    // تحديث مستكشف الملفات لإظهار الملف النشط
    updateFileExplorer();

    // إظهار المحرر إذا كان مخفياً
    const executor = document.getElementById('code-executor');
    if (executor && !executor.classList.contains('visible')) {
        executor.classList.add('visible');
    }

    // تحديث محتوى المحرر
    const editorContainer = document.getElementById('editor-container');
    if (editorContainer) {
        try {
            // إذا كان المحرر موجوداً بالفعل، نقوم بتحديث المحتوى فقط
            if (monacoEditor) {
                const model = monaco.editor.createModel(file.content, file.language);
                monacoEditor.setModel(model);
            } else {
                // إنشاء محرر جديد
                monacoEditor = monaco.editor.create(editorContainer, {
                    value: file.content,
                    language: file.language,
                    theme: 'vs-dark',
                    automaticLayout: true,
                    minimap: {
                        enabled: true
                    },
                    scrollBeyondLastLine: true, // إضافة تمرير بعد السطر الأخير
                    padding: { // إضافة تباعد في المحرر
                        top: 10,
                        bottom: 20 // تباعد إضافي في الأسفل
                    },
                    lineNumbers: 'on',
                    roundedSelection: true,
                    scrollbar: {
                        useShadows: true,
                        verticalHasArrows: true,
                        horizontalHasArrows: true,
                        vertical: 'visible',
                        horizontal: 'visible',
                        verticalScrollbarSize: 12,
                        horizontalScrollbarSize: 12
                    }
                });

                // Set direction to LTR for code
                if (monacoEditor.updateOptions) {
                    monacoEditor.updateOptions({ direction: 'ltr' });
                }

                if (monacoEditor.getDomNode) {
                    monacoEditor.getDomNode().style.direction = 'ltr';
                }

                // Update content when changed
                if (monacoEditor.onDidChangeModelContent) {
                    monacoEditor.onDidChangeModelContent(function () {
                        if (fileId && workspace.files[fileId]) {
                            workspace.files[fileId].content = monacoEditor.getValue();
                        }
                    });
                }

                // Add cursor position tracking (VS Code status bar)
                monacoEditor.onDidChangeCursorPosition(function (e) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 0) {
                        statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                    }
                });

                // Add quick action buttons (like VS Code)
                const quickActions = document.createElement('div');
                quickActions.className = 'quick-actions';
                quickActions.innerHTML = `
                        <div class="quick-action" title="Split Editor" onclick="splitEditor()"><i class="fas fa-columns"></i></div>
                        <div class="quick-action" title="More Options" onclick="showEditorOptions(event)"><i class="fas fa-ellipsis-v"></i></div>
                    `;
                editorContainer.appendChild(quickActions);
            }

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    }

    // تحديث شريط الحالة
    updateStatusBar(file);
}

// تقسيم المحرر
function splitEditor() {
    alert('Editor split functionality will be available in the next update.');
}

// عرض خيارات المحرر
function showEditorOptions(event) {
    event.stopPropagation();

    let optionsMenu = document.getElementById('editor-options-menu');

    if (optionsMenu) {
        optionsMenu.remove();
        return;
    }

    optionsMenu = document.createElement('div');
    optionsMenu.id = 'editor-options-menu';
    optionsMenu.className = 'editor-options-menu';

    optionsMenu.innerHTML = `
        <div class="editor-option" onclick="changeEditorTheme()">Change Theme</div>
        <div class="editor-option" onclick="changeFontSize()">Change Font Size</div>
        <div class="editor-option" onclick="toggleMinimap()">Toggle Minimap</div>
        <div class="editor-option" onclick="formatDocument()">Format Document</div>
    `;

    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    document.body.appendChild(optionsMenu);

    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    setTimeout(() => {
        document.addEventListener('click', closeEditorOptions);
    }, 10);
}

function closeEditorOptions() {
    const optionsMenu = document.getElementById('editor-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeEditorOptions);
}

// وظائف خيارات المحرر
function changeEditorTheme() {
    const themes = ['vs-dark', 'vs', 'hc-black'];
    const theme = prompt(`Select editor theme (0-${themes.length - 1}):\n0: Dark\n1: Light\n2: High Contrast`, '0');

    if (theme !== null && monacoEditor) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            monaco.editor.setTheme(themes[themeIndex]);
            localStorage.setItem('editorTheme', themes[themeIndex]);
        }
    }
    closeEditorOptions();
}

function changeFontSize() {
    const fontSize = prompt('Enter editor font size (px):', '14');
    if (fontSize && monacoEditor) {
        const size = parseInt(fontSize);
        if (!isNaN(size) && size > 0) {
            monacoEditor.updateOptions({ fontSize: size });
            localStorage.setItem('editorFontSize', size);
        }
    }
    closeEditorOptions();
}

function toggleMinimap() {
    if (monacoEditor) {
        const currentState = monacoEditor.getOption(monaco.editor.EditorOption.minimap).enabled;
        monacoEditor.updateOptions({ minimap: { enabled: !currentState } });
        localStorage.setItem('editorMinimapEnabled', !currentState);
    }
    closeEditorOptions();
}

function formatDocument() {
    if (monacoEditor) {
        monacoEditor.getAction('editor.action.formatDocument').run();
    }
    closeEditorOptions();
}

// تحديث شريط الحالة
function updateStatusBar(file) {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const languageIndicator = statusBar.querySelector('.language-indicator .status-item-text');
    if (languageIndicator && file) {
        languageIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
    }
}

// دالة تصغير نافذة المعاينة
function minimizeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.classList.toggle('minimized');
        sidebar.classList.remove('maximized');

        // حفظ الحالة
        localStorage.setItem('webPreviewMinimized', sidebar.classList.contains('minimized'));
        localStorage.setItem('webPreviewMaximized', false);
    }
}

// دالة تكبير نافذة المعاينة
function maximizeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        // حفظ الموضع والحجم الحالي قبل التكبير إذا لم تكن مكبرة بالفعل
        if (!sidebar.classList.contains('maximized')) {
            const rect = sidebar.getBoundingClientRect();
            localStorage.setItem('webPreviewLastPosition', JSON.stringify({
                top: sidebar.style.top,
                left: sidebar.style.left,
                width: sidebar.style.width,
                height: sidebar.style.height
            }));
        }

        sidebar.classList.toggle('maximized');
        sidebar.classList.remove('minimized');

        // حفظ الحالة
        localStorage.setItem('webPreviewMaximized', sidebar.classList.contains('maximized'));
        localStorage.setItem('webPreviewMinimized', false);
    }
}

// دالة تغيير عرض الجهاز
function changeDeviceView(event) {
    const deviceType = event.target ? event.target.value : event;
    const deviceFrame = document.getElementById('device-frame');
    const container = document.getElementById('preview-container');
    const deviceWrapper = document.querySelector('.device-wrapper');
    const iframe = document.getElementById('web-preview-iframe');

    if (!deviceFrame || !container || !iframe) return;

    // إزالة جميع الفئات السابقة
    deviceFrame.className = 'device-frame';
    deviceFrame.style.width = '';
    deviceFrame.style.height = '';
    deviceFrame.style.transform = '';
    deviceFrame.style.border = 'none';
    deviceFrame.style.margin = '20px auto';
    deviceFrame.style.position = 'relative';
    container.style.overflow = 'auto';

    // تعريف إعدادات كل جهاز
    const deviceSettings = {
        responsive: { width: '100%', height: '100%', scale: 1, userAgent: null },
        desktop: { width: '1920px', height: '1080px', scale: 0.5, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        laptop: { width: '1366px', height: '768px', scale: 0.7, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        tablet: { width: '768px', height: '1024px', scale: 0.8, userAgent: "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X)" },
        mobile: { width: '414px', height: '896px', scale: 1, userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)" }
    };

    // تطبيق إعدادات الجهاز المحدد
    if (deviceType !== 'custom') {
        const settings = deviceSettings[deviceType] || deviceSettings.responsive;

        deviceFrame.classList.add(deviceType);

        if (deviceType === 'responsive') {
            // الوضع المتجاوب: ملء المساحة المتاحة
            deviceFrame.style.width = '100%';
            deviceFrame.style.height = '100%';
            deviceFrame.style.margin = '0';
            container.style.overflow = 'hidden';

            // إعادة ضبط أي تحويلات
            deviceFrame.style.transform = 'none';

            if (deviceWrapper) {
                deviceWrapper.style.padding = '0';
                deviceWrapper.style.overflow = 'visible';
            }
        } else {
            // تطبيق الأبعاد المحددة
            deviceFrame.style.width = settings.width;
            deviceFrame.style.height = settings.height;

            // تطبيق حدود وإطارات خاصة بالجهاز
            if (deviceType === 'mobile') {
                deviceFrame.style.borderRadius = '20px';
                deviceFrame.style.border = '5px solid #333';
            } else if (deviceType === 'tablet') {
                deviceFrame.style.borderRadius = '12px';
                deviceFrame.style.border = '5px solid #333';
            } else if (deviceType === 'laptop') {
                deviceFrame.style.borderRadius = '6px 6px 0 0';
                deviceFrame.style.border = '5px solid #333';
                deviceFrame.style.borderBottom = '40px solid #333';
            } else if (deviceType === 'desktop') {
                deviceFrame.style.borderRadius = '6px 6px 0 0';
                deviceFrame.style.border = '5px solid #1e1e1e';
                deviceFrame.style.borderBottom = '25px solid #1e1e1e';
            }

            // حساب الأبعاد الحقيقية للعنصر متضمنة الحدود والحشوات
            const frameTotalWidth = deviceFrame.offsetWidth;
            const frameTotalHeight = deviceFrame.offsetHeight;

            // حساب مساحة العرض المتاحة مع هوامش أمان
            const containerAvailWidth = container.clientWidth - 40;
            const containerAvailHeight = container.clientHeight - 40;

            // احتساب المقياس بشكل أكثر دقة مع مراعاة جميع الأبعاد
            let scale = Math.min(
                containerAvailWidth / frameTotalWidth,
                containerAvailHeight / frameTotalHeight
            );

            // حد أدنى وأقصى للتكبير/التصغير
            scale = Math.min(Math.max(0.2, scale), 1);

            // تطبيق المقياس المناسب بناءً على حجم الشاشة
            const windowWidth = window.innerWidth;
            if (windowWidth <= 576) {
                // الأجهزة الصغيرة جدًا
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 0.9);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            } else if (windowWidth <= 992) {
                // الأجهزة المتوسطة
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 1);
                } else if (deviceType === 'tablet') {
                    scale = Math.min(scale, 0.7);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            } else if (windowWidth <= 1400) {
                // الأجهزة الكبيرة
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 1);
                } else if (deviceType === 'tablet') {
                    scale = Math.min(scale, 0.8);
                } else if (deviceType === 'laptop') {
                    scale = Math.min(scale, 0.6);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            }

            // تطبيق التحويل مع مركزة العنصر
            deviceFrame.style.transform = `scale(${scale})`;
            deviceFrame.style.transformOrigin = 'center center';

            // ضمان عرض العنصر في المركز والمساحة المناسبة
            if (deviceWrapper) {
                deviceWrapper.style.display = 'flex';
                deviceWrapper.style.justifyContent = 'center';
                deviceWrapper.style.alignItems = 'center';
                deviceWrapper.style.padding = '20px';
                deviceWrapper.style.overflow = 'visible';
            }

            // زيادة الحشوة في حاوية المعاينة لضمان ظهور الجهاز بالكامل
            container.style.padding = '20px';
        }

        // إضافة meta viewport ديناميكي لمحاكاة الجهاز
        try {
            // انتظار تحميل iframe
            setTimeout(() => {
                if (iframe.contentDocument) {
                    // البحث عن meta viewport الحالي
                    let viewport = iframe.contentDocument.querySelector('meta[name="viewport"]');

                    // إنشاء واحد جديد إذا لم يكن موجودًا
                    if (!viewport) {
                        viewport = document.createElement('meta');
                        viewport.name = 'viewport';
                        iframe.contentDocument.head.appendChild(viewport);
                    }

                    // تعيين المحتوى المناسب للجهاز
                    if (deviceType === 'mobile') {
                        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
                    } else if (deviceType === 'tablet') {
                        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                    } else if (deviceType === 'responsive') {
                        viewport.content = 'width=device-width, initial-scale=1.0';
                    } else {
                        viewport.content = 'width=device-width, initial-scale=1.0';
                    }

                    // محاكاة User-Agent للجهاز
                    if (settings.userAgent && iframe.contentWindow.navigator) {
                        try {
                            // بدلاً من محاولة تغيير user-agent بشكل مباشر، نكتفي بتسجيله في console
                            console.log('محاكاة User-Agent:', settings.userAgent);
                            // لا نحاول تعديل الخاصية لأنها read-only
                            // Object.defineProperty(iframe.contentWindow.navigator, 'userAgent', {
                            //     get: function() { return settings.userAgent; }
                            // });
                        } catch (e) {
                            console.warn('خطأ في محاكاة user-agent:', e);
                        }
                    }

                    // إضافة CSS لضبط المحتوى داخل iframe
                    if (iframe.contentDocument.head && !iframe.contentDocument.head.querySelector('style[data-device-style]')) {
                        const styleElement = document.createElement('style');
                        styleElement.setAttribute('data-device-style', 'true');
                        styleElement.textContent = `
                            html, body {
                                margin: 0;
                                padding: 0;
                                width: 100%;
                                height: 100%;
                                overflow: auto;
                            }
                            /* للتأكد من تناسب المحتوى مع إطار الجهاز */
                            * {
                                box-sizing: border-box;
                            }
                        `;
                        iframe.contentDocument.head.appendChild(styleElement);
                    }

                    // ضبط نمط body للتأكد من عدم وجود هوامش داخلية
                    if (iframe.contentDocument.body) {
                        iframe.contentDocument.body.className = `device-${deviceType}`;
                        iframe.contentDocument.body.style.margin = '0';
                        iframe.contentDocument.body.style.padding = '0';
                        iframe.contentDocument.body.style.width = '100%';
                        iframe.contentDocument.body.style.height = '100%';
                        iframe.contentDocument.body.style.overflow = 'auto';
                    }
                }
            }, 300);
        } catch (e) {
            console.warn('خطأ في ضبط meta viewport:', e);
        }
    } else {
        // إذا كان مخصصًا، اطلب الأبعاد
        const width = prompt('أدخل العرض بالبكسل:', '1024');
        const height = prompt('أدخل الارتفاع بالبكسل:', '768');

        if (width && height) {
            deviceFrame.style.width = `${width}px`;
            deviceFrame.style.height = `${height}px`;

            // ضبط التكبير/التصغير للأبعاد المخصصة
            // احتساب الأبعاد الحقيقية للعنصر
            const frameTotalWidth = parseInt(width) + 20; // هامش 10px من كل جانب
            const frameTotalHeight = parseInt(height) + 20; // هامش 10px من أعلى وأسفل

            // حساب مساحة العرض المتاحة مع هوامش أمان
            const containerAvailWidth = container.clientWidth - 80;
            const containerAvailHeight = container.clientHeight - 100;

            let scale = Math.min(
                containerAvailWidth / frameTotalWidth,
                containerAvailHeight / frameTotalHeight
            );

            // حد أدنى وأقصى للتكبير/التصغير
            scale = Math.min(Math.max(0.2, scale), 1);

            if (scale < 1) {
                // تطبيق التحويل مع مركزة العنصر
                deviceFrame.style.transform = `scale(${scale})`;
                deviceFrame.style.transformOrigin = 'center center';
                deviceFrame.style.position = 'relative';
                deviceFrame.style.margin = '40px auto';
                deviceFrame.style.top = '0';

                // ضمان عرض العنصر في المركز
                if (deviceWrapper) {
                    deviceWrapper.style.display = 'flex';
                    deviceWrapper.style.justifyContent = 'center';
                    deviceWrapper.style.alignItems = 'center';
                    deviceWrapper.style.padding = '40px 20px';
                    deviceWrapper.style.overflow = 'visible';
                }

                // زيادة الحشوة في حاوية المعاينة لضمان ظهور الجهاز بالكامل
                container.style.padding = '40px 20px';
            }

            // إضافة meta viewport للإعدادات المخصصة
            try {
                setTimeout(() => {
                    if (iframe.contentDocument) {
                        // تصحيح موضع iframe لضمان ظهوره بالكامل
                        iframe.style.position = 'relative';
                        iframe.style.top = '0';

                        let viewport = iframe.contentDocument.querySelector('meta[name="viewport"]');
                        if (!viewport) {
                            viewport = document.createElement('meta');
                            viewport.name = 'viewport';
                            iframe.contentDocument.head.appendChild(viewport);
                        }
                        viewport.content = `width=${width}, initial-scale=1.0`;

                        // إضافة class مخصص للـ body وضبط الأنماط
                        if (iframe.contentDocument.body) {
                            iframe.contentDocument.body.className = 'device-custom';
                            iframe.contentDocument.body.style.width = `${width}px`;
                            iframe.contentDocument.body.style.height = `${height}px`;
                            iframe.contentDocument.body.style.margin = '0';
                            iframe.contentDocument.body.style.padding = '0';
                            iframe.contentDocument.body.style.overflow = 'auto';

                            // إضافة CSS لضبط المحتوى داخل iframe
                            const styleElement = document.createElement('style');
                            styleElement.textContent = `
                                html, body {
                                    margin: 0;
                                    padding: 0;
                                    width: 100%;
                                    height: 100%;
                                    overflow: auto;
                                }
                                /* للتأكد من تناسب المحتوى مع الإطار المخصص */
                                .device-custom * {
                                    box-sizing: border-box;
                                }
                            `;
                            if (!iframe.contentDocument.head.querySelector('style[data-device-style]')) {
                                styleElement.setAttribute('data-device-style', 'true');
                                iframe.contentDocument.head.appendChild(styleElement);
                            }
                        }
                    }
                }, 500);
            } catch (e) {
                console.warn('خطأ في ضبط meta viewport للإعدادات المخصصة:', e);
            }
        }
    }

    // حفظ الإعداد المحدد
    localStorage.setItem('webPreviewDeviceType', deviceType);

    // تحديث نص تلميح الأداة للزر
    updateDeviceButtonTooltip(deviceType);

    // تحديث أبعاد الجهاز في واجهة المستخدم
    updateDeviceDimensions();
}

// إضافة دالة updateDeviceDimensions في نهاية الملف كدالة عامة
function updateDeviceDimensions() {
    const deviceFrame = document.getElementById('device-frame');
    const widthSpan = document.getElementById('device-width');
    const heightSpan = document.getElementById('device-height');
    if (deviceFrame && widthSpan && heightSpan) {
        // استخدام offsetWidth و offsetHeight لأنها تتضمن الحدود
        const width = deviceFrame.offsetWidth;
        const height = deviceFrame.offsetHeight;
        widthSpan.textContent = Math.round(width);
        heightSpan.textContent = Math.round(height);
    }
}