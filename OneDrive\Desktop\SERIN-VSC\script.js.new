﻿// Ø­Ø§Ù„Ø© Ø§Ù„Ù…Ø­Ø§Ø¯Ø«Ø§Øª
let conversations = [];
let currentConversationId = null;
const codeContexts = {};
let monacoEditor = null;
let activeFileId = null;

// Ù†Ø¸Ø§Ù… Ø§Ù„Ù…Ù„ÙØ§Øª
let workspace = {
    files: {},
    folders: {
        'root': {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        }
    },
    currentPath: '/'
};

// ÙˆØ¸Ø§Ø¦Ù Ù†Ø¸Ø§Ù… Ø§Ù„Ù…Ù„ÙØ§Øª
function createFile(name, content, language, path = '/') {
    // ØªÙ†Ø¸ÙŠÙ Ø§Ù„Ù…Ø³Ø§Ø±
    path = path.replace(/\/+/g, '/');
    if (!path.endsWith('/')) path += '/';
    if (!path.startsWith('/')) path = '/' + path;

    // Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ù…Ù„Ù Ø¨Ù†ÙØ³ Ø§Ù„Ø§Ø³Ù… ÙÙŠ Ù†ÙØ³ Ø§Ù„Ù…Ø¬Ù„Ø¯ ÙˆØ¥Ø¶Ø§ÙØ© Ø±Ù‚Ù… Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
    let finalName = name;
    let counter = 1;

    // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙˆØ¬ÙˆØ¯Ø© Ø¨Ù†ÙØ³ Ø§Ù„Ø§Ø³Ù… ÙÙŠ Ù†ÙØ³ Ø§Ù„Ù…Ø¬Ù„Ø¯
    const filesInFolder = Object.values(workspace.files).filter(file =>
        file.path.startsWith(path) && file.path.substring(path.length) === name
    );

    // Ø¥Ø°Ø§ ÙˆØ¬Ø¯ Ù…Ù„Ù Ø¨Ù†ÙØ³ Ø§Ù„Ø§Ø³Ù…ØŒ Ù†Ø¶ÙŠÙ Ø±Ù‚Ù… Ù„Ù„Ù…Ù„Ù Ø§Ù„Ø¬Ø¯ÙŠØ¯
    if (filesInFolder.length > 0) {
        // Ø§Ø³ØªØ®Ø±Ø§Ø¬ Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù ÙˆØ§Ù„Ù„Ø§Ø­Ù‚Ø©
        const lastDotIndex = name.lastIndexOf('.');
        const baseName = lastDotIndex !== -1 ? name.substring(0, lastDotIndex) : name;
        const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

        // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø£Ø¹Ù„Ù‰ Ø±Ù‚Ù… Ù…ÙˆØ¬ÙˆØ¯ ÙÙŠ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ØªØ´Ø§Ø¨Ù‡Ø©
        const regex = new RegExp(`^${escapeRegExp(baseName)} \\((\\d+)\\)${escapeRegExp(extension)}$`);

        Object.values(workspace.files).forEach(file => {
            if (file.path.startsWith(path)) {
                const fileName = file.path.substring(path.length);
                const match = fileName.match(regex);
                if (match) {
                    const num = parseInt(match[1]);
                    if (num >= counter) {
                        counter = num + 1;
                    }
                }
            }
        });

        // Ø¥Ù†Ø´Ø§Ø¡ Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù Ø§Ù„Ø¬Ø¯ÙŠØ¯ Ù…Ø¹ Ø§Ù„Ø±Ù‚Ù…
        finalName = `${baseName} (${counter})${extension}`;
    }

    // Ø¥Ø¶Ø§ÙØ© ØªØ¹Ù„ÙŠÙ‚ Ø§Ù„Ù…Ù„Ù Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
    content = ensureFileComment(content, language, finalName, path);

    const fileId = 'file_' + Date.now() + Math.random().toString(36).substr(2, 5);
    const filePath = path + finalName;

    workspace.files[fileId] = {
        id: fileId,
        name: finalName,
        path: filePath,
        content: content,
        language: language,
        type: 'file'
    };

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ù„Ù Ø¥Ù„Ù‰ Ø§Ù„Ù…Ø¬Ù„Ø¯ Ù…Ø¹ Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ù…Ø¬Ù„Ø¯
    if (!workspace.folders[path]) {
        createFolder(path);
    }
    
    // ØªØ­Ù‚Ù‚ Ù…Ø±Ø© Ø£Ø®Ø±Ù‰ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø¨Ø¹Ø¯ Ù…Ø­Ø§ÙˆÙ„Ø© Ø¥Ù†Ø´Ø§Ø¦Ù‡
    if (workspace.folders[path] && workspace.folders[path].children) {
        workspace.folders[path].children.push(fileId);
    } else {
        // Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ø§Ù„Ù…Ø¬Ù„Ø¯ Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§ØŒ Ø£Ø¶Ù Ø§Ù„Ù…Ù„Ù Ø¥Ù„Ù‰ Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø§Ù„Ø¬Ø°Ø±
        if (!workspace.folders['root']) {
            workspace.folders['root'] = {
                id: 'root',
                name: 'root',
                path: '/',
                type: 'folder',
                children: []
            };
        }
        if (!workspace.folders['root'].children) {
            workspace.folders['root'].children = [];
        }
        workspace.folders['root'].children.push(fileId);
    }

    // Ø­ÙØ¸ Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª Ù…Ø¨Ø§Ø´Ø±Ø©
    saveWorkspace();

    return fileId;
}

function ensureFileComment(content, language, fileName, filePath) {
    // Ø§Ø­Ø°Ù Ø£ÙŠ ØªØ¹Ù„ÙŠÙ‚ Ù…Ø³Ø§Ø± ÙÙŠ Ø¨Ø¯Ø§ÙŠØ© Ø§Ù„Ù…Ù„Ù (JS, Python, HTML, CSS)
    content = content.replace(/^\s*(\/\/|#)\s*file:.*\n?/i, ''); // JS/Python
    content = content.replace(/^\s*<!--\s*file:.*?-->\s*\n?/i, ''); // HTML
    content = content.replace(/^\s*\/\*\s*file:.*?\*\/\s*\n?/is, ''); // CSS/JS block
        return content;
}

function createFolder(path) {
    const parts = path.split('/').filter(p => p);
    let currentPath = '/';

    for (const part of parts) {
        const newPath = currentPath + part + '/';
        if (!workspace.folders[newPath]) {
            const folderId = 'folder_' + Date.now() + Math.random().toString(36).substr(2, 5);
            workspace.folders[newPath] = {
                id: folderId,
                name: part,
                path: newPath,
                type: 'folder',
                children: []
            };

            // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø¥Ù„Ù‰ Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø§Ù„Ø£Ø¨ Ù…Ø¹ Ø§Ù„ØªØ­Ù‚Ù‚
            if (currentPath !== '/') {
                if (workspace.folders[currentPath] && workspace.folders[currentPath].children) {
                    workspace.folders[currentPath].children.push(folderId);
                }
            } else if (workspace.folders['root'] && workspace.folders['root'].children) {
                workspace.folders['root'].children.push(folderId);
            }
        }
        currentPath = newPath;
    }

    // Ø­ÙØ¸ Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª Ù…Ø¨Ø§Ø´Ø±Ø©
    saveWorkspace();
}

function updateFileExplorer() {
    const explorerContent = document.getElementById('explorer-content');
    explorerContent.innerHTML = '';

    // Breadcrumb Navigation
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'breadcrumb';

    const paths = workspace.currentPath.split('/').filter(p => p);
    let currentPath = '/';

    breadcrumb.innerHTML = `<span class="breadcrumb-item" onclick="navigateToFolder('/')">root</span>`;

    paths.forEach((part, index) => {
        currentPath += part + '/';
        breadcrumb.innerHTML += `
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" onclick="navigateToFolder('${currentPath}')">${part}</span>
                `;
    });

    explorerContent.appendChild(breadcrumb);

    // Explorer Sections - VS Code style
    const openEditorsSection = document.createElement('div');
    openEditorsSection.className = 'explorer-section';
    openEditorsSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>Ø§Ù„Ù…Ø­Ø±Ø±Ø§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" title="Ø­ÙØ¸ Ø§Ù„ÙƒÙ„"><i class="fas fa-save"></i></button>
                        <button class="explorer-section-action" title="Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„ÙƒÙ„" onclick="closeAllFiles()"><i class="fas fa-times"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="open-editors-content"></div>
            `;
    explorerContent.appendChild(openEditorsSection);

    // Populate Open Editors section - Ø¹Ø±Ø¶ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© ÙˆÙ„ÙŠØ³ ÙÙ‚Ø· Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
    const openEditorsContent = document.getElementById('open-editors-content');
    if (openEditorsContent) {
        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');

        if (openFiles.length > 0) {
            openFiles.forEach(fileId => {
                if (workspace.files[fileId]) {
                    const file = workspace.files[fileId];
                    const fileItem = document.createElement('div');
                    fileItem.className = `explorer-item file ${activeFileId === fileId ? 'active' : ''}`;

                    // ØªØ­Ù‚Ù‚ Ù…Ù…Ø§ Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙØªÙˆØ­Ù‹Ø§ Ø­Ø§Ù„ÙŠÙ‹Ø§
                    const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

                    fileItem.innerHTML = `
                                <div class="explorer-item-content">
                                    <span class="explorer-item-icon">
                                        ${getFileIcon(file.name)}
                                    </span>
                                    <span class="explorer-item-name">${file.name}</span>
                                </div>
                                <div class="explorer-item-actions">
                                    ${!isEditorVisible && activeFileId === fileId ?
                            `<button class="explorer-item-action" onclick="reopenEditor(event)" title="Ø¥Ø¹Ø§Ø¯Ø© ÙØªØ­ Ø§Ù„Ù…Ø­Ø±Ø±">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>` : ''
                        }
                                    <button class="explorer-item-action" onclick="closeFile('${file.id}', event)" title="Ø¥ØºÙ„Ø§Ù‚">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            `;
                    fileItem.onclick = (e) => {
                        if (!e.target.closest('.explorer-item-action')) {
                            // ØªÙ†Ø´ÙŠØ· Ù‡Ø°Ø§ Ø§Ù„Ù…Ù„Ù
                            activeFileId = file.id;
                            updateFileTabs();

                            // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ØºÙ„Ù‚Ù‹Ø§ ÙˆØ§Ù„Ù…Ù„Ù Ù‡Ùˆ Ø§Ù„Ù†Ø´Ø·ØŒ Ù†Ø¹ÙŠØ¯ ÙØªØ­Ù‡
                            if (!isEditorVisible && activeFileId === fileId) {
                                reopenEditor();
                            } else {
                                // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙØªÙˆØ­Ù‹Ø§ Ø£Ùˆ Ø§Ù„Ù…Ù„Ù Ù„ÙŠØ³ Ù‡Ùˆ Ø§Ù„Ù†Ø´Ø·ØŒ Ù†ÙØªØ­ Ø§Ù„Ù…Ù„Ù
                                openFile(file.id);
                            }
                        }
                    };
                    openEditorsContent.appendChild(fileItem);
                }
            });
        } else {
            // Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù‡Ù†Ø§Ùƒ Ù…Ù„Ù Ù…ÙØªÙˆØ­ØŒ Ù†Ø¹Ø±Ø¶ Ø±Ø³Ø§Ù„Ø©
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-editors-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">Ù„Ø§ ØªÙˆØ¬Ø¯ Ù…Ù„ÙØ§Øª Ù…ÙØªÙˆØ­Ø©</span>`;
            openEditorsContent.appendChild(emptyMessage);
        }
    }

    // Project Files Section
    const projectSection = document.createElement('div');
    projectSection.className = 'explorer-section';
    projectSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>${paths.length > 0 ? paths[paths.length - 1].toUpperCase() : 'Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ù…Ù„'}</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" onclick="createNewFile()" title="Ù…Ù„Ù Ø¬Ø¯ÙŠØ¯"><i class="fas fa-file"></i></button>
                        <button class="explorer-section-action" onclick="createNewFolder()" title="Ù…Ø¬Ù„Ø¯ Ø¬Ø¯ÙŠØ¯"><i class="fas fa-folder"></i></button>
                        <button class="explorer-section-action" onclick="refreshFileExplorer()" title="ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù…Ø³ØªÙƒØ´Ù"><i class="fas fa-sync"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="project-files-content"></div>
            `;
    explorerContent.appendChild(projectSection);

    // Populate Project Files section
    const projectFilesContent = document.getElementById('project-files-content');
    const currentFolder = workspace.folders[workspace.currentPath] || workspace.folders['root'];

    if (currentFolder && Array.isArray(currentFolder.children)) {
        // ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø£ÙŠ Ù…Ù„ÙØ§Øª Ø£Ùˆ Ù…Ø¬Ù„Ø¯Ø§Øª ÙÙŠ Ø§Ù„Ù…Ø³Ø§Ø± Ø§Ù„Ø­Ø§Ù„ÙŠ
        if (currentFolder.children.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-folder-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">Ø§Ù„Ù…Ø¬Ù„Ø¯ ÙØ§Ø±Øº</span>`;
            projectFilesContent.appendChild(emptyMessage);
            return;
        }

        // Sort: Folders first, then files alphabetically
        const sortedItems = [...currentFolder.children].sort((a, b) => {
            const isAFolder = a.startsWith('folder_');
            const isBFolder = b.startsWith('folder_');

            if (isAFolder && !isBFolder) return -1;
            if (!isAFolder && isBFolder) return 1;

            // Sort by name if both are folders or both are files
            const itemA = isAFolder ?
                Object.values(workspace.folders).find(f => f && f.id === a) :
                workspace.files[a];
            const itemB = isBFolder ?
                Object.values(workspace.folders).find(f => f && f.id === b) :
                workspace.files[b];

            if (itemA && itemB) {
                return itemA.name.localeCompare(itemB.name);
            }
            return 0;
        });

        sortedItems.forEach(childId => {
            let item;
            if (childId.startsWith('folder_')) {
                const folderObj = Object.values(workspace.folders).find(f => f && f.id === childId);
                item = folderObj ? workspace.folders[folderObj.path] : null;

                // ØªØ­Ù‚Ù‚ Ø¥Ø¶Ø§ÙÙŠ Ù…Ù† ØµØ­Ø© Ø§Ù„Ù…Ø¬Ù„Ø¯
                if (!item) {
                    console.warn('ØªÙ… Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ Ù…Ø¹Ø±Ù Ù…Ø¬Ù„Ø¯ ØºÙŠØ± ØµØ§Ù„Ø­:', childId);
                    return;
                }
            } else {
                item = workspace.files[childId];

                // ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ù…Ù„Ù
                if (!item) {
                    console.warn('ØªÙ… Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ Ù…Ø¹Ø±Ù Ù…Ù„Ù ØºÙŠØ± ØµØ§Ù„Ø­:', childId);
                    return;
                }
            }

            const itemElement = document.createElement('div');
            itemElement.className = `explorer-item ${item.type} ${activeFileId === item.id ? 'active' : ''}`;
            itemElement.innerHTML = `
                        <div class="explorer-item-content">
                            <span class="explorer-item-icon" style="order: -1">
                                ${item.type === 'folder' ? '<i class="fas fa-folder"></i>' : getFileIcon(item.name)}
                        </span>
                        <span class="explorer-item-name">${item.name}</span>
                        </div>
                        <div class="explorer-item-actions">
                            <button class="explorer-item-action" onclick="deleteFile('${item.id}', event)" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                            ${item.type === 'file' ? `
                            <button class="explorer-item-action" onclick="renameFile('${item.id}', event)" title="Rename">
                                <i class="fas fa-edit"></i>
                            </button>
                            ` : ''}
                        </div>
                    `;

            itemElement.onclick = (e) => {
                if (!e.target.closest('.explorer-item-action')) {
                    if (item.type === 'folder') {
                        navigateToFolder(item.path);
                    } else {
                        openFile(item.id);
                    }
                }
            };

            projectFilesContent.appendChild(itemElement);
        });
    } else {
        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø¬Ù„Ø¯ ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯ØŒ Ù†Ø¹Ø±Ø¶ Ø±Ø³Ø§Ù„Ø©
        console.warn('Ø§Ù„Ù…Ø¬Ù„Ø¯ ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯:', workspace.currentPath);
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-folder-message';
        emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">Ø§Ù„Ù…Ø¬Ù„Ø¯ ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯</span>`;
        projectFilesContent.appendChild(emptyMessage);
    }
}

// Helper function to get appropriate file icon based on extension
function getFileIcon(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'html': '<i class="fas fa-file-code" style="color: #e44d26;"></i>',
        'css': '<i class="fas fa-file-code" style="color: #264de4;"></i>',
        'js': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'json': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'ts': '<i class="fas fa-file-code" style="color: #007acc;"></i>',
        'py': '<i class="fas fa-file-code" style="color: #306998;"></i>',
        'php': '<i class="fas fa-file-code" style="color: #777bb4;"></i>',
        'md': '<i class="fas fa-file-alt" style="color: #03a9f4;"></i>',
        'txt': '<i class="fas fa-file-alt" style="color: #9e9e9e;"></i>',
        'jpg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'jpeg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'png': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'gif': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'svg': '<i class="fas fa-file-image" style="color: #ff9800;"></i>',
        'pdf': '<i class="fas fa-file-pdf" style="color: #f44336;"></i>',
        'zip': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
        'rar': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>'
    };

    return iconMap[extension] || '<i class="fas fa-file-code" style="color: #75beff;"></i>';
}

// Function to refresh the file explorer
function refreshFileExplorer() {
    updateFileExplorer();
}

function navigateToFolder(path) {
    // ØªÙ†Ø¸ÙŠÙ Ø§Ù„Ù…Ø³Ø§Ø±
    if (!path.startsWith('/')) {
        path = '/' + path;
    }
    if (!path.endsWith('/')) {
        path += '/';
    }

    // ØªØ£ÙƒØ¯ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ù…Ø¬Ù„Ø¯
    if (path !== '/' && !workspace.folders[path]) {
        console.error('Ø§Ù„Ù…Ø¬Ù„Ø¯ ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯:', path);
        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø¬Ù„Ø¯ ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯ØŒ Ù†Ø¹ÙˆØ¯ Ù„Ù„Ù…Ø¬Ù„Ø¯ Ø§Ù„Ø£Ø¨
        const parentPath = path.substring(0, path.lastIndexOf('/', path.length - 2) + 1);
        if (parentPath && workspace.folders[parentPath]) {
            path = parentPath;
        } else {
            path = '/';
        }
    }

    workspace.currentPath = path;

    // Ø­ÙØ¸ Ø§Ù„Ù…Ø³Ø§Ø± Ø§Ù„Ø­Ø§Ù„ÙŠ ÙÙŠ Ø§Ù„ØªØ®Ø²ÙŠÙ† Ø§Ù„Ù…Ø­Ù„ÙŠ
    localStorage.setItem('currentPath', path);

    updateFileExplorer();
}

// Add the VSCode-style status bar
function addStatusBar() {
    // Check if status bar already exists
    if (document.querySelector('.status-bar')) return;

    const codeExecutor = document.getElementById('code-executor');
    if (!codeExecutor) return;

    const statusBar = document.createElement('div');
    statusBar.className = 'status-bar';
    statusBar.innerHTML = `
                <div class="status-items-left">
            <div class="status-item icon-only-on-small">
                <i class="fas fa-code-branch"></i>
                <span class="status-item-text">main</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-sync"></i>
            </div>
            <div class="status-item terminal-toggle-btn">
                <i class="fas fa-terminal"></i>
                <span class="status-item-text">Terminal</span>
            </div>
            <div class="status-item hide-on-tiny">
                <i class="fas fa-bell"></i>
            </div>
                </div>
                <div class="status-items-right">
            <div class="status-item cursor-position always-show-text">
                <span class="status-item-text">Ln 1, Col 1</span>
            </div>
            <div class="status-item indent-setting icon-only-on-small">
                <i class="fas fa-indent"></i>
                <span class="status-item-text">Spaces: 4</span>
            </div>
            <div class="status-item hide-on-small">UTF-8</div>
            <div class="status-item language-indicator">
                <i class="fas fa-file-code"></i>
                <span class="status-item-text">JavaScript</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-check-circle"></i>
                <span class="status-item-text">Prettier</span>
            </div>
                </div>
            `;
    codeExecutor.appendChild(statusBar);

    // Make status items interactive
    statusBar.querySelectorAll('.status-item').forEach(item => {
        item.addEventListener('click', function() {
            // Show a tooltip or perform an action when clicked
            if (this.classList.contains('indent-setting')) {
                const options = ['Spaces: 2', 'Spaces: 4', 'Tabs: 4'];
                const currentIndex = options.findIndex(opt => {
                    const text = this.querySelector('.status-item-text');
                    return text && opt === text.textContent;
                });
                const nextIndex = (currentIndex + 1) % options.length;
                const text = this.querySelector('.status-item-text');
                if (text) {
                    text.textContent = options[nextIndex];
                }

                // Also update editor if available
                if (monacoEditor && monacoEditor.updateOptions) {
                    const tabSize = parseInt(options[nextIndex].split(':')[1]);
                    monacoEditor.updateOptions({
                        tabSize,
                        insertSpaces: options[nextIndex].startsWith('Spaces')
                    });
                }
            } else if (this.classList.contains('terminal-toggle-btn')) {
                // Toggle terminal visibility
                const executorFooter = document.querySelector('.executor-footer');
                if (executorFooter) {
                    if (executorFooter.classList.contains('hidden')) {
                        executorFooter.classList.remove('hidden');
                        localStorage.setItem('terminalState', 'open');
                    } else if (executorFooter.classList.contains('collapsed')) {
                        executorFooter.classList.remove('collapsed');
                        localStorage.setItem('terminalState', 'open');
                    } else {
                        executorFooter.classList.add('hidden');
                        localStorage.setItem('terminalState', 'hidden');
                    }
                }
            }
        });
    });

    // Update the language indicator based on current file
    const currentFile = workspace.files[activeFileId];
    if (currentFile && currentFile.language) {
        const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = currentFile.language.charAt(0).toUpperCase() + currentFile.language.slice(1);
        }
    }

    // Adjust status bar for screen size
    updateStatusBarResponsiveness();

    // Add window resize listener for responsive status bar
    window.addEventListener('resize', updateStatusBarResponsiveness);
}

// Function to update status bar based on screen width
function updateStatusBarResponsiveness() {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const width = window.innerWidth;

    // Very small screens - show only essential items
    if (width < 400) {
        statusBar.querySelectorAll('.status-item:not(.always-show-text)').forEach(item => {
            const text = item.querySelector('.status-item-text');
            if (text) text.style.display = 'none';
        });
    }
    // Small screens - show icons and some text
    else if (width < 600) {
        statusBar.querySelectorAll('.status-item.icon-only-on-small .status-item-text').forEach(text => {
            text.style.display = 'none';
        });
        statusBar.querySelectorAll('.status-item:not(.icon-only-on-small):not(.hide-on-small) .status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
    // Larger screens - show everything
    else {
        statusBar.querySelectorAll('.status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
}

function openFile(fileId) {
    const file = workspace.files[fileId];
    if (!file) return;

    // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
    activeFileId = fileId;

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ù„Ù Ø¥Ù„Ù‰ Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    const editorContainer = document.getElementById('editor-container');

    if (typeof monaco === 'undefined') {
        console.error('Monaco Editor not loaded. Please wait...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';
        setTimeout(() => openFile(fileId), 1000);
        return;
    }

    if (!monacoEditor) {
        try {
            const model = monaco.editor.createModel(
                file.content || '',
                file.language || 'plaintext'
            );

            // VS Code-like options
            monacoEditor = monaco.editor.create(editorContainer, {
                model: model,
                theme: 'vs-dark',
                automaticLayout: true,
                fontSize: 14,
                lineNumbers: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                renderLineHighlight: 'all',
                cursorBlinking: 'smooth',
                cursorSmoothCaretAnimation: true,
                smoothScrolling: true,
                wordWrap: 'on',
                formatOnPaste: true,
                formatOnType: true,
                suggest: {
                    showMethods: true,
                    showFunctions: true,
                    showConstructors: true,
                    showFields: true,
                    showVariables: true,
                    showClasses: true,
                    showStructs: true,
                    showInterfaces: true,
                    showModules: true,
                    showProperties: true,
                    showEvents: true,
                    showOperators: true,
                    showUnits: true,
                    showValues: true,
                    showConstants: true,
                    showEnums: true,
                    showEnumMembers: true,
                    showKeywords: true,
                    showWords: true,
                    showColors: true,
                    showFiles: true,
                    showReferences: true,
                    showFolders: true,
                    showTypeParameters: true,
                    showIssues: true,
                    showUsers: true,
                    showSnippets: true
                }
            });

            // Set direction to LTR for code
            if (monacoEditor.updateOptions) {
                monacoEditor.updateOptions({ direction: 'ltr' });
            }

            if (monacoEditor.getDomNode) {
                monacoEditor.getDomNode().style.direction = 'ltr';
            }

            // Update content when changed
            if (monacoEditor.onDidChangeModelContent) {
                // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ø§Ù„Ø³Ø§Ø¨Ù‚Ø© Ù„ØªØ¬Ù†Ø¨ Ø§Ù„ØªÙƒØ±Ø§Ø±
                if (monacoEditor.getModel()._contentChangedHandler) {
                    monacoEditor.getModel()._contentChangedHandler.dispose();
                }

                monacoEditor.getModel()._contentChangedHandler = monacoEditor.onDidChangeModelContent(function () {
                    // ØªØ£ÙƒØ¯ Ù…Ù† Ø£Ù† Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª ØªÙØ­ÙØ¸ ÙÙ‚Ø· Ù„Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø· Ø§Ù„Ø­Ø§Ù„ÙŠ
                    if (activeFileId && workspace.files[activeFileId] && activeFileId === fileId) {
                        workspace.files[activeFileId].content = monacoEditor.getValue();
                        // Ø­ÙØ¸ Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª Ø¹Ù†Ø¯ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ù…Ø­ØªÙˆÙ‰
                        saveWorkspace();
                    }
                });
            }

            // Add cursor position tracking (VS Code status bar)
            monacoEditor.onDidChangeCursorPosition(function (e) {
                const statusItems = document.querySelectorAll('.status-items-right .status-item');
                if (statusItems.length > 0) {
                    statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                }
            });

            // Add quick action buttons (like VS Code)
            const quickActions = document.createElement('div');
            quickActions.className = 'quick-actions';
            quickActions.innerHTML = `
                        <div class="quick-action" title="Split Editor"><i class="fas fa-columns"></i></div>
                        <div class="quick-action" title="More Options"><i class="fas fa-ellipsis-v"></i></div>
                    `;
            editorContainer.appendChild(quickActions);

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    } else {
        try {
            // ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ù†Ù…Ø§Ø°Ø¬
            let model = null;
            const modelUri = monaco.Uri.parse('inmemory://' + fileId);

            // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø§Ù„Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„Ø­Ø§Ù„ÙŠ
            const existingModels = monaco.editor.getModels();
            model = existingModels.find(m => m.uri && m.uri.toString() === modelUri.toString());

            // Ø¥Ù†Ø´Ø§Ø¡ Ù†Ù…ÙˆØ°Ø¬ Ø¬Ø¯ÙŠØ¯ Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
            if (!model) {
                try {
                    model = monaco.editor.createModel(
                        file.content || '',
                        file.language || 'plaintext',
                        modelUri
                    );
                } catch (e) {
                    console.warn('Error creating model, trying to reuse existing model:', e);
                    // Ù…Ø­Ø§ÙˆÙ„Ø© Ø§Ø³ØªØ®Ø¯Ø§Ù… Ø§Ù„Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„Ø­Ø§Ù„ÙŠ Ù„Ù„Ù…Ø­Ø±Ø±
                    model = monacoEditor.getModel();
                    if (model) {
                        model.setValue(file.content || '');
                        try {
                            if (file.language) {
                                monaco.editor.setModelLanguage(model, file.language);
                            }
                        } catch (langError) {
                            console.warn('Could not set language:', langError);
                        }
                    } else {
                        // Ø¥Ù†Ø´Ø§Ø¡ Ù†Ù…ÙˆØ°Ø¬ Ø¬Ø¯ÙŠØ¯ Ø¨Ø¯ÙˆÙ† URI Ù…Ø­Ø¯Ø¯
                        model = monaco.editor.createModel(file.content || '');
                    }
                }
            } else {
                // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù†Ù…ÙˆØ°Ø¬ Ø§Ù„Ù…ÙˆØ¬ÙˆØ¯
                model.setValue(file.content || '');
                try {
                    if (file.language) {
                        monaco.editor.setModelLanguage(model, file.language);
                    }
                } catch (langError) {
                    console.warn('Could not set language:', langError);
                }
            }

            // ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ù†Ù…ÙˆØ°Ø¬ Ù„Ù„Ù…Ø­Ø±Ø±
            monacoEditor.setModel(model);

            // ØªØ­Ø¯ÙŠØ« Ù…Ø¤Ø´Ø± Ø§Ù„Ù„ØºØ© ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
            try {
                if (file.language) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 3) {
                        statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                    }
                }
            } catch (e) {
                console.warn('Could not update language indicator:', e);
            }

            // ØªØ³Ø¬ÙŠÙ„ Ø­Ø¯Ø« ØªØºÙŠÙŠØ± Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ù„Ù„Ø­ÙØ¸ Ø§Ù„ØªÙ„Ù‚Ø§Ø¦ÙŠ
            if (monacoEditor.onDidChangeModelContent) {
                // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ø§Ù„Ø³Ø§Ø¨Ù‚Ø© Ù„ØªØ¬Ù†Ø¨ Ø§Ù„ØªÙƒØ±Ø§Ø±
                if (monacoEditor.getModel()._contentChangedHandler) {
                    monacoEditor.getModel()._contentChangedHandler.dispose();
                }

                monacoEditor.getModel()._contentChangedHandler = monacoEditor.onDidChangeModelContent(function () {
                    // ØªØ£ÙƒØ¯ Ù…Ù† Ø£Ù† Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª ØªÙØ­ÙØ¸ ÙÙ‚Ø· Ù„Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø· Ø§Ù„Ø­Ø§Ù„ÙŠ
                    if (activeFileId && workspace.files[activeFileId] && activeFileId === fileId) {
                        workspace.files[activeFileId].content = monacoEditor.getValue();
                        // Ø­ÙØ¸ Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª Ø¹Ù†Ø¯ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ù…Ø­ØªÙˆÙ‰
                        saveWorkspace();
                    }
                });
            }
        } catch (e) {
            console.error('Error changing editor model:', e);
        }
    }

    updateFileTabs();
    document.getElementById('code-executor').classList.add('visible');

    // Add VS Code status bar
    addStatusBar();

    // Focus editor after opening
    setTimeout(() => {
        if (monacoEditor && monacoEditor.focus) {
            try {
                monacoEditor.focus();
            } catch (e) {
                console.warn('Could not focus editor:', e);
            }
        }
    }, 100);
}

function renameFile(fileId, event) {
    event.stopPropagation();
    const file = workspace.files[fileId];
    if (!file) return;

    const newName = prompt('Ø£Ø¯Ø®Ù„ Ø§Ù„Ø§Ø³Ù… Ø§Ù„Ø¬Ø¯ÙŠØ¯:', file.name);
    if (newName && newName !== file.name) {
        file.name = newName;

        // ØªØ­Ø¯ÙŠØ« Ù…Ø³Ø§Ø± Ø§Ù„Ù…Ù„Ù Ø£ÙŠØ¶Ù‹Ø§
        const pathParts = file.path.split('/');
        pathParts.pop(); // Ø¥Ø²Ø§Ù„Ø© Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù‚Ø¯ÙŠÙ…
        pathParts.push(newName); // Ø¥Ø¶Ø§ÙØ© Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù Ø§Ù„Ø¬Ø¯ÙŠØ¯
        file.path = pathParts.join('/');

        updateFileExplorer();

        // Ø­ÙØ¸ Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª Ù…Ø¨Ø§Ø´Ø±Ø©
        saveWorkspace();
    }
}

function deleteFile(itemId, event) {
    event.stopPropagation();
    if (!confirm('Ù‡Ù„ Ø£Ù†Øª Ù…ØªØ£ÙƒØ¯ Ù…Ù† Ø­Ø°Ù Ù‡Ø°Ø§ Ø§Ù„Ø¹Ù†ØµØ±ØŸ')) return;

    if (itemId.startsWith('folder_')) {
        const folderObj = Object.values(workspace.folders).find(f => f && f.id === itemId);
        if (folderObj) {
            // Ø­Ø°Ù Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª ÙˆØ§Ù„Ù…Ø¬Ù„Ø¯Ø§Øª Ø¯Ø§Ø®Ù„ Ù‡Ø°Ø§ Ø§Ù„Ù…Ø¬Ù„Ø¯
            const folderPath = folderObj.path;

            // Ø­Ø°Ù Ø§Ù„Ù…Ù„ÙØ§Øª Ø¯Ø§Ø®Ù„ Ø§Ù„Ù…Ø¬Ù„Ø¯
            for (const fileId in workspace.files) {
                const file = workspace.files[fileId];
                if (file.path.startsWith(folderPath)) {
                    delete workspace.files[fileId];

                    // Ø¥Ø²Ø§Ù„Ø© Ù…Ù† Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
                    if (activeFileId === fileId) {
                        hideCodeExecutor();
                    }
                    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
                    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
                    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));
                }
            }

            // Ø­Ø°Ù Ø§Ù„Ù…Ø¬Ù„Ø¯Ø§Øª Ø§Ù„ÙØ±Ø¹ÙŠØ©
            for (const path in workspace.folders) {
                if (path !== folderPath && path.startsWith(folderPath)) {
                    delete workspace.folders[path];
                }
            }

            // Ø­Ø°Ù Ø§Ù„Ù…Ø¬Ù„Ø¯ Ù†ÙØ³Ù‡
            delete workspace.folders[folderPath];
        }
    } else {
        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù…Ø­Ø°ÙˆÙ Ù‡Ùˆ Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù…ÙØªÙˆØ­ Ø­Ø§Ù„ÙŠÙ‹Ø§ØŒ Ù†ØºÙ„Ù‚ Ø§Ù„Ù…Ø­Ø±Ø±
        if (activeFileId === itemId) {
            hideCodeExecutor();
        }

        delete workspace.files[itemId];

        // Ø­Ø°Ù Ø§Ù„Ù…Ù„Ù Ù…Ù† Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
        const updatedOpenFiles = openFiles.filter(id => id !== itemId);
        localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));
    }

    // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„Ø¹Ù†ØµØ± Ù…Ù† Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø§Ù„Ø£Ø¨ Ù…Ø¹ Ø§Ù„ØªØ­Ù‚Ù‚
    for (const folder of Object.values(workspace.folders)) {
        if (folder && Array.isArray(folder.children)) {
            const index = folder.children.indexOf(itemId);
            if (index !== -1) {
                folder.children.splice(index, 1);
                break;
            }
        }
    }

    updateFileExplorer();
    updateFileTabs();

    // Ø­ÙØ¸ Ø§Ù„ØªØºÙŠÙŠØ±Ø§Øª Ù…Ø¨Ø§Ø´Ø±Ø©
    saveWorkspace();
}

function toggleExplorer() {
    const explorer = document.getElementById('file-explorer');
    const toggle = document.getElementById('explorer-toggle');

    explorer.classList.toggle('visible');
    toggle.classList.toggle('active');
}

function createNewFile() {
    const fileName = prompt('Ø£Ø¯Ø®Ù„ Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù (Ù…Ø«Ø§Ù„: index.html):');
    if (fileName) {
        const lang = fileName.split('.').pop();
        const fileId = createFile(fileName, '', lang, workspace.currentPath);
        openFile(fileId);
        updateFileExplorer();
    }
}

function createNewFolder() {
    const folderName = prompt('Ø£Ø¯Ø®Ù„ Ø§Ø³Ù… Ø§Ù„Ù…Ø¬Ù„Ø¯:');
    if (folderName) {
        // ØªØ­Ù‚Ù‚ Ù…Ù† ØµØ­Ø© Ø§Ø³Ù… Ø§Ù„Ù…Ø¬Ù„Ø¯
        if (folderName.includes('/')) {
            alert('Ø§Ø³Ù… Ø§Ù„Ù…Ø¬Ù„Ø¯ Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø£Ù† ÙŠØ­ØªÙˆÙŠ Ø¹Ù„Ù‰ Ø§Ù„Ø±Ù…Ø² "/"');
            return;
        }

        // Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù…Ø³Ø§Ø± Ø§Ù„Ø¬Ø¯ÙŠØ¯
        let newPath = workspace.currentPath;
        if (!newPath.endsWith('/')) newPath += '/';
        newPath += folderName + '/';

        // ØªØ­Ù‚Ù‚ Ù…Ù† Ø¹Ø¯Ù… ÙˆØ¬ÙˆØ¯ Ù…Ø¬Ù„Ø¯ Ø¨Ù†ÙØ³ Ø§Ù„Ø§Ø³Ù…
        if (workspace.folders[newPath]) {
            alert('ÙŠÙˆØ¬Ø¯ Ù…Ø¬Ù„Ø¯ Ø¨Ù†ÙØ³ Ø§Ù„Ø§Ø³Ù… Ø¨Ø§Ù„ÙØ¹Ù„!');
            return;
        }

        // Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù…Ø¬Ù„Ø¯
        createFolder(newPath);

        // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù…Ø³ØªÙƒØ´Ù
        updateFileExplorer();

        // Ø§Ù„Ø§Ù†ØªÙ‚Ø§Ù„ Ø¥Ù„Ù‰ Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø§Ù„Ø¬Ø¯ÙŠØ¯
        navigateToFolder(newPath);
    }
}

function uploadFile() {
    document.getElementById('file-upload').click();
}

function handleFileUpload(event) {
    const files = event.target.files;
    if (!files.length) return;

    Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            const lang = file.name.split('.').pop();
            createFile(file.name, content, lang, workspace.currentPath);
            updateFileExplorer();
        };
        reader.readAsText(file);
    });
}

// ÙˆØ¸Ø§Ø¦Ù Ù„ÙˆØ§Ø¬Ù‡Ø© ØªØ´ØºÙŠÙ„ Ø§Ù„Ø£ÙƒÙˆØ§Ø¯
function showCodeExecutor(codeBlock, suggestedName, lang, content) {
    const executor = document.getElementById('code-executor');
    const editorContainer = document.getElementById('editor-container');

    // Create file if it doesn't exist
    const fileId = createFile(suggestedName, content.trim(), lang || 'javascript');
    activeFileId = fileId;

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ù„Ù Ø¥Ù„Ù‰ Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    if (typeof monaco === 'undefined') {
        console.log('Monaco Editor not loaded yet. Waiting...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';

        setTimeout(() => {
            showCodeExecutor(codeBlock, suggestedName, lang, content);
        }, 1000);
        return;
    }

    openFile(fileId);

    // Ensure terminal element exists
    let terminalContainer = document.querySelector('.executor-footer');
    if (!terminalContainer) {
        console.warn('Terminal container not found, creating it');
        terminalContainer = document.createElement('div');
        terminalContainer.className = 'executor-footer';
        executor.appendChild(terminalContainer);

        // Create terminal header
        const terminalHeader = document.createElement('div');
        terminalHeader.className = 'terminal-header';
        terminalHeader.innerHTML = `
            <span>Ø§Ù„Ù†ØªØ§Ø¦Ø¬</span>
            <button onclick="clearExecutorResult()">Ù…Ø³Ø­</button>
        `;
        terminalContainer.appendChild(terminalHeader);

        // Create terminal output area
        const terminalOutput = document.createElement('div');
        terminalOutput.className = 'terminal';
        terminalOutput.id = 'executor-result';
        terminalContainer.appendChild(terminalOutput);
    }

    updateTerminalHeader();
    addStatusBar();

    // Update language indicator in status bar
    if (lang) {
        const langIndicator = document.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = lang.charAt(0).toUpperCase() + lang.slice(1);
        }
    }

    executor.classList.add('visible');
}

// ØªØ­Ø³ÙŠÙ† ÙˆØ¸Ø§Ø¦Ù Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function updateTerminalHeader() {
    const terminalHeader = document.querySelector('.terminal-header');
    if (terminalHeader) {
        terminalHeader.innerHTML = `
                    <div class="terminal-tabs">
                <div class="terminal-tab active" data-tab="terminal">TERMINAL</div>
                <div class="terminal-tab" data-tab="output">OUTPUT</div>
                <div class="terminal-tab" data-tab="problems">PROBLEMS</div>
                <div class="terminal-tab" data-tab="debug">DEBUG CONSOLE</div>
                    </div>
                    <div class="terminal-actions">
                        <button title="Clear Terminal" onclick="clearExecutorResult()"><i class="fas fa-trash-alt"></i></button>
                <button title="Kill Terminal" onclick="killTerminal()"><i class="fas fa-times-circle"></i></button>
                        <button title="New Terminal" onclick="createNewTerminal()"><i class="fas fa-plus"></i></button>
                        <button title="Split Terminal" onclick="splitTerminal()"><i class="fas fa-columns"></i></button>
                        <button title="Toggle Terminal" onclick="toggleTerminal()"><i class="fas fa-chevron-down"></i></button>
                <button title="Maximize Terminal" onclick="maximizeTerminal()"><i class="fas fa-expand-alt"></i></button>
                <button title="More Options" onclick="showTerminalOptions(event)"><i class="fas fa-ellipsis-v"></i></button>
                <button title="Close Terminal" onclick="hideTerminal()"><i class="fas fa-times"></i></button>
                    </div>
                `;

        // Ø¬Ø¹Ù„ Ø§Ù„ØªØ¨ÙˆÙŠØ¨Ø§Øª Ù‚Ø§Ø¨Ù„Ø© Ù„Ù„Ù†Ù‚Ø±
        const tabs = terminalHeader.querySelectorAll('.terminal-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                tabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // ØªØ­Ø¯ÙŠØ« Ù…Ø­ØªÙˆÙ‰ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø¨Ù†Ø§Ø¡Ù‹ Ø¹Ù„Ù‰ Ø§Ù„ØªØ¨ÙˆÙŠØ¨ Ø§Ù„Ù†Ø´Ø·
                const tabName = this.dataset.tab;
                const terminalElem = document.getElementById('executor-result');

                switch(tabName) {
                    case 'problems':
                        terminalElem.innerHTML = '<div class="terminal-message success">No problems have been detected in the workspace.</div>';
                        break;
                    case 'output':
                        terminalElem.innerHTML = '<div class="terminal-message">Output channel is empty.</div>';
                        break;
                    case 'debug':
                        terminalElem.innerHTML = '<div class="terminal-message">Debug console is available in debug mode.</div>';
                        break;
                    case 'terminal':
                    default:
                        // Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ù…Ø­ØªÙˆÙ‰ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø§Ù„Ø£ØµÙ„ÙŠ Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹
                        if (window._terminalContent) {
                            terminalElem.innerHTML = window._terminalContent;
                        } else {
                            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal ready.</div>';
                        }
                }
            });
        });
    }
}

// Ø­ÙØ¸ Ù…Ø­ØªÙˆÙ‰ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ù‚Ø¨Ù„ ØªØºÙŠÙŠØ± Ø§Ù„ØªØ¨ÙˆÙŠØ¨
function saveTerminalContent() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem && terminalElem.innerHTML.trim() !== '') {
        window._terminalContent = terminalElem.innerHTML;
    }
}

// Ø¥Ø®ÙØ§Ø¡/Ø¥Ø¸Ù‡Ø§Ø± Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø¨Ø´ÙƒÙ„ ÙƒØ§Ù…Ù„
function toggleTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        if (executorFooter.classList.contains('collapsed')) {
            // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…Ø·ÙˆÙŠØ§Ù‹ØŒ Ù†ÙØªØ­Ù‡
            executorFooter.classList.remove('collapsed');
            localStorage.setItem('terminalState', 'open');
        } else if (executorFooter.classList.contains('hidden')) {
            // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…Ø®ÙÙŠØ§Ù‹ØŒ Ù†Ø¸Ù‡Ø±Ù‡
            executorFooter.classList.remove('hidden');
            localStorage.setItem('terminalState', 'open');
        } else {
            // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙØªÙˆØ­Ø§Ù‹ØŒ Ù†Ø·ÙˆÙŠÙ‡
            executorFooter.classList.add('collapsed');
            localStorage.setItem('terminalState', 'collapsed');
        }
    }
}

// Ø¥Ø®ÙØ§Ø¡ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ ØªÙ…Ø§Ù…Ø§Ù‹
function hideTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.add('hidden');
        localStorage.setItem('terminalState', 'hidden');
    }
}

// ØªÙƒØ¨ÙŠØ± Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function maximizeTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.toggle('maximized');
        if (executorFooter.classList.contains('maximized')) {
            localStorage.setItem('terminalState', 'maximized');
        } else {
            localStorage.setItem('terminalState', 'open');
        }
    }
}

// Ø¥ÙŠÙ‚Ø§Ù Ø¹Ù…Ù„ÙŠØ§Øª Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function killTerminal() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        saveTerminalContent();
        terminalElem.innerHTML += '<div class="terminal-message error">Process terminated.</div>';
    }
}

// Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø­Ø§Ù„Ø© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø¹Ù†Ø¯ ØªØ­Ù…ÙŠÙ„ Ø§Ù„ØµÙØ­Ø©
function restoreTerminalState() {
    const state = localStorage.getItem('terminalState') || 'open';
    const executorFooter = document.querySelector('.executor-footer');

    if (executorFooter) {
        // Ø¥Ø²Ø§Ù„Ø© Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø­Ø§Ù„Ø§Øª Ø£ÙˆÙ„Ø§Ù‹
        executorFooter.classList.remove('collapsed', 'hidden', 'maximized');
        executorFooter.style.height = '';

        // ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ø­Ø§Ù„Ø© Ø§Ù„Ù…Ø­ÙÙˆØ¸Ø©
        switch (state) {
            case 'collapsed':
                executorFooter.classList.add('collapsed');
                break;
            case 'hidden':
                executorFooter.classList.add('hidden');
                break;
            case 'maximized':
                executorFooter.classList.add('maximized');
                break;
            case 'custom':
                const savedHeight = localStorage.getItem('terminalHeight');
                if (savedHeight) {
                    executorFooter.style.height = savedHeight + 'px';
                }
                break;
        }
    }
}

// Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ù„ÙØªØ­/Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
function addTerminalToggleButton() {
    // ØªØ­Ù‚Ù‚ Ù…Ù…Ø§ Ø¥Ø°Ø§ ÙƒØ§Ù† Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø© Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    // ØªØ­Ù‚Ù‚ Ù…Ù…Ø§ Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ø²Ø± Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹ Ø¨Ø§Ù„ÙØ¹Ù„
    if (statusBar.querySelector('.terminal-toggle-btn')) return;

    // Ø¥Ù†Ø´Ø§Ø¡ Ø²Ø± Ø§Ù„ØªØ¨Ø¯ÙŠÙ„
    const toggleBtn = document.createElement('div');
    toggleBtn.className = 'status-item terminal-toggle-btn';
    toggleBtn.innerHTML = '<i class="fas fa-terminal"></i> Terminal';
    toggleBtn.title = 'Toggle Terminal (Ctrl+`)';
    toggleBtn.onclick = function() {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else if (executorFooter.classList.contains('collapsed')) {
                executorFooter.classList.remove('collapsed');
                localStorage.setItem('terminalState', 'open');
            } else {
                executorFooter.classList.add('hidden');
                localStorage.setItem('terminalState', 'hidden');
            }
        }
    };

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ø²Ø± Ø¥Ù„Ù‰ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
    const leftItems = statusBar.querySelector('.status-items-left');
    if (leftItems) {
        leftItems.appendChild(toggleBtn);
    } else {
        statusBar.appendChild(toggleBtn);
    }
}

// Ø¥Ø¶Ø§ÙØ© Ù…Ù‚Ø¨Ø¶ ØªØºÙŠÙŠØ± Ø­Ø¬Ù… Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function addTerminalResizer() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // ØªØ­Ù‚Ù‚ Ù…Ù…Ø§ Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ù‚Ø¨Ø¶ Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹ Ø¨Ø§Ù„ÙØ¹Ù„
    if (executorFooter.querySelector('.terminal-resizer')) return;

    // Ø¥Ù†Ø´Ø§Ø¡ Ù…Ù‚Ø¨Ø¶ Ø§Ù„ØªØºÙŠÙŠØ±
    const resizer = document.createElement('div');
    resizer.className = 'terminal-resizer';
    executorFooter.appendChild(resizer);

    // ØªÙØ¹ÙŠÙ„ ÙˆØ¸ÙŠÙØ© Ø§Ù„Ø³Ø­Ø¨
    let startY, startHeight;

    resizer.addEventListener('mousedown', function(e) {
        startY = e.clientY;
        startHeight = parseInt(getComputedStyle(executorFooter).height);
        document.addEventListener('mousemove', doDrag, false);
        document.addEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = 'row-resize';
        e.preventDefault();
    });

    function doDrag(e) {
        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ø§Ø±ØªÙØ§Ø¹ Ø§Ù„Ø¬Ø¯ÙŠØ¯ (Ø§Ù„Ø³Ø­Ø¨ Ù„Ø£Ø¹Ù„Ù‰ ÙŠÙ‚Ù„Ù„ Ø§Ù„Ø§Ø±ØªÙØ§Ø¹)
        const newHeight = startHeight - (e.clientY - startY);
        // ØªØ­Ø¯ÙŠØ¯ Ø­Ø¯ Ø£Ø¯Ù†Ù‰ ÙˆØ£Ù‚ØµÙ‰ Ù„Ù„Ø§Ø±ØªÙØ§Ø¹
        const minHeight = 100; // Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ø¯Ù†Ù‰ Ù„Ù„Ø§Ø±ØªÙØ§Ø¹
        const maxHeight = window.innerHeight * 0.8; // 80% Ù…Ù† Ø§Ø±ØªÙØ§Ø¹ Ø§Ù„Ù†Ø§ÙØ°Ø©

        if (newHeight > minHeight && newHeight < maxHeight) {
            executorFooter.style.height = newHeight + 'px';
            executorFooter.classList.remove('collapsed', 'maximized');
            localStorage.setItem('terminalHeight', newHeight);
            localStorage.setItem('terminalState', 'custom');
        }
    }

    function stopDrag() {
        document.removeEventListener('mousemove', doDrag, false);
        document.removeEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = '';
    }
}

// ØªØ­Ø¯ÙŠØ« Ø¯Ø§Ù„Ø© openFile Ù„Ø¥Ø¶Ø§ÙØ© Ù…Ù‚Ø¨Ø¶ ØªØºÙŠÙŠØ± Ø§Ù„Ø­Ø¬Ù…
const originalOpenFile = openFile;
openFile = function(fileId) {
    originalOpenFile(fileId);

    // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ ÙˆÙ…Ù‚Ø¨Ø¶ ØªØºÙŠÙŠØ± Ø§Ù„Ø­Ø¬Ù… Ø¨Ø¹Ø¯ ÙØªØ­ Ø§Ù„Ù…Ù„Ù
    setTimeout(() => {
        addTerminalToggleButton();
        addTerminalResizer();
        restoreTerminalState();
        updateTerminalHeader();
    }, 200);
};

// ØªØ­Ø¯ÙŠØ« Ø¯Ø§Ù„Ø© addStatusBar Ù„ØªØ¶ÙŠÙ Ø²Ø± Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
const originalAddStatusBar = addStatusBar;
addStatusBar = function() {
    originalAddStatusBar();
    addTerminalToggleButton();
};

// ØªØ¹Ø¯ÙŠÙ„ Ø¯Ø§Ù„Ø© clearExecutorResult Ù„Ù„Ø­ÙØ§Ø¸ Ø¹Ù„Ù‰ ØªÙ†Ø³ÙŠÙ‚ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function clearExecutorResult() {
    const resultElement = document.getElementById('executor-result');
    if (resultElement) {
        resultElement.innerHTML = '<div class="terminal-welcome">Terminal cleared.</div>';
        window._terminalContent = resultElement.innerHTML;
    }
}

// Ø¥Ø¶Ø§ÙØ© Ø§Ø®ØªØµØ§Ø± Ù„ÙˆØ­Ø© Ø§Ù„Ù…ÙØ§ØªÙŠØ­ Ù„Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ (Ctrl+`)
document.addEventListener('keydown', function(e) {
    // Ctrl+` Ù„ÙØªØ­/Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
    if (e.ctrlKey && (e.key === '`' || e.key === 'Backquote')) {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else {
        executorFooter.classList.toggle('collapsed');
                localStorage.setItem('terminalState',
                    executorFooter.classList.contains('collapsed') ? 'collapsed' : 'open');
    }
            e.preventDefault();
}
    }
});

function updateFileTabs() {
    const tabsContainer = document.getElementById('file-tabs');
    tabsContainer.innerHTML = '';

    // ØªØ¹Ø¯ÙŠÙ„: Ø¹Ø±Ø¶ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ø¨Ø¯Ù„Ø§Ù‹ Ù…Ù† Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø· ÙÙ‚Ø·
    // Ù†Ø­ØªÙØ¸ Ø¨Ù‚Ø§Ø¦Ù…Ø© Ù…Ù† Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
    const openFiles = [];

    // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù‡Ù†Ø§Ùƒ Ù…Ù„Ù Ù†Ø´Ø·ØŒ Ù†Ø¶ÙŠÙÙ‡ Ø£ÙˆÙ„Ø§Ù‹
    if (activeFileId && workspace.files[activeFileId]) {
        openFiles.push(activeFileId);
    }

    // Ù†Ø¶ÙŠÙ Ø¨Ø§Ù‚ÙŠ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ù…Ù† localStorage
    const savedOpenFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    savedOpenFiles.forEach(fileId => {
        if (workspace.files[fileId] && !openFiles.includes(fileId)) {
            openFiles.push(fileId);
        }
    });

    // Ø¹Ø±Ø¶ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© ÙƒØªØ¨ÙˆÙŠØ¨Ø§Øª
    openFiles.forEach(fileId => {
        const file = workspace.files[fileId];
        const tab = document.createElement('div');
        tab.className = `file-tab ${fileId === activeFileId ? 'active' : ''}`;

        // ØªØ­Ù‚Ù‚ Ù…Ù…Ø§ Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙØªÙˆØ­Ø§Ù‹ Ø­Ø§Ù„ÙŠÙ‹Ø§
        const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

        tab.innerHTML = `
                    <span class="file-tab-name">${file.name}</span>
                    ${!isEditorVisible && fileId === activeFileId ?
                `<span class="file-tab-open" onclick="reopenEditor(event)" title="Ø¥Ø¹Ø§Ø¯Ø© ÙØªØ­ Ø§Ù„Ù…Ø­Ø±Ø±">
                            <i class="fas fa-external-link-alt"></i>
                        </span>` : ''
            }
                    <span class="file-tab-close" onclick="closeFile('${file.id}', event)">Ã—</span>
                `;
        tab.onclick = (e) => {
            if (!e.target.closest('.file-tab-close') && !e.target.closest('.file-tab-open')) {
                // ØªÙ†Ø´ÙŠØ· Ù‡Ø°Ø§ Ø§Ù„Ù…Ù„Ù
                activeFileId = file.id;
                updateFileTabs();

                // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ØºÙ„Ù‚Ù‹Ø§ØŒ Ù†Ø¹ÙŠØ¯ ÙØªØ­Ù‡
                if (!document.getElementById('code-executor').classList.contains('visible')) {
                    reopenEditor();
                } else {
                    // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙØªÙˆØ­Ù‹Ø§ØŒ Ù†Ù‚ÙˆÙ… Ø¨ØªØ­Ù…ÙŠÙ„ Ù‡Ø°Ø§ Ø§Ù„Ù…Ù„Ù
                    openFile(file.id);
                }
            }
        };
        tabsContainer.appendChild(tab);
    });

    // Ø­ÙØ¸ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© ÙÙŠ localStorage
    localStorage.setItem('openFiles', JSON.stringify(openFiles));

    // ØªÙ…Ø±ÙŠØ± Ø§Ù„ØªØ¨ÙˆÙŠØ¨ Ø§Ù„Ù†Ø´Ø· Ø¥Ù„Ù‰ Ù…Ù†Ø·Ù‚Ø© Ø§Ù„Ø¹Ø±Ø¶
    setTimeout(() => {
        const activeTab = tabsContainer.querySelector('.file-tab.active');
        if (activeTab) {
            activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }, 100);

    // Call our addStatusBar function instead of duplicating code
    addStatusBar();
}

// Ø¯Ø§Ù„Ø© Ù„Ø¥Ø¹Ø§Ø¯Ø© ÙØªØ­ Ø§Ù„Ù…Ø­Ø±Ø± Ù„Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
function reopenEditor(event) {
    if (event) {
        event.stopPropagation();
    }

    if (activeFileId && workspace.files[activeFileId]) {
        const file = workspace.files[activeFileId];

        // Ø¥Ø¹Ø§Ø¯Ø© ÙØªØ­ Ø§Ù„Ù…Ø­Ø±Ø± Ø¨Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
        document.getElementById('code-executor').classList.add('visible');

        // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù…Ø­Ø±Ø± Ø¨Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ø­Ø§Ù„ÙŠ
        if (monacoEditor) {
            try {
                // Ø¥ÙŠØ¬Ø§Ø¯ Ø£Ùˆ Ø¥Ù†Ø´Ø§Ø¡ Ù†Ù…ÙˆØ°Ø¬
                let model = null;
                const existingModels = monaco.editor.getModels();

                if (existingModels && existingModels.length > 0) {
                    model = existingModels.find(m => m.uri && m.uri.path === '/' + activeFileId);
                }

                if (!model) {
                    try {
                        model = monaco.editor.createModel(
                            file.content || '',
                            file.language || 'plaintext',
                            monaco.Uri.parse('inmemory://' + activeFileId)
                        );
                    } catch (e) {
                        console.error('Error creating model:', e);
                        model = monaco.editor.createModel(file.content || '');
                    }
                }

                // ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ù†Ù…ÙˆØ°Ø¬ ÙˆØªØ­Ø¯ÙŠØ«Ù‡
                monacoEditor.setModel(model);
                monacoEditor.setValue(file.content || '');

                // Ù…Ø­Ø§ÙˆÙ„Ø© ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ù„ØºØ©
                try {
                    if (file.language && monaco.editor.setModelLanguage) {
                        monaco.editor.setModelLanguage(model, file.language);

                        // ØªØ­Ø¯ÙŠØ« Ù…Ø¤Ø´Ø± Ø§Ù„Ù„ØºØ© ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
                        const statusItems = document.querySelectorAll('.status-items-right .status-item');
                        if (statusItems.length > 3) {
                            statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                        }
                    }
                } catch (e) {
                    console.warn('Could not set language:', e);
                }

                // ØªØ±ÙƒÙŠØ² Ø§Ù„Ù…Ø­Ø±Ø±
                setTimeout(() => {
                    if (monacoEditor && monacoEditor.focus) {
                        try {
                            monacoEditor.focus();
                        } catch (e) {
                            console.warn('Could not focus editor:', e);
                        }
                    }
                }, 100);
            } catch (e) {
                console.error('Error reopening editor:', e);
            }
        } else {
            // Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§ØŒ Ù†Ø³ØªØ®Ø¯Ù… openFile
            openFile(activeFileId);
        }

        // ØªØ­Ø¯ÙŠØ« Ø¹Ù„Ø§Ù…Ø§Øª Ø§Ù„ØªØ¨ÙˆÙŠØ¨
        updateFileTabs();
    }
}

// Ø¥Ø¶Ø§ÙØ© Ø£Ù†Ù…Ø§Ø· CSS Ù„Ù„Ø²Ø± Ø§Ù„Ø¬Ø¯ÙŠØ¯
document.addEventListener('DOMContentLoaded', function () {
    const style = document.createElement('style');
    style.textContent = `
                .file-tab-open {
                    margin-left: 8px;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 3px;
                    color: var(--text-dim);
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    cursor: pointer;
                }

                .file-tab:hover .file-tab-open {
                    opacity: 1;
                }

                .file-tab-open:hover {
                    color: white;
                    background: rgba(94, 53, 177, 0.5);
                }
            `;
    document.head.appendChild(style);
});

function closeFile(fileId, event) {
    if (event) {
        event.stopPropagation();
    }

    // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„Ù…Ù„Ù Ù…Ù† Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));

    // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù…ØºÙ„Ù‚ Ù‡Ùˆ Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
    if (activeFileId === fileId) {
        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù‡Ù†Ø§Ùƒ Ù…Ù„ÙØ§Øª Ù…ÙØªÙˆØ­Ø© Ø£Ø®Ø±Ù‰ØŒ Ù†Ø¬Ø¹Ù„ Ø§Ù„Ù…Ù„Ù Ø§Ù„Ø£ÙˆÙ„ Ù‡Ùˆ Ø§Ù„Ù†Ø´Ø·
        if (updatedOpenFiles.length > 0) {
            activeFileId = updatedOpenFiles[0];

            // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙØªÙˆØ­Ù‹Ø§ØŒ Ù†ÙØªØ­ Ø§Ù„Ù…Ù„Ù Ø§Ù„Ø¬Ø¯ÙŠØ¯
            if (document.getElementById('code-executor').classList.contains('visible')) {
                openFile(activeFileId);
            }
        } else {
            // Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù‡Ù†Ø§Ùƒ Ù…Ù„ÙØ§Øª Ù…ÙØªÙˆØ­Ø© Ø£Ø®Ø±Ù‰ØŒ Ù†ØºÙ„Ù‚ Ø§Ù„Ù…Ø­Ø±Ø±
            activeFileId = null;
            document.getElementById('code-executor').classList.remove('visible');

            // ØªÙ†Ø¸ÙŠÙ iframe Ø§Ù„ØªÙ†ÙÙŠØ°
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
                window._executorIframe = null;
            }
        }
    }

    updateFileTabs();
    updateFileExplorer();
}

function hideCodeExecutor() {
    document.getElementById('code-executor').classList.remove('visible');
    // Ù„Ø§ Ù†Ù‚ÙˆÙ… Ø¨Ø¥Ù„ØºØ§Ø¡ ØªØ¹ÙŠÙŠÙ† activeFileId Ù‡Ù†Ø§ Ù„ÙƒÙŠ ØªØ¨Ù‚Ù‰ Ø§Ù„ØªØ¨ÙˆÙŠØ¨Ø© Ù†Ø´Ø·Ø©
    // activeFileId = null;

    // ØªÙ†Ø¸ÙŠÙ iframe Ø§Ù„ØªÙ†ÙÙŠØ°
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    // ØªØ­Ø¯ÙŠØ« Ù…Ø³ØªÙƒØ´Ù Ø§Ù„Ù…Ù„ÙØ§Øª Ù„Ø¥Ø¸Ù‡Ø§Ø± Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø· ÙÙŠ Ù‚Ø³Ù… Ø§Ù„Ù…Ø­Ø±Ø±Ø§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
    updateFileExplorer();
}

async function runCodeInExecutor() {
    const resultElement = document.getElementById('executor-result');
    resultElement.textContent = 'Ø¬Ø§Ø±ÙŠ Ø§Ù„ØªØ´ØºÙŠÙ„...\n';

    // Debug: Check if the terminal element exists
    if (!resultElement) {
        console.error('Terminal element not found (executor-result)');
        return;
    }

    if (!activeFileId || !workspace.files[activeFileId]) {
        resultElement.textContent += 'Ù„Ø§ ÙŠÙˆØ¬Ø¯ Ù…Ù„Ù Ù†Ø´Ø· Ù„Ù„ØªÙ†ÙÙŠØ°.\n';
        return;
    }
    const file = workspace.files[activeFileId];
    const lang = (file.language || '').toLowerCase();

    // Log debugging info
    console.log(`Executing file: ${file.name}, Language: ${lang}`);

    // Ø¯Ø¹Ù… HTML: Ø¹Ø±Ø¶ Ø§Ù„Ù†ØªÙŠØ¬Ø© ÙÙŠ Ù†Ø§ÙØ°Ø© Ø¬Ø§Ù†Ø¨ÙŠØ© (web-preview-sidebar)
    if (lang === 'html') {
        resultElement.textContent += 'Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©...\n';

        // Ø§Ø­ØµÙ„ Ø¹Ù„Ù‰ Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ù…Ù„Ù
        const htmlContent = file.content;

        // ØªØ­Ù‚Ù‚ Ù…Ù† Ø§Ù„Ù…ØµØ§Ø¯Ø± Ø§Ù„Ù…Ø±ØªØ¨Ø·Ø© (CSS, JS, ØµÙˆØ±)
        const linkedFiles = findLinkedFiles(htmlContent);
        console.log('Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…Ø±ØªØ¨Ø·Ø©:', linkedFiles);

        // Ø¥Ù†Ø´Ø§Ø¡ Ø®Ø±ÙŠØ·Ø© Ù„Ù„Ù…ØµØ§Ø¯Ø± Ø§Ù„Ù…Ø¶Ù…Ù†Ø©
        const resourceMap = createResourceMap(linkedFiles);
        console.log('Ø®Ø±ÙŠØ·Ø© Ø§Ù„Ù…ØµØ§Ø¯Ø±:', resourceMap);

        // Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø±ÙˆØ§Ø¨Ø· Ø§Ù„Ù…ØµØ§Ø¯Ø± Ø¨Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ù…Ø¶Ù…Ù† Ø£Ùˆ Ø§Ù„Ø¨ÙŠØ§Ù†Ø§Øª Ø§Ù„Ù…Ø´ÙØ±Ø© base64
        const processedHtml = replaceLinkedResources(htmlContent, resourceMap);

        // Ø¹Ø±Ø¶ Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
        const sidebar = document.getElementById('web-preview-sidebar');
        const iframe = document.getElementById('web-preview-iframe');

        if (sidebar && iframe) {
            // Ø­ÙØ¸ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¹Ø±Ø¶ Ø§Ù„Ø­Ø§Ù„ÙŠØ© Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù†Ø§ÙØ°Ø© Ù…ÙØªÙˆØ­Ø©
            let currentDeviceType = 'responsive';
            let wasVisible = sidebar.style.display !== 'none';

            if (wasVisible) {
                // Ø­ÙØ¸ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø² Ø§Ù„Ø­Ø§Ù„ÙŠ
                const deviceSelector = document.getElementById('device-selector');
                if (deviceSelector) {
                    currentDeviceType = deviceSelector.value;
                }

                // Ø­ÙØ¸ Ù…ÙˆÙ‚Ø¹ ÙˆØ­Ø¬Ù… Ø§Ù„Ù†Ø§ÙØ°Ø©
                saveWebPreviewPosition();
            }

            // Ø¥Ø¸Ù‡Ø§Ø± Ø§Ù„Ù†Ø§ÙØ°Ø© Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ù…Ø®ÙÙŠØ©
            if (sidebar.style.display === 'none') {
                sidebar.style.display = 'flex';
            }

            // ØªÙ‡ÙŠØ¦Ø© Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø¥Ø°Ø§ Ù„Ù… ØªÙƒÙ† Ù…Ù‡ÙŠØ£Ø© Ø¨Ø¹Ø¯
            if (!sidebar.dataset.initialized) {
                initWebPreviewSidebar();
                sidebar.dataset.initialized = 'true';
            }

            // ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ù…Ø­ØªÙˆÙ‰ ÙÙŠ iframe
            iframe.setAttribute('srcdoc', processedHtml);

            // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„Ù„Ø£Ø­Ø¯Ø§Ø« Ù„ØªØ·Ø¨ÙŠÙ‚ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø² Ø¨Ø¹Ø¯ Ø§Ù„ØªØ­Ù…ÙŠÙ„
            iframe.onload = () => {
                // Ø§Ø³ØªØ±Ø¬Ø§Ø¹ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø²
                if (wasVisible) {
                    // Ø§Ø³ØªØ®Ø¯Ø§Ù… Ù†ÙØ³ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø²
        setTimeout(() => {
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            deviceSelector.value = currentDeviceType;
                        }
                        changeDeviceView(currentDeviceType);

                        // Ø¶Ù…Ø§Ù† Ø¸Ù‡ÙˆØ± Ø§Ù„Ø¥Ø·Ø§Ø± Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
                        scrollDeviceFrameIntoView();
        }, 100);
                } else {
                    // Ø§Ø³ØªØ±Ø¬Ø§Ø¹ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ù…Ø­ÙÙˆØ¸Ø© Ø£Ùˆ Ø§Ø³ØªØ®Ø¯Ø§Ù… Ø§Ù„Ø§ÙØªØ±Ø§Ø¶ÙŠØ©
                    const savedDeviceType = localStorage.getItem('webPreviewDeviceType') || 'responsive';
                    setTimeout(() => {
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            deviceSelector.value = savedDeviceType;
                        }
                        changeDeviceView(savedDeviceType);

                        // Ø¶Ù…Ø§Ù† Ø¸Ù‡ÙˆØ± Ø§Ù„Ø¥Ø·Ø§Ø± Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
                        scrollDeviceFrameIntoView();
                    }, 100);
                }

                // Ø¥Ø¶Ø§ÙØ© Ø®ÙŠØ§Ø±Ø§Øª Ù„Ù„ØªÙØ§Ø¹Ù„ Ù…Ø¹ iframe
                try {
                    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„Ù„Ù†Ù‚Ø±Ø§Øª Ø¹Ù„Ù‰ Ø§Ù„Ø±ÙˆØ§Ø¨Ø· Ø¯Ø§Ø®Ù„ iframe
                    iframe.contentDocument.addEventListener('click', (e) => {
                        const link = e.target.closest('a');
                        if (link && link.href) {
                            // Ù…Ù†Ø¹ Ø§Ù„Ø§Ù†ØªÙ‚Ø§Ù„ Ø®Ø§Ø±Ø¬ iframe
                            e.preventDefault();

                            // Ø¹Ø±Ø¶ Ø±Ø³Ø§Ù„Ø© ÙÙŠ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
                            resultElement.textContent += `ØªÙ… Ø§Ù„Ù†Ù‚Ø± Ø¹Ù„Ù‰ Ø±Ø§Ø¨Ø·: ${link.href}\n`;
                            resultElement.textContent += `(Ø§Ù„Ø±ÙˆØ§Ø¨Ø· Ù…Ø­Ø¸ÙˆØ±Ø© ÙÙŠ ÙˆØ¶Ø¹ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ù„Ù„Ø£Ù…Ø§Ù†)\n`;
                        }
                    });

                    // ØªØ¹Ø·ÙŠÙ„ ÙˆØ¸Ø§Ø¦Ù Ø§Ù„ØªØ­Ø°ÙŠØ± ÙˆØ§Ù„Ø®Ø·Ø£ Ù…Ù† Ø¯Ø§Ø®Ù„ iframe
                    iframe.contentWindow.alert = (msg) => {
                        resultElement.textContent += `[ØªÙ†Ø¨ÙŠÙ‡]: ${msg}\n`;
                    };

                    iframe.contentWindow.onerror = (msg, source, line, col, error) => {
                        resultElement.textContent += `[Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©]: ${msg} (Ø³Ø·Ø± ${line})\n`;
                        return true; // Ù…Ù†Ø¹ Ø§Ù„Ø¸Ù‡ÙˆØ± ÙÙŠ ÙˆØ­Ø¯Ø© Ø§Ù„ØªØ­ÙƒÙ…
                    };

                    // Ø§Ø³ØªÙ…Ø¹ Ù„Ø±Ø³Ø§Ø¦Ù„ console.log
                    const originalConsoleLog = iframe.contentWindow.console.log;
                    iframe.contentWindow.console.log = function() {
                        originalConsoleLog.apply(this, arguments);
                        const args = Array.from(arguments).join(' ');
                        resultElement.textContent += `[console.log]: ${args}\n`;
                    };
                } catch (e) {
                    console.warn('Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø§Ù„Ø£Ø­Ø¯Ø§Ø« Ø¥Ù„Ù‰ iframe:', e);
                }
            };

            resultElement.textContent += 'ØªÙ… ÙØªØ­ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©!\n';
        } else {
            resultElement.textContent += 'Ø®Ø·Ø£: Ù„Ø§ ÙŠÙ…ÙƒÙ† Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ Ø¹Ù†Ø§ØµØ± Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©.\n';
        }

        return;
    }

    // Ø¯Ø¹Ù… Ø¬Ø§ÙØ§Ø³ÙƒØ±ÙŠØ¨Øª
    if (["js", "javascript"].includes(lang)) {
        try {
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
            }
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            window._executorIframe = iframe;
            let context = {
                iframe,
                win: iframe.contentWindow,
                consoleOutput: []
            };

            // Override console methods in the iframe
            try {
            context.win.console.log = function (...args) {
                context.consoleOutput.push(args.join(' '));
                console.log(...args);
            };
            context.win.console.error = function (...args) {
                context.consoleOutput.push('ERROR: ' + args.join(' '));
                console.error(...args);
            };
            context.win.__files = {};
            for (const [id, f] of Object.entries(workspace.files)) {
                context.win.__files[f.name] = f.content;
            }
            } catch (e) {
                resultElement.textContent += `Ø®Ø·Ø£ ÙÙŠ ØªÙ‡ÙŠØ¦Ø© Ø¨ÙŠØ¦Ø© Ø§Ù„ØªÙ†ÙÙŠØ°: ${e.message}\n`;
                console.error('Error setting up execution environment:', e);
                return;
            }

            codeContexts[currentConversationId] = context;
            context.consoleOutput = [];

            const wrappedCode = `
                        try {
                            function require(name) {
                                if (__files[name]) {
                                    const module = { exports: {} };
                                    const func = new Function('module', 'exports', __files[name]);
                                    func(module, module.exports);
                                    return module.exports;
                                }
                                throw new Error('Module not found: ' + name);
                            }
                            ${file.content}
                        } catch(e) {
                            console.error('Error:', e);
                        }
                    `;

            console.log('Executing JavaScript code');
            let result;
            try {
                result = context.win.eval(wrappedCode);
            } catch (e) {
                context.consoleOutput.push(`ERROR: ${e.message}`);
                console.error('Error during evaluation:', e);
            }

            if (result !== undefined) {
                context.consoleOutput.push(result.toString());
            }

            resultElement.textContent += context.consoleOutput.join('\n') || 'ØªÙ… Ø§Ù„ØªÙ†ÙÙŠØ° Ø¨Ù†Ø¬Ø§Ø­ Ø¨Ø¯ÙˆÙ† Ø¥Ø®Ø±Ø§Ø¬.\n';
        } catch (e) {
            resultElement.textContent += `Ø®Ø·Ø£: ${e.message}\n`;
            console.error('Error executing code:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // Ø¯Ø¹Ù… Ø¨Ø§ÙŠØ«ÙˆÙ†
    if (lang === 'python' || lang === 'py') {
        try {
            resultElement.textContent = 'Ø¬Ø§Ø±ÙŠ ØªØ­Ù…ÙŠÙ„ Ø¨Ø§ÙŠØ«ÙˆÙ† (Pyodide)...\n';

            // Check if Pyodide is available
            if (typeof loadPyodide !== 'function') {
                resultElement.textContent += 'Ø®Ø·Ø£: Ù…ÙƒØªØ¨Ø© Pyodide ØºÙŠØ± Ù…ØªÙˆÙØ±Ø©! ØªØ£ÙƒØ¯ Ù…Ù† ØªØ¶Ù…ÙŠÙ† Ø§Ù„Ù…Ù„Ù ÙÙŠ Ø§Ù„ØµÙØ­Ø©.\n';
                console.error('Pyodide library not available. Make sure to include the script.');
                return;
            }

            if (!window.pyodide) {
                try {
                window.pyodide = await loadPyodide();
                } catch (e) {
                    resultElement.textContent += `Ø®Ø·Ø£ ÙÙŠ ØªØ­Ù…ÙŠÙ„ Pyodide: ${e.message}\n`;
                    console.error('Error loading Pyodide:', e);
                    return;
            }
            }

            resultElement.textContent = 'Ø¬Ø§Ø±ÙŠ ØªÙ†ÙÙŠØ° ÙƒÙˆØ¯ Ø¨Ø§ÙŠØ«ÙˆÙ†...\n';
            let output = '';

            try {
            window.pyodide.setStdout({
                batched: (s) => { output += s; }
            });
            window.pyodide.setStderr({
                batched: (s) => { output += s; }
            });
            } catch (e) {
                resultElement.textContent += `Ø®Ø·Ø£ ÙÙŠ ØªÙ‡ÙŠØ¦Ø© Ø§Ù„Ù…Ø®Ø±Ø¬Ø§Øª: ${e.message}\n`;
                console.error('Error setting up stdout/stderr:', e);
            }

            try {
            await window.pyodide.runPythonAsync(file.content);
            resultElement.textContent += output || 'ØªÙ… Ø§Ù„ØªÙ†ÙÙŠØ° Ø¨Ù†Ø¬Ø§Ø­ Ø¨Ø¯ÙˆÙ† Ø¥Ø®Ø±Ø§Ø¬.\n';
        } catch (e) {
            resultElement.textContent += `Ø®Ø·Ø£ Ø¨Ø§ÙŠØ«ÙˆÙ†: ${e.message}\n`;
                console.error('Python execution error:', e);
            }
        } catch (e) {
            resultElement.textContent += `Ø®Ø·Ø£: ${e.message}\n`;
            console.error('General error in Python execution:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // Ù„ØºØ© ØºÙŠØ± Ù…Ø¯Ø¹ÙˆÙ…Ø©
    resultElement.textContent += `Ø§Ù„Ù„ØºØ© "${lang}" ØºÙŠØ± Ù…Ø¯Ø¹ÙˆÙ…Ø©. ØªÙ†ÙÙŠØ° Ø§Ù„ÙƒÙˆØ¯ Ù…ØªØ§Ø­ ÙÙ‚Ø· Ù„Ø¬Ø§ÙØ§Ø³ÙƒØ±ÙŠØ¨ØªØŒ Ø¨Ø§ÙŠØ«ÙˆÙ†ØŒ ÙˆHTML Ø­Ø§Ù„ÙŠØ§Ù‹.\n`;
}

function clearExecutorResult() {
    document.getElementById('executor-result').textContent = '';
}

// Ø¥Ø¶Ø§ÙØ© Ø§Ø®ØªØµØ§Ø±Ø§Øª Ù„ÙˆØ­Ø© Ø§Ù„Ù…ÙØ§ØªÙŠØ­
document.addEventListener('keydown', function (e) {
    const executor = document.getElementById('code-executor');
    if (executor.classList.contains('visible')) {
        if (e.key === 'Escape') {
            hideCodeExecutor(); // ÙÙ‚Ø· Ø¥Ø®ÙØ§Ø¡ Ø§Ù„Ù†Ø§ÙØ°Ø© Ø¯ÙˆÙ† Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„ØªØ¨ÙˆÙŠØ¨
            e.preventDefault();
        }

        if (e.key === 'F5') {
            runCodeInExecutor();
            e.preventDefault();
        }
    }

    // Ø§Ø®ØªØµØ§Ø± Ù„Ø¥Ø±Ø³Ø§Ù„ Ø§Ù„Ø±Ø³Ø§Ù„Ø©
    if (e.key === 'Enter' && !e.shiftKey && document.activeElement.id === 'chat-input') {
        e.preventDefault();
        sendMessage();
    }
});

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const menuToggle = document.getElementById('menu-toggle');

    sidebar.classList.toggle('visible');
    menuToggle.classList.toggle('active');

    // Ø¥Ø²Ø§Ù„Ø© Ø£ÙŠ ØªØ£Ø«ÙŠØ± Ø¹Ù„Ù‰ Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ø±Ø¦ÙŠØ³ÙŠ
    const mainContent = document.getElementById('main-content');
    mainContent.classList.remove('sidebar-visible');
}

function createNewChat() {
    currentConversationId = Date.now().toString();
    const newConversation = {
        id: currentConversationId,
        title: 'Ù…Ø­Ø§Ø¯Ø«Ø© Ø¬Ø¯ÙŠØ¯Ø©',
        messages: [],
        timestamp: new Date().toISOString()
    };

    conversations.unshift(newConversation);
    saveConversations();
    renderConversations();
    loadConversation(currentConversationId);
    toggleSidebar();
}

// ØªØ­Ø¯ÙŠØ« Ø¯Ø§Ù„Ø© saveConversations Ù„ØªØ­ÙØ¸ Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ù…Ù„ Ø¨Ø´ÙƒÙ„ Ù…Ù†ÙØµÙ„
function saveConversations() {
    localStorage.setItem('conversations', JSON.stringify(conversations));
    saveWorkspace();
}

// Ø¯Ø§Ù„Ø© Ø¬Ø¯ÙŠØ¯Ø© Ù„Ø­ÙØ¸ Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ù…Ù„ Ø¨Ø´ÙƒÙ„ Ù…Ù†ÙØµÙ„
function saveWorkspace() {
    localStorage.setItem('workspace', JSON.stringify(workspace));
    console.log('ØªÙ… Ø­ÙØ¸ Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ù…Ù„:', Object.keys(workspace.files).length, 'Ù…Ù„ÙØ§Øª');
}

function loadConversations() {
    const saved = localStorage.getItem('conversations');
    if (saved) {
        conversations = JSON.parse(saved);
        // ÙØ±Ø² Ø§Ù„Ù…Ø­Ø§Ø¯Ø«Ø§Øª Ø­Ø³Ø¨ Ø§Ù„ØªØ§Ø±ÙŠØ® Ù…Ù† Ø§Ù„Ø£Ø­Ø¯Ø«
        conversations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        renderConversations();

        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù‡Ù†Ø§Ùƒ Ù…Ø­Ø§Ø¯Ø«Ø§ØªØŒ Ù†ÙØªØ­ Ø§Ù„Ø£Ø®ÙŠØ±Ø©
        if (conversations.length > 0) {
            currentConversationId = conversations[0].id;
            loadConversation(currentConversationId);
        }
    }

    // ØªØ­Ù…ÙŠÙ„ Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ù…Ù„
    const savedWorkspace = localStorage.getItem('workspace');
    if (savedWorkspace) {
        workspace = JSON.parse(savedWorkspace);

        // Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø¢Ø®Ø± Ù…Ø³Ø§Ø± ØªÙ… Ø²ÙŠØ§Ø±ØªÙ‡
        const lastPath = localStorage.getItem('currentPath');
        if (lastPath) {
            workspace.currentPath = lastPath;
        }
    } else {
        // Ø¥Ù†Ø´Ø§Ø¡ Ù‡ÙŠÙƒÙ„ Ø§ÙØªØ±Ø§Ø¶ÙŠ Ù„Ù„Ù…Ø´Ø±ÙˆØ¹ ÙÙ‚Ø· Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù‡Ù†Ø§Ùƒ Ù…Ø³Ø§Ø­Ø© Ø¹Ù…Ù„ Ù…Ø­ÙÙˆØ¸Ø©
        createDefaultWorkspace();
    }

    updateFileExplorer();
}

// Ø¯Ø§Ù„Ø© Ø¬Ø¯ÙŠØ¯Ø© Ù„Ø¥Ù†Ø´Ø§Ø¡ Ù‡ÙŠÙƒÙ„ Ø§ÙØªØ±Ø§Ø¶ÙŠ Ù„Ù„Ù…Ø´Ø±ÙˆØ¹
function createDefaultWorkspace() {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
}

function renderConversations() {
    const list = document.getElementById('conversations-list');
    list.innerHTML = '';

    conversations.forEach(conv => {
        // Ø¥Ø¶Ø§ÙØ© Ø£ÙŠÙ‚ÙˆÙ†Ø© Ù„Ù„Ø±Ø³Ø§Ø¦Ù„ Ø§Ù„ØºÙ†ÙŠØ©
        const hasRichContent = conv.messages.some(m => m.content.includes('<div class="code-block">'));
        const icon = hasRichContent ? 'ðŸ“„' : 'ðŸ’¬';
        const item = document.createElement('div');
        item.className = 'conversation-item';
        item.innerHTML = `
            <div class="conv-title">${icon} ${conv.title}</div>
            <div class="conversation-actions">
                <button class="conversation-action" onclick="deleteConversation('${conv.id}', event)">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="conversation-action" onclick="shareConversation('${conv.id}', event)">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        `;
        item.addEventListener('click', (e) => {
            if (!e.target.closest('.conversation-action')) {
                loadConversation(conv.id);
            }
        });
        list.appendChild(item);
    });
}

function deleteConversation(id, event) {
    event.stopPropagation();
    if (confirm('Ù‡Ù„ ØªØ±ÙŠØ¯ Ø­Ø°Ù Ù‡Ø°Ù‡ Ø§Ù„Ù…Ø­Ø§Ø¯Ø«Ø© Ø¨Ø´ÙƒÙ„ Ø¯Ø§Ø¦Ù…ØŸ')) {
        conversations = conversations.filter(conv => conv.id !== id);
        if (currentConversationId === id) {
            currentConversationId = null;
            document.getElementById('chat-window').innerHTML = '';
        }
        saveConversations();
        renderConversations();
    }
}

function shareConversation(id, event) {
    event.stopPropagation();
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        const shareContent = conversation.messages
            .map(m => `${m.role === 'user' ? 'Ø£Ù†Øª' : 'Ø§Ù„Ù…Ø³Ø§Ø¹Ø¯'}: ${m.content}`)
            .join('\n\n');

        if (navigator.share) {
            navigator.share({
                title: `Ù…Ø­Ø§Ø¯Ø«Ø©: ${conversation.title}`,
                text: shareContent,
                url: window.location.href
            });
        } else {
            const textArea = document.createElement('textarea');
            textArea.value = shareContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('ØªÙ… Ù†Ø³Ø® Ø§Ù„Ù…Ø­Ø§Ø¯Ø«Ø© Ø¥Ù„Ù‰ Ø§Ù„Ø­Ø§ÙØ¸Ø©!');
        }
    }
}

function loadConversation(id) {
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        currentConversationId = id;
        const chatWindow = document.getElementById('chat-window');
        chatWindow.innerHTML = '';
        conversation.messages.forEach(msg => {
            const div = document.createElement('div');
            div.className = `message ${msg.role}`;

            if (msg.role === 'user') {
                div.innerHTML = `<div class="message-content">${DOMPurify.sanitize(msg.content, { KEEP_CONTENT: true })}</div>`;
            } else {
                div.innerHTML = msg.content;

                setTimeout(() => {
                    Prism.highlightAllUnder(div);
                    MathJax.typeset([div]);
                    div.querySelectorAll('.copy-button').forEach(btn => {
                        btn.onclick = () => copyCode(btn);
                    });
                    div.querySelectorAll('.run-code-btn').forEach(btn => {
                        btn.onclick = function (e) {
                            e.stopPropagation();
                            const codeBlock = this.closest('.code-block');
                            const lang = codeBlock.querySelector('.language-label').textContent.trim();
                            const content = codeBlock.querySelector('code').textContent;
                            const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                                content.match(/#\s*file:\s*([^\n]+)/i);
                            const fileName = fileNameMatch && fileNameMatch[1] ?
                                fileNameMatch[1].trim() :
                                `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                            showCodeExecutor(codeBlock, fileName, lang, content);
                        };
                    });
                    fixNestedLists();
                }, 100);
            }

            chatWindow.appendChild(div);
        });
    }
}

function showErrorAnimation() {
    const menuToggle = document.getElementById('menu-toggle');
    menuToggle.classList.add('error');
    setTimeout(() => menuToggle.classList.remove('error'), 400);
}

function fixNestedLists() {
    document.querySelectorAll('.message').forEach(message => {
        let lists = message.querySelectorAll('ol, ul');

        lists.forEach(list => {
            list.querySelectorAll('li').forEach(li => {
                if (li.children.length === 0 ||
                    Array.from(li.children).every(child => child.nodeName === 'SPAN' || child.nodeName === 'EM' || child.nodeName === 'STRONG')) {
                } else {
                    let newElements = [];
                    Array.from(li.childNodes).forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE &&
                            !['OL', 'UL', 'LI', 'SPAN', 'EM', 'STRONG', 'A', 'CODE'].includes(node.nodeName)) {
                            newElements.push(node);
                        }
                    });

                    if (newElements.length > 0) {
                        const parent = li.parentNode;
                        newElements.forEach(el => {
                            parent.insertBefore(el, li.nextSibling);
                        });
                    }
                }
            });
        });

        // Ø¥ØµÙ„Ø§Ø­ Ø§Ù„Ù‚ÙˆØ§Ø¦Ù… Ø§Ù„Ù…Ø±Ù‚Ù…Ø© Ø§Ù„Ù…ÙƒØ³ÙˆØ±Ø©
        message.querySelectorAll('ol').forEach(ol => {
            if (!ol.hasAttribute('data-fixed')) {
                const items = ol.innerHTML.split('</li>');
                if (items.length > 1) {
                    ol.innerHTML = items.map(item => {
                        if (item.trim() && !item.includes('<ol') && !item.includes('<ul')) {
                            return item + '</li>';
                        }
                        return item;
                    }).join('');
                }
                ol.setAttribute('data-fixed', 'true');
            }
        });
    });
}

////////////////////////////////////////////////////////////////////////////////
const promptContent = document.getElementById("chat-input");
const maxH = 80;
promptContent.addEventListener('input', function () {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
    if (this.scrollHeight <= maxH) {
        this.style.overflowY = 'hidden';
    } else {
        this.style.height = maxH + 'px';
        this.style.overflowY = 'auto';
    }
});

function sanitizeText(text) {
    return text;
}

function escapeHtml(text) {
    return text
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;");
}

function toggleThink(header) {
    const container = header.parentElement;
    const content = container.querySelector('.think-content');
    const toggle = container.querySelector('.think-toggle');

    const isOpen = content.style.display !== 'none';

    content.dataset.state = isOpen ? 'closed' : 'open';
    container.dataset.state = isOpen ? 'closed' : 'open';

    toggle.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(90deg)';

    content.style.display = isOpen ? 'none' : 'block';

    if (isOpen) {
        content.style.animation = 'think-close 0.3s ease forwards';
    } else {
        content.style.animation = 'think-open 0.3s ease forwards';

        setTimeout(() => {
            const codeBlocks = content.querySelectorAll('pre code');
            if (codeBlocks.length > 0) {
                Prism.highlightAllUnder(content);
            }

            if (content.querySelector('.math-inline, .math-block')) {
                MathJax.typeset([content]);
            }
        }, 300);
    }
}

function renderMarkdown(text) {
    const thinkProcess = [];
    const thinkRegex = /^&lt;think&gt;([\s\S]*?)&lt;\/think&gt;/gm;
    codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    inlineCodeRegex = /`([^`]+)`/g;

    const mathStore = [];
    const mathBlockRegex = /\\?(\$\$)([^\$]*?[^\\])(\\?\$\$)/g;
    const mathInlineRegex = /\\?(\$)(?!\$)([^\$]*?[^\\])(\\?\$)/g;
    const codeStore = [];

    text = text
        .replace(codeBlockRegex, (match, lang, content) => {
            codeStore.push({ type: 'block', lang, content });
            return `@@CODE_B_${codeStore.length - 1}@@`;
        });

    text = text
        .replace(mathBlockRegex, (_, o, c, cl) => {
            mathStore.push({ type: 'block', content: c });
            return `@@MATH_B_${mathStore.length - 1}@@`;
        })
        .replace(mathInlineRegex, (_, o, c, cl) => {
            mathStore.push({ type: 'inline', content: c });
            return `@@MATH_I_${mathStore.length - 1}@@`;
        });

    text = escapeHtml(text);
    text = DOMPurify.sanitize(text, { ALLOWED_TAGS: [], KEEP_CONTENT: true });

    text = text.replace(/@@CODE_B_(\d+)@@/g, (_, id) => {
        const { lang, content } = codeStore[id];

        // Ø§Ø³ØªØ®Ø±Ø§Ø¬ Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù Ù…Ù† Ø§Ù„ØªØ¹Ù„ÙŠÙ‚ Ø§Ù„Ù…Ø­Ø³Ù†
        let fileName = `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
        let filePath = '/';
        const fileMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
            content.match(/#\s*file:\s*([^\n]+)/i);

        if (fileMatch && fileMatch[1]) {
            const fullPath = fileMatch[1].trim();
            const pathParts = fullPath.split('/');
            fileName = pathParts.pop();
            filePath = pathParts.join('/') + '/';

            // ØªÙ†Ø¸ÙŠÙ Ø§Ù„Ù…Ø³Ø§Ø± Ø¥Ø°Ø§ ÙƒØ§Ù† ÙŠØ¨Ø¯Ø£ Ø¨Ù€ /
            if (filePath.startsWith('/')) {
                filePath = filePath.substring(1);
            }
            if (!filePath.endsWith('/')) {
                filePath += '/';
            }
        }

        // Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù…Ù„Ù Ù…Ø¹ Ø§Ù„Ù…Ø³Ø§Ø± Ø§Ù„ØµØ­ÙŠØ­
        const fileId = createFile(fileName, content.trim(), lang || 'javascript', filePath);

        // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„ØªØ´ØºÙŠÙ„
        const runButton = (lang === 'javascript' || lang === 'js' || lang === 'python')
            ? ``
            : '';

        // Ø¥Ø¶Ø§ÙØ© Ø²Ø± ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
        const openButton = `<button class="open-code-btn">
                    <i class="fas fa-code"></i> ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
                </button>`;

        // ØªØ®Ø²ÙŠÙ† Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª ÙÙŠ data attributes Ù„Ø§Ø³ØªØ®Ø¯Ø§Ù…Ù‡Ø§ Ù„Ø§Ø­Ù‚Ù‹Ø§
        const dataAttrs = `data-filename="${fileName}" data-lang="${lang || 'javascript'}" data-file-id="${fileId}"`;

        let codeDiv = `<div class="code-block" ${dataAttrs}>`;
        codeDiv += `<div class="code-header">`;
        codeDiv += `<span class="language-label">${lang || 'bash'}</span>`;
        codeDiv += `<button class="copy-button" onclick="copyCode(this)">Ù†Ø³Ø®</button>`;
        codeDiv += openButton;
        codeDiv += runButton;
        codeDiv += `</div>`;
        codeDiv += `<div class="code-content">`;
        codeDiv += `<pre><code class="language-${lang || 'bash'} line-numbers">${content.trim()}</code></pre>`;
        codeDiv += `</div>`;
        codeDiv += `</div>`;
        return codeDiv;
    });

    text = text.replace(inlineCodeRegex, function (match, codeContent) {
        return `<code class="inline-code">${codeContent}</code>`;
    });

    text = text.replace(thinkRegex, function (match, thinkContent) {
        let processedContent = renderMarkdown(thinkContent);
        processedContent = DOMPurify.sanitize(processedContent, {
            ALLOWED_TAGS: ['div', 'span', 'p', 'br', 'b', 'i', 'strong', 'em', 'code', 'pre', 'ul', 'ol', 'li', 'a', 'hr'],
            ALLOWED_ATTR: ['class', 'style', 'href', 'target', 'rel', 'data-animated', 'data-number', 'data-level', 'data-icon', 'data-enhanced'],
        });

        const formattedContent = processedContent.split('\n').map(line => {
            line = line.trim();
            if (!line) return '';
            if (line.startsWith('---')) {
                return '<div class="think-divider"></div>';
            }
            return `<div class="think-step">${line}</div>`;
        }).filter(line => line).join('');

        return `
    <div class="think-container">
        <div class="think-header" onclick="toggleThink(this)">
            <div class="think-toggle">â–¼</div>
            <div class="think-title">Thinking Process</div>
        </div>
        <div class="think-content">
            ${formattedContent}
        </div>
    </div>`;
    });

    const markdownProcessors = [
        {
            regex: /^(#{1,6})\s(.+?)(?:\n|$)/gm,
            handler: (_, hashes, content) =>
                `<h${hashes.length}>${content}</h${hashes.length}>`
        },
        {
            regex: /\*\*(.+?)\*\*/g,
            handler: (_, content) => `<b>${content}</b>`
        },
        {
            regex: /\*(.+?)\*/g,
            handler: (_, content) => `<em>${content}</em>`
        },
        {
            regex: /^[-*_]{3,}$/gm,
            handler: () => '<hr class="divider">'
        },
        {
            regex: /^(\d+)\.\s(.+)$/gm,
            handler: (match, num, content) => {
                return `<ol><li>${content}</li></ol>`;
            }
        },
        {
            regex: /^[-*+]\s(.+)$/gm,
            handler: (match, content) => {
                return `<ul><li>${content}</li></ul>`;
            }
        },
        {
            regex: /^>\s(.+)/gm,
            handler: (_, content) => `<blockquote class="blockquote">${content}</blockquote>`
        },
        {
            regex: /\[([^\]]+)\]\(([^)]+)\)/g,
            handler: (_, text, url) => `<a href="${url}" class="link">${text}</a>`
        },
        {
            regex: /!\[([^\]]+)\]\(([^)]+)\)/g,
            handler: (_, alt, src) => `<img src="${src}" alt="${alt}" class="image">`
        },
        {
            regex: /^(\|?[^\n]+\|)(\n\s*\|?[ :-]+\|?[^\n]*)(\n(\|?[^\n]+\|)(\n\|?[^\n]+\|)*)+/gm,
            handler: (_, headers, alignLine, rows) => {
                const alignments = alignLine.split('|')
                    .slice(1, -1)
                    .map(col => {
                        if (/^:-+:$/.test(col)) return 'center';
                        if (/^:-+/.test(col)) return 'left';
                        if (/-+:$/.test(col)) return 'right';
                        return 'left';
                    });

                let html = `
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  ${headers.split('|').slice(1, -1).map((h, i) => `
                    <th style="text-align:${alignments[i]}">${h.trim()}</th>
                  `).join('')}
                </tr>
              </thead>
              <tbody>
                ${rows.trim().split('\n').map(row => `
                  <tr>
                    ${row.split('|').slice(1, -1).map((cell, i) => `
                      <td style="text-align:${alignments[i]}">${cell.trim()}</td>
                    `).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>`;

                return html;
            }
        }
    ];

    markdownProcessors.forEach(({ regex, handler }) => {
        text = text.replace(regex, handler);
    });

    text = text
        .replace(/^@@MATH_B_(\d+)@@/gm, (_, id) =>
            `<div class="math-block">\\[${mathStore[id].content}\\]</div>`)
        .replace(/^@@MATH_I_(\d+)@@/gm, (_, id) =>
            `<span class="math-inline">\\(${mathStore[id].content}\\)</span>`);

    // Ø¥ØµÙ„Ø§Ø­ Ø§Ù„Ù‚ÙˆØ§Ø¦Ù… Ø§Ù„Ù…ØªØ¯Ø§Ø®Ù„Ø©
    text = text.replace(/<li>([^<]+)<(ul|ol)/g, '<li>$1</li><$2');

    // Ø¥ØºÙ„Ø§Ù‚ Ø£ÙŠ Ù‚ÙˆØ§Ø¦Ù… Ù…ÙØªÙˆØ­Ø© ÙÙŠ Ù†Ù‡Ø§ÙŠØ© Ø§Ù„Ù†Øµ
    if (text.indexOf('<ol') !== -1 && text.lastIndexOf('<ol') > text.lastIndexOf('</ol>')) {
        text += '</ol>';
    }
    if (text.indexOf('<ul') !== -1 && text.lastIndexOf('<ul') > text.lastIndexOf('</ul>')) {
        text += '</ul>';
    }

    return text
        .replace(/\\\{/g, '{')
        .replace(/\\\}/g, '}')
        .replace(/\\times/g, 'Ã—')
        .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '\\frac{$1}{$2}');
}

async function typeRegularMessage(text, elem) {
    let currentText = "";
    const speed = 1;
    for (let i = 0; i < text.length; i++) {
        currentText += text[i];
        elem.innerHTML = DOMPurify.sanitize(renderMarkdown(currentText), { KEEP_CONTENT: true });
        await new Promise(resolve => setTimeout(resolve, speed));
    }
    Prism.highlightAllUnder(elem);
    MathJax.typeset();
    fixNestedLists();
}

async function typeCodeBlock(content, lang, container) {
    // Ø§Ø³ØªØ®Ø±Ø§Ø¬ Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù Ù…Ù† Ø§Ù„ØªØ¹Ù„ÙŠÙ‚Ø§Øª Ø¥Ù† ÙˆØ¬Ø¯
    let fileName = `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
    let filePath = '/';
    const fileMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
        content.match(/#\s*file:\s*([^\n]+)/i);

    if (fileMatch && fileMatch[1]) {
        const fullPath = fileMatch[1].trim();
        const pathParts = fullPath.split('/');
        fileName = pathParts.pop();
        filePath = pathParts.join('/') + '/';

        // ØªÙ†Ø¸ÙŠÙ Ø§Ù„Ù…Ø³Ø§Ø± Ø¥Ø°Ø§ ÙƒØ§Ù† ÙŠØ¨Ø¯Ø£ Ø¨Ù€ /
        if (filePath.startsWith('/')) {
            filePath = filePath.substring(1);
        }
        if (!filePath.endsWith('/')) {
            filePath += '/';
        }
    }

    // Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù…Ù„Ù ÙÙ‚Ø· Ø¯ÙˆÙ† ÙØªØ­Ù‡ ØªÙ„Ù‚Ø§Ø¦ÙŠÙ‹Ø§
    const fileId = createFile(fileName, content.trim(), lang || 'javascript', filePath);

    // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„ØªØ´ØºÙŠÙ„ Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù„ØºØ© Ù…Ø¯Ø¹ÙˆÙ…Ø©
    const runButton = (lang === 'javascript' || lang === 'js' || lang === 'python')
        ? `<button class="run-code-btn">
                      <i class="fas fa-play"></i> ØªØ´ØºÙŠÙ„ Ø§Ù„ÙƒÙˆØ¯
                   </button>`
        : '';

    // Ø¥Ø¶Ø§ÙØ© Ø²Ø± ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
    const openButton = `<button class="open-code-btn">
                <i class="fas fa-code"></i> ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
            </button>`;

    let codeDiv = `<div class="code-block" data-file-id="${fileId}">`;
    codeDiv += `<div class="code-header">`;
    codeDiv += `<span class="language-label">${lang || 'bash'}</span>`;
    codeDiv += `<button class="copy-button" onclick="copyCode(this)">Ù†Ø³Ø®</button>`;
    codeDiv += openButton;
    codeDiv += runButton;
    codeDiv += `</div>`;
    codeDiv += `<div class="code-content">`;
    codeDiv += `<pre><code class="language-${lang || 'bash'} line-numbers"></code></pre>`;
    codeDiv += `</div>`;
    codeDiv += `</div>`;

    container.innerHTML += codeDiv;

    let codeElement = container.querySelector('.code-block:last-child code');

    // Ø§Ø¶Ø§ÙØ© Ø§Ø³ØªØ¬Ø§Ø¨Ø© Ù„Ø²Ø± Ø§Ù„ØªØ´ØºÙŠÙ„
    let runBtn = container.querySelector('.code-block:last-child .run-code-btn');
    if (runBtn) {
        runBtn.onclick = function (e) {
            e.stopPropagation();
            const codeBlock = this.closest('.code-block');
            const langVal = codeBlock.querySelector('.language-label').textContent.trim();
            const contentVal = codeBlock.querySelector('code').textContent;
            showCodeExecutor(codeBlock, fileName, langVal, contentVal);
        };
    }

    // Ø§Ø¶Ø§ÙØ© Ø§Ø³ØªØ¬Ø§Ø¨Ø© Ù„Ø²Ø± ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
    let openBtn = container.querySelector('.code-block:last-child .open-code-btn');
    if (openBtn) {
        openBtn.onclick = function (e) {
            e.stopPropagation();
            const codeBlock = this.closest('.code-block');
            const fileId = codeBlock.getAttribute('data-file-id');
            if (fileId) {
                openFile(fileId);
            }
        };
    }

    let currentText = "";
    const speed = 1;
    for (let i = 0; i < content.length; i++) {
        currentText += content[i];
        codeElement.textContent = currentText;
        await new Promise(resolve => setTimeout(resolve, speed));
    }
    Prism.highlightElement(codeElement);
}

async function typeMessage(message, container) {
    const thinkTagRegex = /<think>([\s\S]+?)<\/think>/g;
    let parts = [];
    let lastIndex = 0, match;
    while ((match = thinkTagRegex.exec(message)) !== null) {
        if (match.index > lastIndex) {
            parts.push({ type: 'normal', content: message.substring(lastIndex, match.index) });
        }
        parts.push({ type: 'think', content: match[1] });
        lastIndex = thinkTagRegex.lastIndex;
    }
    if (lastIndex < message.length) {
        parts.push({ type: 'normal', content: message.substring(lastIndex) });
    }

    for (const part of parts) {
        if (part.type === 'normal') {
            const codeRegex = /```(\w+)?\n([\s\S]*?)```/g;
            let idx = 0, codeMatch;
            while ((codeMatch = codeRegex.exec(part.content)) !== null) {
                if (codeMatch.index > idx) {
                    const normalText = part.content.substring(idx, codeMatch.index);
                    const p = document.createElement('p');
                    container.appendChild(p);
                    await typeRegularMessage(normalText, p);
                }
                await typeCodeBlock(codeMatch[2], codeMatch[1] || 'bash', container);
                idx = codeRegex.lastIndex;
            }
            if (idx < part.content.length) {
                const remaining = part.content.substring(idx);
                const p = document.createElement('p');
                container.appendChild(p);
                await typeRegularMessage(remaining, p);
            }
        } else if (part.type === 'think') {
            const thinkBlock = document.createElement("div");
            thinkBlock.className = "think-container";
            thinkBlock.dataset.state = "open";

            thinkBlock.innerHTML = `
    <div class="think-header" onclick="toggleThink(this)">
        <div class="think-toggle">â–¼</div>
        <div class="think-title">Thinking Process</div>
    </div>
    <div class="think-content" style="display: block;"></div>
`;
            container.appendChild(thinkBlock);

            const thinkContentContainer = thinkBlock.querySelector(".think-content");

            const cleanedContent = part.content
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .trim();

            await typeRegularMessage(cleanedContent, thinkContentContainer);

            setTimeout(() => {
                const steps = thinkContentContainer.innerHTML.split('\n');
                let formattedContent = '';

                steps.forEach(step => {
                    step = step.trim();
                    if (!step) return;

                    if (step.startsWith('---')) {
                        formattedContent += '<div class="think-divider"></div>';
                    } else {
                        formattedContent += `<div class="think-step">${step}</div>`;
                    }
                });

                thinkContentContainer.innerHTML = formattedContent;

                Prism.highlightAllUnder(thinkContentContainer);
                MathJax.typeset([thinkContentContainer]);
            }, 100);
        }
    }
    setTimeout(() => {
        Prism.highlightAllUnder(container);
        MathJax.typeset([container]);
        container.querySelectorAll('.copy-button').forEach(btn => {
            btn.onclick = () => copyCode(btn);
        });

        // ØªØ­Ø¯ÙŠØ« Ø³Ù„ÙˆÙƒ Ø£Ø²Ø±Ø§Ø± Ø§Ù„ØªØ´ØºÙŠÙ„ ÙˆØ§Ù„ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
        container.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });

        // Ø§Ø¶Ø§ÙØ© Ø§Ø³ØªØ¬Ø§Ø¨Ø© Ù„Ø²Ø± ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
        container.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        fixNestedLists();
    }, 1);
}

function createMessageDiv(content, role) {
    const div = document.createElement('div');
    div.className = `message ${role}`;
    return div;
}

function createTypingIndicator() {
    const div = document.createElement('div');
    div.className = 'message bot typing-indicator';
    div.innerHTML = `
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            `;
    return div;
}


function hideTypingIndicator() {
    var typingIndicator = document.getElementById("typing-indicator");
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const codeContent = codeBlock.querySelector('code').innerText;
    navigator.clipboard.writeText(codeContent).then(() => {
        const originalText = button.textContent;
        button.textContent = 'ØªÙ… Ø§Ù„Ù†Ø³Ø®!';
        setTimeout(() => button.textContent = originalText, 2000);
    });
}

async function queryGroqAI(userInput) {
    const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${"********************************************************"}`,
        },
        body: JSON.stringify({
            model: "deepseek-r1-distill-llama-70b",
            messages: [
                {
                    role: "system",
                    content:
                    `Ø£Ù†Øª Ù…Ø³Ø§Ø¹Ø¯ Ø°ÙƒÙŠ Ù…ØªØ®ØµØµ ÙÙŠ ØªØµÙ…ÙŠÙ… ÙˆØªØ·ÙˆÙŠØ± ÙˆØ§Ø¬Ù‡Ø§Øª Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù… (UI/UX). Ù…Ù‡Ø§Ù…Ùƒ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ©:

                    ## ØªØ®ØµØµÙƒ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠ:
                    1. **ØªØµÙ…ÙŠÙ… ÙˆØ§Ø¬Ù‡Ø§Øª Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…**: Ø¥Ù†Ø´Ø§Ø¡ ØªØµØ§Ù…ÙŠÙ… Ø­Ø¯ÙŠØ«Ø© ÙˆØ¬Ø°Ø§Ø¨Ø©
                    2. **ØªØ¬Ø±Ø¨Ø© Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…**: ØªØ­Ø³ÙŠÙ† Ø³Ù‡ÙˆÙ„Ø© Ø§Ù„Ø§Ø³ØªØ®Ø¯Ø§Ù… ÙˆØ§Ù„ØªÙØ§Ø¹Ù„
                    3. **Ø§Ù„ØªØµÙ…ÙŠÙ… Ø§Ù„ØªØ¬Ø§ÙˆØ¨ÙŠ**: Ø¶Ù…Ø§Ù† Ø¹Ù…Ù„ Ø§Ù„ØªØµÙ…ÙŠÙ… Ø¹Ù„Ù‰ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø£Ø¬Ù‡Ø²Ø©
                    4. **Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„**: Ø¬Ø¹Ù„ Ø§Ù„ØªØµÙ…ÙŠÙ… Ù…ØªØ§Ø­Ø§Ù‹ Ù„Ù„Ø¬Ù…ÙŠØ¹
                    5. **Ø§Ù„Ø£Ø¯Ø§Ø¡**: ØªØ­Ø³ÙŠÙ† Ø³Ø±Ø¹Ø© Ø§Ù„ØªØ­Ù…ÙŠÙ„ ÙˆØ§Ù„Ø£Ø¯Ø§Ø¡

                    ## Ø¹Ù†Ø¯ Ø¥Ù†Ø´Ø§Ø¡ Ø£ÙŠ ÙƒÙˆØ¯:
                    - Ø£Ø¶Ù ØªØ¹Ù„ÙŠÙ‚Ù‹Ø§ ÙÙŠ Ø§Ù„Ø³Ø·Ø± Ø§Ù„Ø£ÙˆÙ„ Ø¨Ø§Ù„ØµÙŠØºØ©: // file: [Ù…Ø³Ø§Ø±/Ø§Ø³Ù…_Ù…Ù„Ù.Ù„ØºØ©]
                    - Ø§Ø³ØªØ®Ø¯Ù… Ø£Ø­Ø¯Ø« Ù…Ø¹Ø§ÙŠÙŠØ± HTML5, CSS3, JavaScript ES6+
                    - Ø·Ø¨Ù‚ Ù…Ø¨Ø§Ø¯Ø¦ Ø§Ù„ØªØµÙ…ÙŠÙ… Ø§Ù„Ø­Ø¯ÙŠØ« (Material Design, Fluent Design)
                    - Ø§Ù‡ØªÙ… Ø¨Ø§Ù„Ø£Ù„ÙˆØ§Ù† ÙˆØ§Ù„Ø®Ø·ÙˆØ· ÙˆØ§Ù„ØªØ¨Ø§Ø¹Ø¯
                    - Ø§Ø³ØªØ®Ø¯Ù… CSS Grid Ùˆ Flexbox Ù„Ù„ØªØ®Ø·ÙŠØ·
                    - Ø£Ø¶Ù ØªØ£Ø«ÙŠØ±Ø§Øª ÙˆØ§Ù†ØªÙ‚Ø§Ù„Ø§Øª Ø³Ù„Ø³Ø©
                    - ØªØ£ÙƒØ¯ Ù…Ù† Ø§Ù„ØªØ¬Ø§ÙˆØ¨ Ù…Ø¹ Ø¬Ù…ÙŠØ¹ Ø£Ø­Ø¬Ø§Ù… Ø§Ù„Ø´Ø§Ø´Ø§Øª

                    ## Ø£Ø¯ÙˆØ§Øª Ù…Ø¬Ø§Ù†ÙŠØ© Ø³ØªØ³ØªØ®Ø¯Ù…Ù‡Ø§:
                    - CSS Frameworks: Bootstrap, Tailwind CSS
                    - Icons: Font Awesome, Feather Icons
                    - Fonts: Google Fonts
                    - Colors: Coolors.co, Adobe Color
                    - Images: Unsplash, Pexels
                    - Animations: Animate.css, AOS

                    ## Ù†ØµØ§Ø¦Ø­ Ø§Ù„ØªØµÙ…ÙŠÙ…:
                    - Ø§Ø³ØªØ®Ø¯Ù… Ù†Ø¸Ø§Ù… Ø£Ù„ÙˆØ§Ù† Ù…ØªÙ†Ø§Ø³Ù‚
                    - Ø§Ù‡ØªÙ… Ø¨Ø§Ù„ØªØ³Ù„Ø³Ù„ Ø§Ù„Ù‡Ø±Ù…ÙŠ Ø§Ù„Ø¨ØµØ±ÙŠ
                    - Ø§Ø³ØªØ®Ø¯Ù… Ø§Ù„Ù…Ø³Ø§Ø­Ø§Øª Ø§Ù„Ø¨ÙŠØ¶Ø§Ø¡ Ø¨ÙØ¹Ø§Ù„ÙŠØ©
                    - Ø§Ø¬Ø¹Ù„ Ø§Ù„ØªØµÙ…ÙŠÙ… Ø¨Ø¯ÙŠÙ‡ÙŠ ÙˆØ³Ù‡Ù„ Ø§Ù„Ø§Ø³ØªØ®Ø¯Ø§Ù…
                    - Ø§Ø®ØªØ¨Ø± Ø§Ù„ØªØµÙ…ÙŠÙ… Ø¹Ù„Ù‰ Ø£Ø¬Ù‡Ø²Ø© Ù…Ø®ØªÙ„ÙØ©`
                },
                {
                    role: "user",
                    content: userInput
                }],
            temperature: 0.3,
            top_p: 0.9,
            frequency_penalty: 0.2
        }),
    });
    const data = await response.json();
    return data.choices[0].message.content;
}

async function sendMessage() {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;

    const chatWindow = document.getElementById("chat-window");

    if (!currentConversationId) {
        currentConversationId = Date.now().toString();
        conversations.unshift({
            id: currentConversationId,
            title: message.substring(0, 20),
            messages: [],
            timestamp: new Date().toISOString()
        });
        renderConversations();
    }

    const userDiv = document.createElement("div");
    userDiv.className = "message user";
    userDiv.innerHTML = `<div class="message-content">${DOMPurify.sanitize(message, { KEEP_CONTENT: true })}</div>`;
    chatWindow.appendChild(userDiv);

    const currentConv = conversations.find(c => c.id === currentConversationId);
    if (currentConv) {
        currentConv.messages.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });
    }

    userDiv.scrollIntoView({ behavior: "smooth" });
    inputElem.value = "";
    inputElem.style.height = "40px";

    const typingDiv = document.createElement("div");
    typingDiv.className = "message bot typing-indicator";
    typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
    chatWindow.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: "smooth" });

    try {
        const botResponse = await queryGroqAI(message);
        typingDiv.remove();

        const botDiv = document.createElement("div");
        botDiv.className = "message bot";
        chatWindow.appendChild(botDiv);
        await typeMessage(botResponse, botDiv);

        if (currentConv) {
            currentConv.messages.push({
                role: 'bot',
                content: botDiv.innerHTML,
                timestamp: new Date().toISOString()
            });
            currentConv.title = message.substring(0, 20);
        }

        saveConversations();
        renderConversations();
        botDiv.scrollIntoView({ behavior: "smooth" });

    } catch (error) {
        typingDiv.remove();
        console.error("Error:", error);

        if (currentConv) {
            currentConv.messages = currentConv.messages.filter(m => m.content !== message);
        }

        showErrorMessage();
    }
    smartScroll(chatWindow);
}

function smartScroll(element) {
    const threshold = 100;
    const isNearBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + threshold;

    if (isNearBottom) {
        element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
        });
    }
}

window.addEventListener('resize', () => {
    const activeElement = document.activeElement;
    if (activeElement.tagName === 'TEXTAREA') {
        smartScroll(document.getElementById('chat-window'));
    }
});

function showErrorMessage() {
    const chatWindow = document.getElementById('chat-window');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message bot error';
    errorDiv.innerHTML = `<div class="message-content">Ø¹Ø°Ø±Ø§Ù‹ØŒ Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«Ù†Ø§Ø¡ Ù…Ø¹Ø§Ù„Ø¬Ø© Ø·Ù„Ø¨Ùƒ. ÙŠØ±Ø¬Ù‰ Ø§Ù„Ù…Ø­Ø§ÙˆÙ„Ø© Ù…Ø±Ø© Ø£Ø®Ø±Ù‰.</div>`;
    chatWindow.appendChild(errorDiv);
    smartScroll(chatWindow);
}

// ØªÙ‡ÙŠØ¦Ø© Ø§Ù„Ù†Ø¸Ø§Ù… Ø¹Ù†Ø¯ Ø§Ù„ØªØ­Ù…ÙŠÙ„
window.addEventListener('load', () => {
    loadConversations();

    // Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ø¹Ù†Ø§ØµØ± Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ© Ù„Ù„ÙˆØ§Ø¬Ù‡Ø©
    const requiredElements = ['file-explorer', 'sidebar', 'pluginbar', 'memorybar', 'taskbar'];
    let missingElements = [];

    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            missingElements.push(id);
            console.error(`Ø¹Ù†ØµØ± Ø£Ø³Ø§Ø³ÙŠ Ù…ÙÙ‚ÙˆØ¯: ${id}`);
        }
    });

    // Ø¥Ù†Ø´Ø§Ø¡ Ø¹Ù†ØµØ± taskbar Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙÙ‚ÙˆØ¯Ø§Ù‹
    if (missingElements.includes('taskbar')) {
        console.log('Ø¬Ø§Ø±ÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø¹Ù†ØµØ± taskbar Ø§Ù„Ù…ÙÙ‚ÙˆØ¯...');
        const taskbar = document.createElement('div');
        taskbar.className = 'taskbar';
        taskbar.id = 'taskbar';
        taskbar.innerHTML = `
                    <div class="taskbar-header">
                        <div class="taskbar-title">Ø§Ù„Ù…Ù‡Ø§Ù…</div>
                        <button class="explorer-action" onclick="toggleTaskbarToolbar()" title="Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„Ù…Ù‡Ø§Ù…">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="taskbar-list" id="taskbar-list"></div>
                `;
        document.body.appendChild(taskbar);
        missingElements = missingElements.filter(el => el !== 'taskbar');
    }

    // Ø¥Ø¶Ø§ÙØ© Ø¹Ù†ØµØ± floating-toolbar Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙÙ‚ÙˆØ¯Ø§Ù‹
    if (!document.getElementById('floating-toolbar')) {
        console.log('Ø¬Ø§Ø±ÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø¹Ù†ØµØ± floating-toolbar Ø§Ù„Ù…ÙÙ‚ÙˆØ¯...');
        const floatingToolbar = document.createElement('div');
        floatingToolbar.className = 'floating-toolbar';
        floatingToolbar.id = 'floating-toolbar';
        document.body.appendChild(floatingToolbar);
    }

    // Ø¥Ù†Ø´Ø§Ø¡ Ù‡ÙŠÙƒÙ„ Ø§ÙØªØ±Ø§Ø¶ÙŠ Ù„Ù„Ù…Ø´Ø±ÙˆØ¹ Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹
    if (!workspace.folders['root'] || !workspace.folders['root'].children || workspace.folders['root'].children.length === 0) {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
    }

    // ØªÙ‡ÙŠØ¦Ø© Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ø¥Ø°Ø§ Ù„Ù… ØªÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ø©
    if (!localStorage.getItem('openFiles')) {
        localStorage.setItem('openFiles', JSON.stringify([]));
    }

    // Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø­Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ù…Ù† Ø§Ù„ØªØ®Ø²ÙŠÙ† Ø§Ù„Ù…Ø­Ù„ÙŠ
    const explorerVisible = localStorage.getItem('explorerVisible') === 'true';
    if (explorerVisible) {
        toggleExplorerToolbar(true); // ÙØªØ­ Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ø¨Ø¯ÙˆÙ† ØªØ¨Ø¯ÙŠÙ„ Ø§Ù„Ø­Ø§Ù„Ø©
    } else {
        // Ø§Ù„ØªØ£ÙƒØ¯ Ù…Ù† Ø£Ù† Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ù…ØºÙ„Ù‚
        document.getElementById('file-explorer').classList.remove('visible');
        // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¹Ù†Ø§ØµØ± Ø§Ù„Ø£Ø®Ø±Ù‰ Ù„Ù„ØªÙƒÙŠÙ Ù…Ø¹ Ø­Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ø§Ù„Ù…ØºÙ„Ù‚
        document.getElementById('main-content').classList.remove('explorer-visible');
        document.querySelector('.header').classList.remove('explorer-visible');
        document.querySelector('.footer').classList.remove('explorer-visible');
        if (document.getElementById('code-executor')) {
            document.getElementById('code-executor').classList.remove('explorer-visible');
        }
    }

    updateFileExplorer();

    // ØªÙ‡ÙŠØ¦Ø© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
    initializeTerminal();

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø­Ø¯Ø§Ø« Ø£Ø²Ø±Ø§Ø± Ø§Ù„ÙƒÙˆØ¯
    function addCodeButtonListeners() {
        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø²Ø±Ø§Ø± "ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø²Ø±Ø§Ø± "ØªØ´ØºÙŠÙ„ Ø§Ù„ÙƒÙˆØ¯"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    // ØªÙ‡ÙŠØ¦Ø© Tippy.js Ø¨Ø£Ø³Ù„ÙˆØ¨ Ø¢Ù…Ù†
    function initTippy() {
        if (typeof tippy !== 'undefined') {
            tippy('.message ol li', {
                content: 'Ø¹Ù†ØµØ± Ù‚Ø§Ø¦Ù…Ø© Ù…Ø±Ù‚Ù…Ø©',
                placement: 'right',
                animation: 'scale',
                theme: 'light',
                delay: [500, 0]
            });

            tippy('.message ul li', {
                content: 'Ø¹Ù†ØµØ± Ù‚Ø§Ø¦Ù…Ø© Ù†Ù‚Ø·ÙŠØ©',
                placement: 'right',
                animation: 'scale',
                theme: 'light',
                delay: [500, 0]
            });

            // Ø¥Ø¶Ø§ÙØ© tippy Ù„Ø£Ø²Ø±Ø§Ø± Ø§Ù„ÙƒÙˆØ¯
            tippy('.open-code-btn', {
                content: 'ÙØªØ­ Ø§Ù„Ù…Ù„Ù ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±',
                placement: 'top',
                animation: 'scale',
                theme: 'light',
                delay: [300, 0]
            });

            tippy('.run-code-btn', {
                content: 'ØªØ´ØºÙŠÙ„ Ø§Ù„ÙƒÙˆØ¯',
                placement: 'top',
                animation: 'scale',
                theme: 'light',
                delay: [300, 0]
            });
        }
    }

    function initSortable() {
        const lists = document.querySelectorAll('.message ul, .message ol');

        lists.forEach(list => {
            if (typeof Sortable !== 'undefined') {
                new Sortable(list, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    disabled: true,
                    onEnd: function (evt) {
                        if (evt.to.tagName.toLowerCase() === 'ol') {
                            const items = evt.to.querySelectorAll('li');
                            items.forEach((item, index) => {
                                item.setAttribute('data-number', index + 1);
                            });
                        }
                    }
                });
            }
        });
    }

    setTimeout(() => {
        initTippy();
        initSortable();
        fixNestedLists();
        addCodeButtonListeners(); // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø§Ù„Ø£Ø­Ø¯Ø§Ø« Ù„Ø£Ø²Ø±Ø§Ø± Ø§Ù„ÙƒÙˆØ¯
    }, 1000);

    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.querySelectorAll) {
                        const lists = node.querySelectorAll('ul, ol');
                        const codeButtons = node.querySelectorAll('.open-code-btn, .run-code-btn');
                        if (lists.length || codeButtons.length) {
                            setTimeout(() => {
                                initTippy();
                                initSortable();
                                fixNestedLists();
                                addCodeButtonListeners(); // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ø¹Ù†Ø¯ Ø¥Ø¶Ø§ÙØ© Ø¹Ù†Ø§ØµØ± Ø¬Ø¯ÙŠØ¯Ø©
                            }, 500);
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.getElementById('chat-window'), {
        childList: true,
        subtree: true
    });

    // ØªÙ‡ÙŠØ¦Ø© Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ù…ÙØªÙˆØ­Ø©
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar && sidebar.style.display !== 'none') {
        initWebPreviewSidebar();
        sidebar.dataset.initialized = 'true';
    }

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø­Ø¯Ø§Ø« Ø£Ø²Ø±Ø§Ø± Ø§Ù„ÙƒÙˆØ¯
    function addCodeButtonListeners() {
        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø²Ø±Ø§Ø± "ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø²Ø±Ø§Ø± "ØªØ´ØºÙŠÙ„ Ø§Ù„ÙƒÙˆØ¯"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    setTimeout(() => {
        addCodeButtonListeners();
    }, 1000);
});

// ØªÙ… Ø¥Ø²Ø§Ù„Ø© Ø¯Ø§Ù„Ø© toggleDirection

// Ù†Ø¸Ø§Ù… Ø§Ù„Ù…Ù‡Ø§Ù… Ø§Ù„Ø°ÙƒÙŠØ©
let tasks = [];
function addTask(title) {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 5);
    const task = {
        id,
        title,
        status: 'Ù‚ÙŠØ¯ Ø§Ù„ØªÙ†ÙÙŠØ°',
        time: new Date().toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })
    };
    tasks.unshift(task);
    renderTasks();
    return id;
}
function updateTask(id, status) {
    const task = tasks.find(t => t.id === id);
    if (task) {
        task.status = status;
        renderTasks();
    }
}
function renderTasks() {
    const list = document.getElementById('taskbar-list');
    if (!list) return;
    list.innerHTML = '';
    tasks.forEach(task => {
        const item = document.createElement('div');
        item.className = 'task-item';
        item.innerHTML = `
            <div class="task-title">${task.title}</div>
            <div class="task-status">${task.status}</div>
            <div class="task-time">${task.time}</div>
        `;
        list.appendChild(item);
    });
}
function toggleTaskbar() {
    const bar = document.getElementById('taskbar');
    bar.classList.toggle('visible');
}
// Ø¯Ù…Ø¬ Ø§Ù„Ù…Ù‡Ø§Ù… Ù…Ø¹ Ø¥Ø±Ø³Ø§Ù„ Ø§Ù„Ø±Ø³Ø§Ø¦Ù„
const oldSendMessage = sendMessage;
sendMessage = async function () {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;
    const taskId = addTask(message.substring(0, 30));
    try {
        await oldSendMessage.apply(this, arguments);
        updateTask(taskId, 'ØªÙ…');
    } catch (e) {
        updateTask(taskId, 'ÙØ´Ù„');
    }
}

// Ù†Ø¸Ø§Ù… Ø§Ù„Ø¥Ø¶Ø§ÙØ§Øª (Plugins)
let plugins = [
    {
        name: 'ðŸŽ¨ Ù…ÙˆÙ„Ø¯ Ù„ÙˆØ­Ø© Ø§Ù„Ø£Ù„ÙˆØ§Ù†',
        description: 'Ø¥Ù†Ø´Ø§Ø¡ Ù„ÙˆØ­Ø© Ø£Ù„ÙˆØ§Ù† Ù…ØªÙ†Ø§Ø³Ù‚Ø© Ù„Ù„ØªØµÙ…ÙŠÙ…. Ø§Ø³ØªØ®Ø¯Ù…: Ø£Ù†Ø´Ø¦ Ù„ÙˆØ­Ø© Ø£Ù„ÙˆØ§Ù† [Ù†ÙˆØ¹/Ù„ÙˆÙ† Ø£Ø³Ø§Ø³ÙŠ]',
        enabled: true,
        match: (msg) => /^Ø£Ù†Ø´Ø¦ Ù„ÙˆØ­Ø© Ø£Ù„ÙˆØ§Ù†|^Ø§Ø¹Ù…Ù„ Ù„ÙˆØ­Ø© Ø£Ù„ÙˆØ§Ù†|^Ù…ÙˆÙ„Ø¯ Ø£Ù„ÙˆØ§Ù†|^color palette/i.test(msg),
        execute: async (msg) => {
            const colorPalettes = {
                'modern': ['#2563eb', '#3b82f6', '#60a5fa', '#93c5fd', '#dbeafe'],
                'warm': ['#dc2626', '#ef4444', '#f87171', '#fca5a5', '#fecaca'],
                'nature': ['#059669', '#10b981', '#34d399', '#6ee7b7', '#a7f3d0'],
                'sunset': ['#ea580c', '#f97316', '#fb923c', '#fdba74', '#fed7aa'],
                'ocean': ['#0891b2', '#06b6d4', '#22d3ee', '#67e8f9', '#a5f3fc'],
                'purple': ['#7c3aed', '#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe'],
                'gradient': ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
            };

            const type = msg.match(/Ø£Ù„ÙˆØ§Ù†\s+(\w+)/)?.[1] || 'modern';
            const palette = colorPalettes[type] || colorPalettes['modern'];

            let response = `<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">ðŸŽ¨ Ù„ÙˆØ­Ø© Ø£Ù„ÙˆØ§Ù† ${type}</h3>`;
            response += `<div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0;">`;

            palette.forEach((color, index) => {
                response += `<div style="display: flex; flex-direction: column; align-items: center; background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px;">`;
                response += `<div style="width: 60px; height: 60px; background: ${color}; border-radius: 8px; border: 2px solid rgba(255,255,255,0.3); margin-bottom: 8px;"></div>`;
                response += `<code style="background: rgba(0,0,0,0.3); padding: 4px 8px; border-radius: 4px; font-size: 12px;">${color}</code>`;
                response += `</div>`;
            });

            response += `</div>`;
            response += `<details style="margin-top: 15px;"><summary style="cursor: pointer; font-weight: bold;">ðŸ“‹ ÙƒÙˆØ¯ CSS</summary>`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin-top: 10px; overflow-x: auto;"><code>:root {\n`;
            palette.forEach((color, index) => {
                response += `  --color-${index + 1}: ${color};\n`;
            });
            response += `}</code></pre></details>`;
            response += `<p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">ðŸ’¡ Ø§Ù„Ø£Ù†ÙˆØ§Ø¹ Ø§Ù„Ù…ØªØ§Ø­Ø©: modern, warm, nature, sunset, ocean, purple, gradient</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: 'ðŸ§© Ù…ÙˆÙ„Ø¯ Ù…ÙƒÙˆÙ†Ø§Øª UI',
        description: 'Ø¥Ù†Ø´Ø§Ø¡ Ù…ÙƒÙˆÙ†Ø§Øª UI Ø¬Ø§Ù‡Ø²Ø© (Ø£Ø²Ø±Ø§Ø±ØŒ Ø¨Ø·Ø§Ù‚Ø§ØªØŒ Ù†Ù…Ø§Ø°Ø¬). Ø§Ø³ØªØ®Ø¯Ù…: Ø£Ù†Ø´Ø¦ Ù…ÙƒÙˆÙ† [Ù†ÙˆØ¹ Ø§Ù„Ù…ÙƒÙˆÙ†]',
        enabled: true,
        match: (msg) => /^Ø£Ù†Ø´Ø¦ Ù…ÙƒÙˆÙ†|^Ø§Ø¹Ù…Ù„ Ù…ÙƒÙˆÙ†|^component|^Ù…ÙƒÙˆÙ†/i.test(msg),
        execute: async (msg) => {
            const componentType = msg.match(/Ù…ÙƒÙˆÙ†\s+(\w+)/)?.[1] || 'button';

            const components = {
                'button': {
                    name: 'Ø²Ø± Ø­Ø¯ÙŠØ«',
                    html: `<button class="modern-btn">Ø§Ù†Ù‚Ø± Ù‡Ù†Ø§</button>`,
                    css: `.modern-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modern-btn:active {
  transform: translateY(0);
}`
                },
                'card': {
                    name: 'Ø¨Ø·Ø§Ù‚Ø© Ø¹ØµØ±ÙŠØ©',
                    html: `<div class="modern-card">
  <div class="card-image">
    <img src="https://via.placeholder.com/300x200" alt="ØµÙˆØ±Ø©">
  </div>
  <div class="card-content">
    <h3>Ø¹Ù†ÙˆØ§Ù† Ø§Ù„Ø¨Ø·Ø§Ù‚Ø©</h3>
    <p>ÙˆØµÙ Ù…Ø®ØªØµØ± Ù„Ù„Ù…Ø­ØªÙˆÙ‰...</p>
    <div class="card-actions">
      <button class="btn-primary">Ø¥Ø¬Ø±Ø§Ø¡ Ø±Ø¦ÙŠØ³ÙŠ</button>
      <button class="btn-secondary">Ø¥Ø¬Ø±Ø§Ø¡ Ø«Ø§Ù†ÙˆÙŠ</button>
    </div>
  </div>
</div>`,
                    css: `.modern-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 350px;
}

.modern-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 24px;
}

.card-content h3 {
  margin: 0 0 12px 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 700;
}

.card-content p {
  color: #666;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.btn-primary, .btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
}`
                }
            };

            const component = components[componentType] || components['button'];

            let response = `<div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); padding: 20px; border-radius: 12px; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0; color: #1e293b;">ðŸ§© ${component.name}</h3>`;

            response += `<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e2e8f0;">`;
            response += `<h4 style="margin: 0 0 10px 0; color: #475569;">HTML:</h4>`;
            response += `<pre style="background: #f8fafc; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e2e8f0;"><code>${component.html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
            response += `</div>`;

            response += `<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e2e8f0;">`;
            response += `<h4 style="margin: 0 0 10px 0; color: #475569;">CSS:</h4>`;
            response += `<pre style="background: #f8fafc; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e2e8f0;"><code>${component.css}</code></pre>`;
            response += `</div>`;

            response += `<p style="margin: 15px 0 0 0; color: #64748b; font-size: 14px;">ðŸ’¡ Ø§Ù„Ù…ÙƒÙˆÙ†Ø§Øª Ø§Ù„Ù…ØªØ§Ø­Ø©: button, card, form, navbar, modal, input</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: 'ðŸ“± Ù…ÙˆÙ„Ø¯ ØªØµÙ…ÙŠÙ… ØªØ¬Ø§ÙˆØ¨ÙŠ',
        description: 'Ø¥Ù†Ø´Ø§Ø¡ ÙƒÙˆØ¯ CSS Ù„Ù„ØªØµÙ…ÙŠÙ… Ø§Ù„ØªØ¬Ø§ÙˆØ¨ÙŠ. Ø§Ø³ØªØ®Ø¯Ù…: Ø£Ù†Ø´Ø¦ ØªØµÙ…ÙŠÙ… ØªØ¬Ø§ÙˆØ¨ÙŠ [Ù†ÙˆØ¹ Ø§Ù„ØªØ®Ø·ÙŠØ·]',
        enabled: true,
        match: (msg) => /^Ø£Ù†Ø´Ø¦ ØªØµÙ…ÙŠÙ… ØªØ¬Ø§ÙˆØ¨ÙŠ|^responsive design|^ØªØ¬Ø§ÙˆØ¨ÙŠ/i.test(msg),
        execute: async (msg) => {
            const layoutType = msg.match(/ØªØ¬Ø§ÙˆØ¨ÙŠ\s+(\w+)/)?.[1] || 'grid';

            const layouts = {
                'grid': {
                    name: 'ØªØ®Ø·ÙŠØ· Ø´Ø¨ÙƒÙŠ',
                    css: `/* ØªØ®Ø·ÙŠØ· Ø´Ø¨ÙƒÙŠ ØªØ¬Ø§ÙˆØ¨ÙŠ */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

/* Ù„Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ÙƒØ¨ÙŠØ±Ø© */
@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
  }
}

/* Ù„Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„Ù…ØªÙˆØ³Ø·Ø© */
@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 15px;
  }
}

/* Ù„Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ØµØºÙŠØ±Ø© */
@media (max-width: 480px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }
}`
                },
                'flexbox': {
                    name: 'ØªØ®Ø·ÙŠØ· Ù…Ø±Ù†',
                    css: `/* ØªØ®Ø·ÙŠØ· Ù…Ø±Ù† ØªØ¬Ø§ÙˆØ¨ÙŠ */
.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
}

.flex-item {
  flex: 1 1 300px;
  min-width: 0;
}

/* Ù„Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ÙƒØ¨ÙŠØ±Ø© */
@media (min-width: 1200px) {
  .flex-item {
    flex: 1 1 calc(25% - 15px);
  }
}

/* Ù„Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„Ù…ØªÙˆØ³Ø·Ø© */
@media (max-width: 768px) {
  .responsive-flex {
    gap: 15px;
    padding: 15px;
  }

  .flex-item {
    flex: 1 1 calc(50% - 7.5px);
  }
}

/* Ù„Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ØµØºÙŠØ±Ø© */
@media (max-width: 480px) {
  .responsive-flex {
    gap: 10px;
    padding: 10px;
  }

  .flex-item {
    flex: 1 1 100%;
  }
}`
                }
            };

            const layout = layouts[layoutType] || layouts['grid'];

            let response = `<div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">ðŸ“± ${layout.name} ØªØ¬Ø§ÙˆØ¨ÙŠ</h3>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin: 15px 0;">`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap;"><code>${layout.css}</code></pre>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">ðŸ“ Ù†Ù‚Ø§Ø· Ø§Ù„ØªÙˆÙ‚Ù Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…Ø©:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px;">`;
            response += `<li>ðŸ“± Ø§Ù„Ù‡ÙˆØ§ØªÙ: Ø£Ù‚Ù„ Ù…Ù† 480px</li>`;
            response += `<li>ðŸ“± Ø§Ù„ØªØ§Ø¨Ù„Øª: 481px - 768px</li>`;
            response += `<li>ðŸ’» Ø§Ù„Ø­Ø§Ø³ÙˆØ¨: 769px - 1199px</li>`;
            response += `<li>ðŸ–¥ï¸ Ø§Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ÙƒØ¨ÙŠØ±Ø©: 1200px ÙØ£ÙƒØ«Ø±</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">ðŸ’¡ Ø§Ù„Ø£Ù†ÙˆØ§Ø¹ Ø§Ù„Ù…ØªØ§Ø­Ø©: grid, flexbox, navbar, sidebar</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: 'ðŸŽ¯ Ù…Ø­Ù„Ù„ Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„',
        description: 'ÙØ­Øµ ÙˆØªØ­Ø³ÙŠÙ† Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„ Ù„Ù„ØªØµÙ…ÙŠÙ…. Ø§Ø³ØªØ®Ø¯Ù…: ÙØ­Øµ Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„',
        enabled: true,
        match: (msg) => /^ÙØ­Øµ Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„|^accessibility check|^a11y/i.test(msg),
        execute: async (msg) => {
            let response = `<div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">ðŸŽ¯ Ø¯Ù„ÙŠÙ„ Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„</h3>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">âœ… Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„ØªØ­Ù‚Ù‚ Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ©:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px; line-height: 1.6;">`;
            response += `<li>Ø§Ø³ØªØ®Ø¯Ø§Ù… Ù†Øµ Ø¨Ø¯ÙŠÙ„ Ù„Ù„ØµÙˆØ± (alt text)</li>`;
            response += `<li>ØªØ¨Ø§ÙŠÙ† Ø£Ù„ÙˆØ§Ù† ÙƒØ§ÙÙŠ (4.5:1 Ù„Ù„Ù†Øµ Ø§Ù„Ø¹Ø§Ø¯ÙŠ)</li>`;
            response += `<li>Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ØªÙ†Ù‚Ù„ Ø¨Ù„ÙˆØ­Ø© Ø§Ù„Ù…ÙØ§ØªÙŠØ­</li>`;
            response += `<li>Ø§Ø³ØªØ®Ø¯Ø§Ù… Ø¹Ù†Ø§ÙˆÙŠÙ† Ù‡Ø±Ù…ÙŠØ© (h1, h2, h3...)</li>`;
            response += `<li>ØªØ³Ù…ÙŠØ§Øª ÙˆØ§Ø¶Ø­Ø© Ù„Ù„Ù†Ù…Ø§Ø°Ø¬</li>`;
            response += `<li>Ø­Ø¬Ù… Ø£Ù‡Ø¯Ø§Ù Ø§Ù„Ù„Ù…Ø³ 44px Ø¹Ù„Ù‰ Ø§Ù„Ø£Ù‚Ù„</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">ðŸ› ï¸ Ø£Ø¯ÙˆØ§Øª Ù…Ø¬Ø§Ù†ÙŠØ© Ù„Ù„ÙØ­Øµ:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px; line-height: 1.6;">`;
            response += `<li><strong>WAVE:</strong> wave.webaim.org</li>`;
            response += `<li><strong>axe DevTools:</strong> Ø¥Ø¶Ø§ÙØ© Ù…ØªØµÙØ­ Ù…Ø¬Ø§Ù†ÙŠØ©</li>`;
            response += `<li><strong>Lighthouse:</strong> Ù…Ø¯Ù…Ø¬ ÙÙŠ Chrome DevTools</li>`;
            response += `<li><strong>Color Contrast Analyzer:</strong> Ù„ÙØ­Øµ Ø§Ù„ØªØ¨Ø§ÙŠÙ†</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">ðŸ“ Ù…Ø«Ø§Ù„ Ø¹Ù„Ù‰ ÙƒÙˆØ¯ ÙŠØ¯Ø¹Ù… Ø¥Ù…ÙƒØ§Ù†ÙŠØ© Ø§Ù„ÙˆØµÙˆÙ„:</h4>`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap;"><code>&lt;button
  aria-label="Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„Ù†Ø§ÙØ°Ø©"
  class="close-btn"
  tabindex="0"&gt;
  &lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;
&lt;/button&gt;

&lt;img
  src="image.jpg"
  alt="ÙˆØµÙ Ù…ÙØµÙ„ Ù„Ù„ØµÙˆØ±Ø©"
  role="img"&gt;

&lt;form&gt;
  &lt;label for="email"&gt;Ø§Ù„Ø¨Ø±ÙŠØ¯ Ø§Ù„Ø¥Ù„ÙƒØªØ±ÙˆÙ†ÙŠ&lt;/label&gt;
  &lt;input
    type="email"
    id="email"
    required
    aria-describedby="email-help"&gt;
  &lt;div id="email-help"&gt;Ø³Ù†Ø³ØªØ®Ø¯Ù… Ø¨Ø±ÙŠØ¯Ùƒ Ù„Ù„ØªÙˆØ§ØµÙ„&lt;/div&gt;
&lt;/form&gt;</code></pre>`;
            response += `</div>`;

            response += `</div>`;

            return response;
        }
    },
    {
        name: 'Ø¨Ø­Ø« DuckDuckGo',
        description: 'Ø§Ø¨Ø­Ø« ÙÙŠ Ø§Ù„Ø¥Ù†ØªØ±Ù†Øª Ø¨Ø³Ø±Ø¹Ø© Ø¹Ø¨Ø± DuckDuckGo (Ù†ØªØ§Ø¦Ø¬ ÙÙˆØ±ÙŠØ© Ù…Ø®ØªØµØ±Ø©). Ø§ÙƒØªØ¨: Ø§Ø¨Ø­Ø« ÙÙŠ Ø¬ÙˆØ¬Ù„ Ø¹Ù† ...',
        enabled: true,
        match: (msg) => /^Ø§Ø¨Ø­Ø« ÙÙŠ Ø¬ÙˆØ¬Ù„ Ø¹Ù† (.+)$/i.test(msg),
        execute: async (msg) => {
            const query = msg.match(/^Ø§Ø¨Ø­Ø« ÙÙŠ Ø¬ÙˆØ¬Ù„ Ø¹Ù† (.+)$/i)[1];
            // DuckDuckGo Instant Answer API (Ù…Ø¬Ø§Ù†ÙŠ)
            const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1&skip_disambig=1`;
            const res = await fetch(url);
            const data = await res.json();
            let answer = data.AbstractText || data.Answer || data.Heading || '';
            let link = data.AbstractURL || '';
            let results = '';
            if (answer) results += `<b>Ø§Ù„Ù†ØªÙŠØ¬Ø©:</b> ${answer}<br>`;
            if (link) results += `<a href='${link}' target='_blank'>Ø±Ø§Ø¨Ø·</a><br>`;
            if (!results) results = 'Ù„Ù… ÙŠØªÙ… Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ Ù†ØªÙŠØ¬Ø© Ù…Ø¨Ø§Ø´Ø±Ø©. Ø¬Ø±Ø¨ Ø³Ø¤Ø§Ù„Ø§Ù‹ Ø¢Ø®Ø±.';
            return results;
        }
    },
    {
        name: 'Ø£ÙˆØ§Ù…Ø± Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ø°ÙƒÙŠØ©',
        description: 'Ù†ÙØ° Ø£ÙˆØ§Ù…Ø± Ø¹Ù„Ù‰ Ø§Ù„Ù…Ù„ÙØ§Øª ÙˆØ§Ù„Ù…Ø¬Ù„Ø¯Ø§Øª Ù…Ø¨Ø§Ø´Ø±Ø© Ù…Ù† Ø§Ù„Ø´Ø§Øª: Ø­Ø°ÙØŒ Ø¥Ù†Ø´Ø§Ø¡ØŒ ÙØªØ­ØŒ Ø¥Ø¹Ø§Ø¯Ø© ØªØ³Ù…ÙŠØ©ØŒ Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰... (Ù…Ø«Ø§Ù„: Ø§Ø­Ø°Ù Ù…Ù„Ù main.js)',
        enabled: true,
        match: (msg) => /^(Ø§Ø­Ø°Ù Ù…Ù„Ù|Ø£Ù†Ø´Ø¦ Ù…Ø¬Ù„Ø¯|Ø§ÙØªØ­ Ù…Ù„Ù|Ø£Ø¹Ø¯ ØªØ³Ù…ÙŠØ© Ù…Ù„Ù|Ø§Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰ Ù…Ù„Ù)\s+/i.test(msg),
        execute: async (msg) => {
            // Ø­Ø°Ù Ù…Ù„Ù
            if (/^Ø§Ø­Ø°Ù Ù…Ù„Ù (.+)$/i.test(msg)) {
                const fileName = msg.match(/^Ø§Ø­Ø°Ù Ù…Ù„Ù (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    delete workspace.files[file.id];
                    for (const folder of Object.values(workspace.folders)) {
                        if (folder && Array.isArray(folder.children)) {
                            const idx = folder.children.indexOf(file.id);
                            if (idx !== -1) folder.children.splice(idx, 1);
                        }
                    }
                    updateFileExplorer();
                    return `âœ… ØªÙ… Ø­Ø°Ù Ø§Ù„Ù…Ù„Ù <b>${fileName}</b> Ø¨Ù†Ø¬Ø§Ø­.`;
                } else {
                    return `âŒ Ø§Ù„Ù…Ù„Ù <b>${fileName}</b> ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯.`;
                }
            }
            // Ø£Ù†Ø´Ø¦ Ù…Ø¬Ù„Ø¯
            if (/^Ø£Ù†Ø´Ø¦ Ù…Ø¬Ù„Ø¯ (.+)$/i.test(msg)) {
                const folderName = msg.match(/^Ø£Ù†Ø´Ø¦ Ù…Ø¬Ù„Ø¯ (.+)$/i)[1].trim();
                const newPath = workspace.currentPath + folderName + '/';
                createFolder(newPath);
                updateFileExplorer();
                return `âœ… ØªÙ… Ø¥Ù†Ø´Ø§Ø¡ Ø§Ù„Ù…Ø¬Ù„Ø¯ <b>${folderName}</b> Ø¨Ù†Ø¬Ø§Ø­.`;
            }
            // Ø§ÙØªØ­ Ù…Ù„Ù
            if (/^Ø§ÙØªØ­ Ù…Ù„Ù (.+)$/i.test(msg)) {
                const fileName = msg.match(/^Ø§ÙØªØ­ Ù…Ù„Ù (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    openFile(file.id);
                    return `âœ… ØªÙ… ÙØªØ­ Ø§Ù„Ù…Ù„Ù <b>${fileName}</b> ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±.`;
                } else {
                    return `âŒ Ø§Ù„Ù…Ù„Ù <b>${fileName}</b> ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯.`;
                }
            }
            // Ø£Ø¹Ø¯ ØªØ³Ù…ÙŠØ© Ù…Ù„Ù
            if (/^Ø£Ø¹Ø¯ ØªØ³Ù…ÙŠØ© Ù…Ù„Ù (.+) Ø¥Ù„Ù‰ (.+)$/i.test(msg)) {
                const [_, oldName, newName] = msg.match(/^Ø£Ø¹Ø¯ ØªØ³Ù…ÙŠØ© Ù…Ù„Ù (.+) Ø¥Ù„Ù‰ (.+)$/i);
                const file = Object.values(workspace.files).find(f => f.name === oldName.trim());
                if (file) {
                    file.name = newName.trim();
                    updateFileExplorer();
                    return `âœ… ØªÙ… ØªØºÙŠÙŠØ± Ø§Ø³Ù… Ø§Ù„Ù…Ù„Ù Ù…Ù† <b>${oldName}</b> Ø¥Ù„Ù‰ <b>${newName}</b>.`;
                } else {
                    return `âŒ Ø§Ù„Ù…Ù„Ù <b>${oldName}</b> ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯.`;
                }
            }
            // Ø§Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰ Ù…Ù„Ù
            if (/^Ø§Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰ Ù…Ù„Ù (.+)$/i.test(msg)) {
                const fileName = msg.match(/^Ø§Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰ Ù…Ù„Ù (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    return `<b>Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ù…Ù„Ù ${fileName}:</b><pre><code>${escapeHtml(file.content)}</code></pre>`;
                } else {
                    return `âŒ Ø§Ù„Ù…Ù„Ù <b>${fileName}</b> ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯.`;
                }
            }
            return 'âš ï¸ Ù„Ù… ÙŠØªÙ… Ø§Ù„ØªØ¹Ø±Ù Ø¹Ù„Ù‰ Ø§Ù„Ø£Ù…Ø±. Ø¬Ø±Ø¨: Ø§Ø­Ø°Ù Ù…Ù„Ù ...ØŒ Ø£Ù†Ø´Ø¦ Ù…Ø¬Ù„Ø¯ ...ØŒ Ø§ÙØªØ­ Ù…Ù„Ù ...ØŒ Ø£Ø¹Ø¯ ØªØ³Ù…ÙŠØ© Ù…Ù„Ù ...ØŒ Ø§Ø¹Ø±Ø¶ Ù…Ø­ØªÙˆÙ‰ Ù…Ù„Ù ...';
        }
    },
    {
        name: 'Ø§Ø³ØªØ¹Ø±Ø§Ø¶ Ø§Ù„Ù…Ù„ÙØ§Øª',
        description: 'Ø§Ø³ØªØ¹Ø±Ø¶ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ØªØ§Ø­Ø© ÙÙŠ Ø§Ù„Ù…Ø´Ø±ÙˆØ¹ ÙˆØ§Ø³Ù…Ø­ Ù„Ù„Ù…Ø³ØªØ®Ø¯Ù… Ø¨ÙØªØ­ Ù…Ù„Ù Ù…Ø¨Ø§Ø´Ø±Ø©. Ø§Ø³ØªØ®Ø¯Ù…: Ø§Ø¹Ø±Ø¶ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ØªØ§Ø­Ø©',
        enabled: true,
        match: (msg) => /^Ø§Ø¹Ø±Ø¶ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ØªØ§Ø­Ø©$|^Ø§Ø¸Ù‡Ø± Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª$|^Ø¹Ø±Ø¶ Ø§Ù„Ù…Ù„ÙØ§Øª$/i.test(msg),
        execute: async (msg) => {
            const files = Object.values(workspace.files);
            if (files.length === 0) {
                return 'âŒ Ù„Ø§ ØªÙˆØ¬Ø¯ Ù…Ù„ÙØ§Øª ÙÙŠ Ø§Ù„Ù…Ø´Ø±ÙˆØ¹ Ø­Ø§Ù„ÙŠÙ‹Ø§.';
            }

            let result = '<b>Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ØªØ§Ø­Ø© ÙÙŠ Ø§Ù„Ù…Ø´Ø±ÙˆØ¹:</b><br><ul>';
            // ØªØ±ØªÙŠØ¨ Ø§Ù„Ù…Ù„ÙØ§Øª Ø­Ø³Ø¨ Ø§Ù„Ù…Ø³Ø§Ø± Ø«Ù… Ø§Ù„Ø§Ø³Ù…
            files.sort((a, b) => {
                if (a.path !== b.path) return a.path.localeCompare(b.path);
                return a.name.localeCompare(b.name);
            }).forEach(file => {
                // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ù„ÙØªØ­ Ø§Ù„Ù…Ù„Ù Ù…Ø¨Ø§Ø´Ø±Ø©
                result += `<li>${file.path}${file.name} -
                            <button onclick="openFile('${file.id}')"
                                style="background: #5e35b1; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; cursor: pointer;">
                                <i class="fas fa-code"></i> ÙØªØ­
                            </button>
                        </li>`;
            });
            result += '</ul>';
            return result;
        }
    }
];
function renderPlugins() {
    const list = document.getElementById('pluginbar-list');
    if (!list) return;
    list.innerHTML = '';
    plugins.forEach((plugin, idx) => {
        const item = document.createElement('div');
        item.className = 'plugin-item';
        item.innerHTML = `
            <div class='plugin-name'>${plugin.name}</div>
            <div class='plugin-desc'>${plugin.description}</div>
            <div class='plugin-switch'>
                <label>Ù…ÙØ¹Ù„<input type='checkbox' ${plugin.enabled ? 'checked' : ''} onchange='togglePlugin(${idx})'></label>
            </div>
        `;
        list.appendChild(item);
    });
}
function togglePluginbar() {
    const bar = document.getElementById('pluginbar');
    bar.classList.toggle('visible');
    renderPlugins();
}
function togglePlugin(idx) {
    plugins[idx].enabled = !plugins[idx].enabled;
    renderPlugins();
}
// Ø§Ø¹ØªØ±Ø§Ø¶ Ø¥Ø±Ø³Ø§Ù„ Ø§Ù„Ø±Ø³Ø§Ø¦Ù„ Ù„ØªÙØ¹ÙŠÙ„ Ø§Ù„Ø¥Ø¶Ø§ÙØ§Øª
const oldSendMessagePlugin = sendMessage;
sendMessage = async function () {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;
    // ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø¥Ø¶Ø§ÙØ© Ù…Ù†Ø§Ø³Ø¨Ø©
    const plugin = plugins.find(p => p.enabled && p.match(message));
    if (plugin) {
        // Ø£Ø¶Ù Ø±Ø³Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…
        const chatWindow = document.getElementById("chat-window");
        const userDiv = document.createElement("div");
        userDiv.className = "message user";
        userDiv.innerHTML = `<div class="message-content">${DOMPurify.sanitize(message, { KEEP_CONTENT: true })}</div>`;
        chatWindow.appendChild(userDiv);
        userDiv.scrollIntoView({ behavior: "smooth" });
        inputElem.value = "";
        inputElem.style.height = "40px";
        // Ø£Ø¶Ù Ù…Ø¤Ø´Ø± Ø§Ù†ØªØ¸Ø§Ø±
        const typingDiv = document.createElement("div");
        typingDiv.className = "message bot typing-indicator";
        typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
        chatWindow.appendChild(typingDiv);
        typingDiv.scrollIntoView({ behavior: "smooth" });
        try {
            const pluginResult = await plugin.execute(message);
            typingDiv.remove();
            const botDiv = document.createElement("div");
            botDiv.className = "message bot";
            botDiv.innerHTML = `<div class='message-content'>${pluginResult}</div>`;
            chatWindow.appendChild(botDiv);
            botDiv.scrollIntoView({ behavior: "smooth" });
        } catch (e) {
            typingDiv.remove();
            const botDiv = document.createElement("div");
            botDiv.className = "message bot error";
            botDiv.innerHTML = `<div class='message-content'>Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«Ù†Ø§Ø¡ ØªÙ†ÙÙŠØ° Ø§Ù„Ø¥Ø¶Ø§ÙØ©.</div>`;
            chatWindow.appendChild(botDiv);
            botDiv.scrollIntoView({ behavior: "smooth" });
        }
        return;
    }
    // Ø¥Ø°Ø§ Ù„Ù… ØªÙˆØ¬Ø¯ Ø¥Ø¶Ø§ÙØ© Ù…Ù†Ø§Ø³Ø¨Ø©ØŒ Ù†ÙØ° Ø§Ù„Ø³Ù„ÙˆÙƒ Ø§Ù„Ø§ÙØªØ±Ø§Ø¶ÙŠ
    await oldSendMessagePlugin.apply(this, arguments);
}

// ØªÙˆØ§Ø¨Ø¹ toolbar Ø§Ù„Ø¬Ø¯ÙŠØ¯Ø©
function closeAllSidebars(except) {
    const ids = ['file-explorer', 'sidebar', 'pluginbar'];
    ids.forEach(id => {
        if (id !== except) {
            const el = document.getElementById(id);
            if (el) el.classList.remove('visible');
            else console.warn(`Ø§Ù„Ø¹Ù†ØµØ± ${id} ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯ ÙÙŠ DOM`);
        }
    });
}

// ØªØ­Ø¯ÙŠØ« Ø¯Ø§Ù„Ø© ØªØ¨Ø¯ÙŠÙ„ Ø§Ù„Ù…Ø³ØªÙƒØ´Ù
function toggleExplorerToolbar(skipToggle) {
    closeAllSidebars('file-explorer');

    const explorer = document.getElementById('file-explorer');
    const container = document.getElementById('main-content');
    const codeExecutor = document.getElementById('code-executor');

    if (!skipToggle) {
        explorer.classList.toggle('visible');
    }
    const isExplorerVisible = explorer.classList.contains('visible');
    localStorage.setItem('explorerVisible', isExplorerVisible ? 'true' : 'false');

    // Still apply classes but don't shift content
    container.classList.toggle('explorer-visible', isExplorerVisible);
    if (codeExecutor) {
        codeExecutor.classList.toggle('explorer-visible', isExplorerVisible);
    }

    // Update toggle button state
    const explorerToggleBtn = document.getElementById('explorer-toggle');
    if (explorerToggleBtn) {
        explorerToggleBtn.classList.toggle('active', isExplorerVisible);
    }
}

function toggleSidebarToolbar() {
    closeAllSidebars('sidebar');
    document.getElementById('sidebar').classList.toggle('visible');
    // ØªÙØ¹ÙŠÙ„/ØªØ¹Ø·ÙŠÙ„ Ø§Ù„Ø²Ø±
    const menuToggleBtn = document.getElementById('menu-toggle');
    if (menuToggleBtn) {
        menuToggleBtn.classList.toggle('active', document.getElementById('sidebar').classList.contains('visible'));
    }
}

function togglePluginbarToolbar() {
    closeAllSidebars('pluginbar');
    document.getElementById('pluginbar').classList.toggle('visible');
    renderPlugins();
    // ØªÙØ¹ÙŠÙ„/ØªØ¹Ø·ÙŠÙ„ Ø§Ù„Ø²Ø±
    const pluginbarToggleBtn = document.getElementById('pluginbar-toggle');
    if (pluginbarToggleBtn) {
        pluginbarToggleBtn.classList.toggle('active', document.getElementById('pluginbar').classList.contains('visible'));
    }
}

function toggleMemorybarToolbar() {
    closeAllSidebars('memorybar');
    document.getElementById('memorybar').classList.toggle('visible');
    renderNotes();
    // ØªÙØ¹ÙŠÙ„/ØªØ¹Ø·ÙŠÙ„ Ø§Ù„Ø²Ø±
    const memorybarToggleBtn = document.getElementById('memorybar-toggle');
    if (memorybarToggleBtn) {
        memorybarToggleBtn.classList.toggle('active', document.getElementById('memorybar').classList.contains('visible'));
    }
}

function toggleTaskbarToolbar() {
    closeAllSidebars('taskbar');
    const taskbar = document.getElementById('taskbar');
    if (taskbar) {
        taskbar.classList.toggle('visible');
        // ØªÙØ¹ÙŠÙ„/ØªØ¹Ø·ÙŠÙ„ Ø§Ù„Ø²Ø±
        const taskbarToggleBtn = document.getElementById('taskbar-toggle');
        if (taskbarToggleBtn) {
            taskbarToggleBtn.classList.toggle('active', taskbar.classList.contains('visible'));
        }
    } else {
        console.error('Ø¹Ù†ØµØ± taskbar ØºÙŠØ± Ù…ÙˆØ¬ÙˆØ¯ ÙÙŠ Ø§Ù„ØµÙØ­Ø©!');
    }
}

// Ø¥Ø¶Ø§ÙØ© Ø¯Ø§Ù„Ø© Ù„Ø¥ØºÙ„Ø§Ù‚ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª
function closeAllFiles() {
    // ØªÙØ±ÙŠØº Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø©
    localStorage.setItem('openFiles', JSON.stringify([]));

    activeFileId = null;
    document.getElementById('code-executor').classList.remove('visible');

    // ØªÙ†Ø¸ÙŠÙ iframe Ø§Ù„ØªÙ†ÙÙŠØ°
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    updateFileTabs();
    updateFileExplorer();
}

// Ø¯Ø§Ù„Ø© Ø¥ØºÙ„Ø§Ù‚ Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø§Ù„Ø¬Ø§Ù†Ø¨ÙŠØ©
function closeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.style.display = 'none';

        // Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙŠÙŠÙ† Ø­Ø§Ù„Ø© Ø§Ù„Ù†Ø§ÙØ°Ø© Ø¹Ù†Ø¯ Ø§Ù„Ø¥ØºÙ„Ø§Ù‚
        sidebar.classList.remove('minimized', 'maximized');

        // Ø¥ÙŠÙ‚Ø§Ù Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
        const iframe = document.getElementById('web-preview-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.src = 'about:blank';
        }

        // Ø­ÙØ¸ Ø­Ø§Ù„Ø© Ø§Ù„Ø¥ØºÙ„Ø§Ù‚
        localStorage.setItem('webPreviewMinimized', false);
        localStorage.setItem('webPreviewMaximized', false);
    }
}

// Ø¯Ø§Ù„Ø© ØªÙ‡ÙŠØ¦Ø© Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø§Ù„Ø¬Ø§Ù†Ø¨ÙŠØ©
function initWebPreviewSidebar() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø§Ù„Ø£Ø­Ø¯Ø§Ø« Ù„Ù„Ø£Ø²Ø±Ø§Ø±
    document.getElementById('preview-refresh').addEventListener('click', refreshWebPreview);
    document.getElementById('preview-minimize').addEventListener('click', minimizeWebPreview);
    document.getElementById('preview-maximize').addEventListener('click', maximizeWebPreview);
    document.getElementById('preview-close').addEventListener('click', closeWebPreview);

    // Ù…Ø³ØªÙ…Ø¹ Ø­Ø¯Ø« Ù„ØªØºÙŠÙŠØ± Ø§Ù„Ø¬Ù‡Ø§Ø²
    const deviceSelector = document.getElementById('device-selector');
    if (deviceSelector) {
        deviceSelector.addEventListener('change', function(e) {
            changeDeviceView(e);
            // ØªÙ…Ø±ÙŠØ± Ø§Ù„Ø¥Ø·Ø§Ø± Ø¥Ù„Ù‰ Ø§Ù„Ø¹Ø±Ø¶ Ø¨Ø¹Ø¯ ØªØºÙŠÙŠØ± Ø§Ù„Ø¬Ù‡Ø§Ø²
            scrollDeviceFrameIntoView();
        });
    }

    // Ø¬Ø¹Ù„ Ø§Ù„Ù†Ø§ÙØ°Ø© Ù‚Ø§Ø¨Ù„Ø© Ù„Ù„Ø³Ø­Ø¨
    makeDraggable(sidebar, document.querySelector('.preview-drag-handle'));

    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„ØªØºÙŠÙŠØ± Ø§Ù„Ø­Ø¬Ù…
    const resizeHandle = document.getElementById('preview-resize-handle');
    if (resizeHandle) {
        resizeHandle.addEventListener('mousedown', initResize);
    }

    // Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ù…Ø­ÙÙˆØ¸Ø©
    restoreWebPreviewSettings();

    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„ØªØºÙŠÙŠØ± Ø­Ø¬Ù… Ø§Ù„Ù†Ø§ÙØ°Ø© Ù„Ø¶Ù…Ø§Ù† Ø§Ù„ØªØ¬Ø§ÙˆØ¨
    window.addEventListener('resize', adjustWebPreviewForScreenSize);
}

// Ø¯Ø§Ù„Ø© ØªØ­Ø¯ÙŠØ« Ù…Ø¹Ø§ÙŠÙ†Ø© Ø§Ù„ÙˆÙŠØ¨
function refreshWebPreview() {
    // Ø¥Ø¹Ø§Ø¯Ø© ØªÙˆÙ„ÙŠØ¯ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© ÙƒÙ…Ø§ ÙÙŠ ØªØ´ØºÙŠÙ„ ÙƒÙˆØ¯ HTML
    runCodeInExecutor();
}

// Ø¯Ø§Ù„Ø© Ø¶Ø¨Ø· Ø­Ø¬Ù… Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø¨Ù†Ø§Ø¡Ù‹ Ø¹Ù„Ù‰ Ø­Ø¬Ù… Ø§Ù„Ø´Ø§Ø´Ø©
function adjustWebPreviewForScreenSize() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar || sidebar.style.display === 'none') return;

    // Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù†Ø§ÙØ°Ø© ÙÙŠ ÙˆØ¶Ø¹ Ø§Ù„ØªÙƒØ¨ÙŠØ± Ø£Ùˆ Ø§Ù„ØªØµØºÙŠØ±ØŒ Ù„Ø§ Ù†Ù‚ÙˆÙ… Ø¨Ø£ÙŠ ØªØºÙŠÙŠØ±
    if (sidebar.classList.contains('maximized') || sidebar.classList.contains('minimized')) {
        return;
    }

    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Ø¥Ø®ÙØ§Ø¡ device-selector Ø¹Ù„Ù‰ Ø§Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ØµØºÙŠØ±Ø©
    const deviceSelectWrapper = document.querySelector('.device-select-wrapper');
    if (deviceSelectWrapper) {
        if (windowWidth <= 700) {
            deviceSelectWrapper.style.display = 'none';
        } else {
            deviceSelectWrapper.style.display = '';
        }
    }

    // Ø¶Ø¨Ø· Ø­Ø¬Ù… ÙˆÙ…ÙˆØ¶Ø¹ Ø§Ù„Ù†Ø§ÙØ°Ø© Ø¨Ù†Ø§Ø¡Ù‹ Ø¹Ù„Ù‰ Ø­Ø¬Ù… Ø§Ù„Ø´Ø§Ø´Ø©
    if (windowWidth <= 576) {
        // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ØµØºÙŠØ±Ø© Ø¬Ø¯Ù‹Ø§ (Ø§Ù„Ù‡ÙˆØ§ØªÙ)
        sidebar.style.width = '100%';
        sidebar.style.height = '100%';
        sidebar.style.top = '0';
        sidebar.style.left = '0';
    } else if (windowWidth <= 768) {
        // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ØµØºÙŠØ±Ø© (Ø§Ù„Ù‡ÙˆØ§ØªÙ Ø§Ù„ÙƒØ¨ÙŠØ±Ø©)
        sidebar.style.width = '95%';
        sidebar.style.height = '90%';
        sidebar.style.top = '5%';
        sidebar.style.left = '2.5%';
    } else if (windowWidth <= 992) {
        // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„Ù…ØªÙˆØ³Ø·Ø© (Ø§Ù„ØªØ§Ø¨Ù„Øª)
        sidebar.style.width = '90%';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '5%';
    } else if (windowWidth <= 1200) {
        // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ÙƒØ¨ÙŠØ±Ø© (Ø§Ù„Ø­ÙˆØ§Ø³ÙŠØ¨ Ø§Ù„Ù…Ø­Ù…ÙˆÙ„Ø©)
        sidebar.style.width = '80%';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '10%';
    } else {
        // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ÙƒØ¨ÙŠØ±Ø© Ø¬Ø¯Ù‹Ø§ (Ø§Ù„Ø­ÙˆØ§Ø³ÙŠØ¨ Ø§Ù„Ù…ÙƒØªØ¨ÙŠØ©)
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
    }

    // Ø¬Ø¹Ù„ iframe ÙŠØ£Ø®Ø° ÙƒÙ„ Ø§Ù„Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ù…ØªØ§Ø­Ø© Ø¯Ø§Ø¦Ù…Ø§Ù‹
    const iframe = document.getElementById('web-preview-iframe');
    if (iframe) {
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
    }
}

// ØªØ¹Ø¯ÙŠÙ„ Ø¯Ø§Ù„Ø© ØªØ´ØºÙŠÙ„ ÙƒÙˆØ¯ HTML Ù„Ø¶Ø¨Ø· Ø­Ø¬Ù… Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø¨Ø¹Ø¯ Ø¹Ø±Ø¶Ù‡Ø§
async function runHtmlCode(code) {
    try {
        // Ø¥Ù†Ø´Ø§Ø¡ blob Ù…Ù† ÙƒÙˆØ¯ HTML
        const blob = new Blob([code], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…Ø±ØªØ¨Ø·Ø© (CSSØŒ JSØŒ Ø§Ù„ØµÙˆØ±)
        const linkedFiles = findLinkedFiles(code);

        // Ø¥Ù†Ø´Ø§Ø¡ Ø®Ø±ÙŠØ·Ø© Ø§Ù„Ù…ÙˆØ§Ø±Ø¯
        const resourceMap = createResourceMap(linkedFiles);

        // Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø±ÙˆØ§Ø¨Ø· Ø§Ù„Ù…ÙˆØ§Ø±Ø¯ ÙÙŠ ÙƒÙˆØ¯ HTML
        const modifiedHtml = replaceLinkedResources(code, resourceMap);

        // Ø¥Ø¶Ø§ÙØ© CSS Ù„Ø¶Ù…Ø§Ù† ØªØ¬Ø§ÙˆØ¨ Ø§Ù„Ù…Ø­ØªÙˆÙ‰
        const responsiveMetaTag = '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
        const responsiveCSS = `
        <style>
            html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
            }
            * {
                box-sizing: border-box;
            }
        </style>
        `;

        // Ø¥Ø¯Ø±Ø§Ø¬ meta viewport Ùˆ CSS ÙÙŠ Ø§Ù„ÙƒÙˆØ¯
        let finalHtml = modifiedHtml;
        if (!finalHtml.includes('<meta name="viewport"')) {
            finalHtml = finalHtml.replace('</head>', `${responsiveMetaTag}\n</head>`);
        }
        if (!finalHtml.includes('box-sizing: border-box')) {
            finalHtml = finalHtml.replace('</head>', `${responsiveCSS}\n</head>`);
        }

        // Ø¥Ù†Ø´Ø§Ø¡ blob Ø¬Ø¯ÙŠØ¯ Ø¨Ø§Ù„ÙƒÙˆØ¯ Ø§Ù„Ù…Ø¹Ø¯Ù„
        const modifiedBlob = new Blob([finalHtml], { type: 'text/html' });
        const modifiedUrl = URL.createObjectURL(modifiedBlob);

        // Ø¹Ø±Ø¶ Ø§Ù„Ù†ØªÙŠØ¬Ø© ÙÙŠ Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø§Ù„Ø¬Ø§Ù†Ø¨ÙŠØ©
        const sidebar = document.getElementById('web-preview-sidebar');
        if (sidebar) {
            sidebar.style.display = 'flex';

            // Ø¶Ø¨Ø· Ø­Ø¬Ù… Ø§Ù„Ù†Ø§ÙØ°Ø© Ø¨Ù†Ø§Ø¡Ù‹ Ø¹Ù„Ù‰ Ø­Ø¬Ù… Ø§Ù„Ø´Ø§Ø´Ø©
            adjustWebPreviewForScreenSize();

            // ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© ÙÙŠ Ø§Ù„Ø¥Ø·Ø§Ø±
            const iframe = document.getElementById('web-preview-iframe');
            if (iframe) {
                // Ø¥Ø¸Ù‡Ø§Ø± Ù…Ø¤Ø´Ø± Ø§Ù„ØªØ­Ù…ÙŠÙ„
                document.getElementById('preview-loading').classList.add('active');

                // ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ù…Ø­ØªÙˆÙ‰ ÙÙŠ Ø§Ù„Ø¥Ø·Ø§Ø±
                iframe.src = modifiedUrl;

                // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„Ø­Ø¯Ø« ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ø¥Ø·Ø§Ø±
                iframe.onload = function() {
                    // Ø¥Ø®ÙØ§Ø¡ Ù…Ø¤Ø´Ø± Ø§Ù„ØªØ­Ù…ÙŠÙ„
                    document.getElementById('preview-loading').classList.remove('active');

                    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„ØªØºÙŠÙŠØ± Ø­Ø¬Ù… Ø§Ù„Ù†Ø§ÙØ°Ø© Ø¯Ø§Ø®Ù„ Ø§Ù„Ø¥Ø·Ø§Ø±
                    try {
                        if (iframe.contentWindow) {
                            iframe.contentWindow.addEventListener('resize', function() {
                                // ØªØ­Ø¯ÙŠØ« Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø¬Ù‡Ø§Ø² ÙÙŠ ÙˆØ§Ø¬Ù‡Ø© Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…
                                updateDeviceDimensions();
                            });
                        }

                        // ØªØ·Ø¨ÙŠÙ‚ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø² Ø§Ù„Ø­Ø§Ù„ÙŠ
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            const currentDevice = deviceSelector.value;
                            setTimeout(() => {
                                changeDeviceView(currentDevice);
                            }, 100);
                        }
                    } catch (e) {
                        console.warn('Ø®Ø·Ø£ ÙÙŠ Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ø§Ù„Ø­Ø¬Ù… Ù„Ù„Ø¥Ø·Ø§Ø±:', e);
                    }
                };

                // ØªØ¹ÙŠÙŠÙ† onerror Ù„Ù„Ø¥Ø·Ø§Ø±
                iframe.onerror = function() {
                    document.getElementById('preview-loading').classList.remove('active');
                    console.error('ÙØ´Ù„ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©');

                    // Ø¥Ø¶Ø§ÙØ© Ø±Ø³Ø§Ù„Ø© Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
                    const terminalElem = document.getElementById('executor-result');
                    if (terminalElem) {
                        terminalElem.innerHTML += '<div class="terminal-message error">ÙØ´Ù„ ØªØ­Ù…ÙŠÙ„ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©</div>';
                    }
                };
            }
        } else {
            // Ø¥Ø°Ø§ Ù„Ù… ØªÙƒÙ† Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ù…ÙˆØ¬ÙˆØ¯Ø©ØŒ Ù†Ø¹Ø±Ø¶ Ø§Ù„Ù†ØªÙŠØ¬Ø© ÙÙŠ Ù†Ø§ÙØ°Ø© Ø¬Ø¯ÙŠØ¯Ø©
            window.open(url, '_blank');
        }

        // Ø¥Ø¶Ø§ÙØ© Ø±Ø³Ø§Ù„Ø© Ù†Ø¬Ø§Ø­ ÙÙŠ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML += '<div class="terminal-message success">ØªÙ… ØªØ´ØºÙŠÙ„ ÙƒÙˆØ¯ HTML Ø¨Ù†Ø¬Ø§Ø­. ØªÙ… ÙØªØ­ Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©.</div>';
        }
    } catch (error) {
        console.error('Ø®Ø·Ø£ ÙÙŠ ØªØ´ØºÙŠÙ„ ÙƒÙˆØ¯ HTML:', error);

        // Ø¥Ø¶Ø§ÙØ© Ø±Ø³Ø§Ù„Ø© Ø®Ø·Ø£ ÙÙŠ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML += `<div class="terminal-message error">Ø®Ø·Ø£ ÙÙŠ ØªØ´ØºÙŠÙ„ ÙƒÙˆØ¯ HTML: ${error.message}</div>`;
        }
    }
}

// Ø¯Ø§Ù„Ø© Ø¬Ø¹Ù„ Ø§Ù„Ø¹Ù†ØµØ± Ù‚Ø§Ø¨Ù„ Ù„Ù„Ø³Ø­Ø¨
function makeDraggable(element, handle) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

    if (handle) {
        // Ø¥Ø°Ø§ ØªÙ… ØªØ­Ø¯ÙŠØ¯ Ù…Ù‚Ø¨Ø¶ØŒ Ø§Ø³ØªØ®Ø¯Ù…Ù‡ Ù„Ù„Ø³Ø­Ø¨
        handle.onmousedown = dragMouseDown;
        // Ø§Ø³ØªØ®Ø¯Ø§Ù… addEventListener Ù…Ø¹ passive: false Ø¨Ø¯Ù„Ø§Ù‹ Ù…Ù† ontouchstart
        handle.addEventListener('touchstart', dragTouchStart, { passive: false });
    } else {
        // ÙˆØ¥Ù„Ø§ Ø§Ø³ØªØ®Ø¯Ù… Ø§Ù„Ø¹Ù†ØµØ± Ù†ÙØ³Ù‡
        element.onmousedown = dragMouseDown;
        // Ø§Ø³ØªØ®Ø¯Ø§Ù… addEventListener Ù…Ø¹ passive: false Ø¨Ø¯Ù„Ø§Ù‹ Ù…Ù† ontouchstart
        element.addEventListener('touchstart', dragTouchStart, { passive: false });
    }

    function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();

        // Ù„Ø§ ØªØ³Ù…Ø­ Ø¨Ø§Ù„Ø³Ø­Ø¨ Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù†Ø§ÙØ°Ø© Ù…ÙƒØ¨Ø±Ø©
        if (element.classList.contains('maximized')) {
            return;
        }

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù…Ø§ÙˆØ³ Ø¹Ù†Ø¯ Ø¨Ø¯Ø¡ Ø§Ù„Ø³Ø­Ø¨
        pos3 = e.clientX;
        pos4 = e.clientY;
        document.onmouseup = closeDragElement;
        document.onmousemove = elementDrag;
    }

    // Ø¯Ø¹Ù… Ø§Ù„Ù„Ù…Ø³ Ù„Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„Ù…Ø­Ù…ÙˆÙ„Ø©
    function dragTouchStart(e) {
        // Ù„Ø§ ØªØ³Ù…Ø­ Ø¨Ø§Ù„Ø³Ø­Ø¨ Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù†Ø§ÙØ°Ø© Ù…ÙƒØ¨Ø±Ø©
        if (element.classList.contains('maximized')) {
            return;
        }

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù„Ù…Ø³ Ø§Ù„Ø£ÙˆÙ„
        const touch = e.touches[0];
        pos3 = touch.clientX;
        pos4 = touch.clientY;

        // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø£Ø­Ø¯Ø§Ø« Ø§Ù„Ù„Ù…Ø³ Ù…Ø¹ passive: false
        document.addEventListener('touchend', closeTouchDragElement, { passive: true });
        document.addEventListener('touchmove', elementTouchDrag, { passive: false });

        // Ù…Ù†Ø¹ Ø§Ù„Ø³Ù„ÙˆÙƒ Ø§Ù„Ø§ÙØªØ±Ø§Ø¶ÙŠ (Ù…Ø«Ù„ Ø§Ù„ØªÙ…Ø±ÙŠØ±)
        e.preventDefault();
    }

    function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();

        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø¬Ø¯ÙŠØ¯
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;

        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ø­Ø¯ÙˆØ¯
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = element.offsetWidth;
        const elementHeight = element.offsetHeight;

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø­Ø§Ù„ÙŠ
        let newTop = element.offsetTop - pos2;
        let newLeft = element.offsetLeft - pos1;

        // Ø§Ù„ØªØ£ÙƒØ¯ Ù…Ù† Ø£Ù† Ø§Ù„Ø¹Ù†ØµØ± Ù„Ø§ ÙŠØ®Ø±Ø¬ Ù…Ù† Ø­Ø¯ÙˆØ¯ Ø§Ù„Ù†Ø§ÙØ°Ø©
        newTop = Math.max(0, Math.min(newTop, windowHeight - 40));
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - 40));

        // ØªØ­Ø¯ÙŠØ« Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø¹Ù†ØµØ±
        element.style.top = newTop + "px";
        element.style.left = newLeft + "px";

        // Ø­ÙØ¸ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø¬Ø¯ÙŠØ¯
        if (element.id === 'web-preview-sidebar') {
            saveWebPreviewPosition();
        }
    }

    // Ø¯Ø¹Ù… Ø§Ù„Ø³Ø­Ø¨ Ø¨Ø§Ù„Ù„Ù…Ø³
    function elementTouchDrag(e) {
        // Ù…Ù†Ø¹ Ø§Ù„Ø³Ù„ÙˆÙƒ Ø§Ù„Ø§ÙØªØ±Ø§Ø¶ÙŠ
        e.preventDefault();

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù„Ù…Ø³ Ø§Ù„Ø­Ø§Ù„ÙŠ
        const touch = e.touches[0];

        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø¬Ø¯ÙŠØ¯
        pos1 = pos3 - touch.clientX;
        pos2 = pos4 - touch.clientY;
        pos3 = touch.clientX;
        pos4 = touch.clientY;

        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ø­Ø¯ÙˆØ¯
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = element.offsetWidth;
        const elementHeight = element.offsetHeight;

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø­Ø§Ù„ÙŠ
        let newTop = element.offsetTop - pos2;
        let newLeft = element.offsetLeft - pos1;

        // Ø§Ù„ØªØ£ÙƒØ¯ Ù…Ù† Ø£Ù† Ø§Ù„Ø¹Ù†ØµØ± Ù„Ø§ ÙŠØ®Ø±Ø¬ Ù…Ù† Ø­Ø¯ÙˆØ¯ Ø§Ù„Ù†Ø§ÙØ°Ø©
        newTop = Math.max(0, Math.min(newTop, windowHeight - 40));
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - 40));

        // ØªØ­Ø¯ÙŠØ« Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø¹Ù†ØµØ±
        element.style.top = newTop + "px";
        element.style.left = newLeft + "px";

        // Ø­ÙØ¸ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø§Ù„Ø¬Ø¯ÙŠØ¯
        if (element.id === 'web-preview-sidebar') {
            saveWebPreviewPosition();
        }
    }

    function closeDragElement() {
        // Ø¥ÙŠÙ‚Ø§Ù ØªØ­Ø±ÙŠÙƒ Ø§Ù„Ø¹Ù†ØµØ± Ø¹Ù†Ø¯ ØªØ±Ùƒ Ø²Ø± Ø§Ù„Ù…Ø§ÙˆØ³
        document.onmouseup = null;
        document.onmousemove = null;
    }

    // Ø¥ÙŠÙ‚Ø§Ù Ø§Ù„Ø³Ø­Ø¨ Ø¨Ø§Ù„Ù„Ù…Ø³
    function closeTouchDragElement() {
        document.removeEventListener('touchend', closeTouchDragElement);
        document.removeEventListener('touchmove', elementTouchDrag);
    }
}

// Ø¯Ø§Ù„Ø© Ø¨Ø¯Ø¡ ØªØºÙŠÙŠØ± Ø§Ù„Ø­Ø¬Ù…
function initResize(e) {
    e.preventDefault();

    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar.classList.contains('maximized')) return;

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = sidebar.offsetWidth;
    const startHeight = sidebar.offsetHeight;

    document.addEventListener('mousemove', doResize);
    document.addEventListener('mouseup', stopResize);

    function doResize(e) {
        const newWidth = startWidth + (e.clientX - startX);
        const newHeight = startHeight + (e.clientY - startY);

        // ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ø­Ø¬Ù… Ø§Ù„Ø¬Ø¯ÙŠØ¯ Ù…Ø¹ Ù…Ø±Ø§Ø¹Ø§Ø© Ø§Ù„Ø­Ø¯ Ø§Ù„Ø£Ø¯Ù†Ù‰
        sidebar.style.width = Math.max(320, newWidth) + 'px';
        sidebar.style.height = Math.max(200, newHeight) + 'px';

        // Ø­ÙØ¸ Ø§Ù„Ø­Ø¬Ù… Ø§Ù„Ø¬Ø¯ÙŠØ¯
        saveWebPreviewPosition();
    }

    function stopResize() {
        document.removeEventListener('mousemove', doResize);
        document.removeEventListener('mouseup', stopResize);
    }
}

// Ø¯Ø§Ù„Ø© Ø­ÙØ¸ Ù…ÙˆØ¶Ø¹ ÙˆØ­Ø¬Ù… Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
function saveWebPreviewPosition() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // Ø­ÙØ¸ Ø§Ù„Ù…ÙˆÙ‚Ø¹ ÙˆØ§Ù„Ø­Ø¬Ù…
    localStorage.setItem('webPreviewTop', sidebar.style.top);
    localStorage.setItem('webPreviewLeft', sidebar.style.left);
    localStorage.setItem('webPreviewWidth', sidebar.style.width);
    localStorage.setItem('webPreviewHeight', sidebar.style.height);

    // Ø­ÙØ¸ Ø­Ø§Ù„Ø© Ø§Ù„ØªØµØºÙŠØ±/Ø§Ù„ØªÙƒØ¨ÙŠØ±
    localStorage.setItem('webPreviewMaximized', sidebar.classList.contains('maximized'));
    localStorage.setItem('webPreviewMinimized', sidebar.classList.contains('minimized'));

    console.log('ØªÙ… Ø­ÙØ¸ Ù…ÙˆØ¶Ø¹ Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©:', {
        top: sidebar.style.top,
        left: sidebar.style.left,
        width: sidebar.style.width,
        height: sidebar.style.height,
        isMaximized: sidebar.classList.contains('maximized'),
        isMinimized: sidebar.classList.contains('minimized')
    });
}

// Ø¯Ø§Ù„Ø© Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙŠÙŠÙ† Ù…ÙˆØ¶Ø¹ Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
function resetWebPreviewPosition() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
    }
}

// Ø¯Ø§Ù„Ø© Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
function restoreWebPreviewSettings() {
    // Ø§Ø³ØªØ±Ø¬Ø§Ø¹ Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù†Ø§ÙØ°Ø©
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    const savedTop = localStorage.getItem('webPreviewTop');
    const savedLeft = localStorage.getItem('webPreviewLeft');
    const savedWidth = localStorage.getItem('webPreviewWidth');
    const savedHeight = localStorage.getItem('webPreviewHeight');
    const deviceType = localStorage.getItem('webPreviewDeviceType') || 'responsive';

    // Ø§Ø³ØªØ±Ø¬Ø§Ø¹ Ø§Ù„Ø­Ø¬Ù… ÙˆØ§Ù„Ù…ÙˆØ¶Ø¹ Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ù…Ø­ÙÙˆØ¸Ø©
    if (savedTop && savedLeft) {
        sidebar.style.top = savedTop;
        sidebar.style.left = savedLeft;
    } else {
        // ÙˆØ¶Ø¹ Ø§ÙØªØ±Ø§Ø¶ÙŠ Ø¥Ø°Ø§ Ù„Ù… ØªÙƒÙ† Ù…Ø­ÙÙˆØ¸Ø©
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
    }

    if (savedWidth && savedHeight) {
        sidebar.style.width = savedWidth;
        sidebar.style.height = savedHeight;
    } else {
        // Ø­Ø¬Ù… Ø§ÙØªØ±Ø§Ø¶ÙŠ Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…Ø­ÙÙˆØ¸Ø§Ù‹
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
    }

    // Ø§Ø³ØªØ±Ø¬Ø§Ø¹ Ø­Ø§Ù„Ø© Ø§Ù„ØªØµØºÙŠØ±/Ø§Ù„ØªÙƒØ¨ÙŠØ±
    const isMaximized = localStorage.getItem('webPreviewMaximized') === 'true';
    const isMinimized = localStorage.getItem('webPreviewMinimized') === 'true';

    if (isMaximized) {
        sidebar.classList.add('maximized');
    } else if (isMinimized) {
        sidebar.classList.add('minimized');
    }

    // Ø§Ø³ØªØ±Ø¬Ø§Ø¹ Ù†ÙˆØ¹ Ø§Ù„Ø¬Ù‡Ø§Ø²
    const deviceSelector = document.getElementById('device-selector');
    if (deviceSelector && deviceType) {
        deviceSelector.value = deviceType;

        // ØªØ·Ø¨ÙŠÙ‚ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø²
        setTimeout(() => {
            changeDeviceView(deviceType);

            // ØªÙ…Ø±ÙŠØ± Ø§Ù„Ø¥Ø·Ø§Ø± Ø¥Ù„Ù‰ Ø§Ù„Ø¹Ø±Ø¶
            scrollDeviceFrameIntoView();
        }, 500);
    }
}

// Ø¯Ø§Ù„Ø© Ù„Ø¶Ù…Ø§Ù† Ø£Ù† Ø§Ù„Ø¥Ø·Ø§Ø± Ù…Ø±Ø¦ÙŠ Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
function scrollDeviceFrameIntoView() {
    setTimeout(() => {
        const deviceFrame = document.getElementById('device-frame');
        if (deviceFrame) {
            // Ø§Ø³ØªØ®Ø¯Ø§Ù… scrollIntoView Ù„Ø¶Ù…Ø§Ù† Ø±Ø¤ÙŠØ© Ø§Ù„Ø¥Ø·Ø§Ø± Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
            deviceFrame.scrollIntoView({
                block: 'center',
                inline: 'center',
                behavior: 'smooth'
            });

            // ØªØ£ÙƒÙŠØ¯ Ø¥Ø¶Ø§ÙÙŠ Ù…Ù† Ø¹Ø¯Ù… ØªØºØ·ÙŠØ© Ø§Ù„Ø¬Ø²Ø¡ Ø§Ù„Ø¹Ù„ÙˆÙŠ
            const container = document.getElementById('preview-container');
            if (container) {
                container.scrollTop = 0;
            }
        }
    }, 300);
}

// ØªØ­Ø¯ÙŠØ« Ø¯Ø§Ù„Ø© openFile Ù„ØªØ­Ø¯ÙŠØ« Ù…Ø¤Ø´Ø± Ø§Ù„Ù„ØºØ© ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
const originalOpenFileWithStatusBar = openFile;
openFile = function(fileId) {
    originalOpenFileWithStatusBar(fileId);

    // ØªØ­Ø¯ÙŠØ« Ù…Ø¤Ø´Ø± Ø§Ù„Ù„ØºØ© ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
    const file = workspace.files[fileId];
    if (file && file.language) {
        const statusBar = document.querySelector('.status-bar');
        if (statusBar) {
            const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
            if (langIndicator) {
                langIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
            }
        }
    }
};

// Window resize event listener
window.addEventListener('resize', function() {
    // Update Monaco editor layout
    if (monacoEditor) {
        monacoEditor.layout();
    }

    // Update status bar responsiveness
    updateStatusBarResponsiveness();

    // Other window resize handlers...
});

// Ø¯ÙˆØ§Ù„ Ù„Ø¯Ø¹Ù… Ø±Ø¨Ø· Ø§Ù„Ù…Ù„ÙØ§Øª ÙÙŠ HTML

// Ø§Ù„Ø¹Ø«ÙˆØ± Ø¹Ù„Ù‰ Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…Ø±ØªØ¨Ø·Ø© ÙÙŠ HTML
function findLinkedFiles(htmlContent) {
    const linkedFiles = [];

    // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø±ÙˆØ§Ø¨Ø· CSS
    const cssRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
    let cssMatch;
    while ((cssMatch = cssRegex.exec(htmlContent)) !== null) {
        const href = cssMatch[1];
        if (href.endsWith('.css') || cssMatch[0].includes('stylesheet')) {
            linkedFiles.push({
                path: href,
                type: 'css'
            });
        }
    }

    // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø±ÙˆØ§Ø¨Ø· JavaScript
    const jsRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let jsMatch;
    while ((jsMatch = jsRegex.exec(htmlContent)) !== null) {
        const src = jsMatch[1];
        linkedFiles.push({
            path: src,
            type: 'js'
        });
    }

    // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ø±ÙˆØ§Ø¨Ø· Ø§Ù„ØµÙˆØ±
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let imgMatch;
    while ((imgMatch = imgRegex.exec(htmlContent)) !== null) {
        const imgPath = imgMatch[1];
        // ØªØ¬Ø§Ù‡Ù„ Ø§Ù„ØµÙˆØ± Ø§Ù„ØªÙŠ ØªØ¨Ø¯Ø£ Ø¨Ù€ http:// Ø£Ùˆ https:// Ø£Ùˆ data:
        if (!imgPath.startsWith('http://') && !imgPath.startsWith('https://') && !imgPath.startsWith('data:')) {
            linkedFiles.push({
                path: imgPath,
                type: 'img'
            });
        }
    }

    return linkedFiles;
}

// Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† Ù…Ù„Ù Ø­Ø³Ø¨ Ø§Ù„Ù…Ø³Ø§Ø±
function findFileByPath(filePath) {
    // ØªÙ†Ø¸ÙŠÙ Ø§Ù„Ù…Ø³Ø§Ø±
    let normalizedPath = filePath.trim();

    // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø³Ø§Ø± Ù…Ø·Ù„Ù‚Ù‹Ø§ (ÙŠØ¨Ø¯Ø£ Ø¨Ù€ /)ØŒ Ù†Ø²ÙŠÙ„ Ø§Ù„Ù€ / Ø§Ù„Ø£ÙˆÙ„Ù‰
    if (normalizedPath.startsWith('/')) {
        normalizedPath = normalizedPath.substring(1);
    }

    // Ø§Ù„Ø¨Ø­Ø« ÙÙŠ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ù…Ù„ÙØ§Øª
    for (const fileId in workspace.files) {
        const file = workspace.files[fileId];

        // ØªØ­Ù‚Ù‚ Ù…Ù† ØªØ·Ø§Ø¨Ù‚ Ø§Ù„Ø§Ø³Ù… Ù…Ø¨Ø§Ø´Ø±Ø©
        if (file.name === normalizedPath) {
            return file;
        }

        // ØªØ­Ù‚Ù‚ Ù…Ù† ØªØ·Ø§Ø¨Ù‚ Ø§Ù„Ù…Ø³Ø§Ø± Ø§Ù„ÙƒØ§Ù…Ù„
        const fullPath = file.path.startsWith('/') ? file.path.substring(1) : file.path;
        if (fullPath === normalizedPath || fullPath + file.name === normalizedPath) {
            return file;
        }

        // ØªØ­Ù‚Ù‚ Ù…Ù† ØªØ·Ø§Ø¨Ù‚ Ø§Ù„Ø§Ø³Ù… ÙÙ‚Ø· (Ù„Ù„Ù…Ù„ÙØ§Øª ÙÙŠ Ø§Ù„Ù…Ø¬Ù„Ø¯ Ø§Ù„Ø­Ø§Ù„ÙŠ)
        if (normalizedPath.indexOf('/') === -1 && file.name === normalizedPath) {
            return file;
        }
    }

    return null;
}

// Ø¥Ù†Ø´Ø§Ø¡ Ø®Ø±ÙŠØ·Ø© Ø§Ù„Ù…ÙˆØ§Ø±Ø¯ (Blob URLs)
function createResourceMap(linkedFiles) {
    const resourceMap = {};

    for (const linkedFile of linkedFiles) {
        const file = findFileByPath(linkedFile.path);
        if (file) {
            // ØªØ­Ø¯ÙŠØ¯ Ù†ÙˆØ¹ Ø§Ù„Ù…Ø­ØªÙˆÙ‰
            let mimeType = 'text/plain';
            if (linkedFile.type === 'css') {
                mimeType = 'text/css';
            } else if (linkedFile.type === 'js') {
                mimeType = 'application/javascript';
            } else if (linkedFile.type === 'img') {
                // ØªØ­Ø¯ÙŠØ¯ Ù†ÙˆØ¹ MIME Ù„Ù„ØµÙˆØ±Ø© Ø¨Ù†Ø§Ø¡Ù‹ Ø¹Ù„Ù‰ Ø§Ù„Ø§Ù…ØªØ¯Ø§Ø¯
                const extension = linkedFile.path.split('.').pop().toLowerCase();
                switch (extension) {
                    case 'png':
                        mimeType = 'image/png';
                        break;
                    case 'jpg':
                    case 'jpeg':
                        mimeType = 'image/jpeg';
                        break;
                    case 'gif':
                        mimeType = 'image/gif';
                        break;
                    case 'svg':
                        mimeType = 'image/svg+xml';
                        break;
                    default:
                        mimeType = 'image/png';
                }
            }

            // Ø¥Ù†Ø´Ø§Ø¡ Blob URL
            const blob = new Blob([file.content], { type: mimeType });
            resourceMap[linkedFile.path] = URL.createObjectURL(blob);
        }
    }

    return resourceMap;
}

// Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø±ÙˆØ§Ø¨Ø· Ø§Ù„Ù…ÙˆØ§Ø±Ø¯ ÙÙŠ HTML
function replaceLinkedResources(htmlContent, resourceMap) {
    let modifiedHTML = htmlContent;

    // Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø±ÙˆØ§Ø¨Ø· CSS
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.css')) {
            const regex = new RegExp(`href=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `href="${url}"`);
        }
    }

    // Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø±ÙˆØ§Ø¨Ø· JavaScript
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }

    // Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø±ÙˆØ§Ø¨Ø· Ø§Ù„ØµÙˆØ±
    for (const [path, url] of Object.entries(resourceMap)) {
        if (!path.endsWith('.css') && !path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }

    return modifiedHTML;
}

// Ø¯Ø§Ù„Ø© Ù„Ù…Ø¹Ø§Ù„Ø¬Ø© Ø§Ù„Ø£Ø­Ø±Ù Ø§Ù„Ø®Ø§ØµØ© ÙÙŠ Ø§Ù„ØªØ¹Ø¨ÙŠØ±Ø§Øª Ø§Ù„Ù†Ù…Ø·ÙŠØ©
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// ØªÙ‡ÙŠØ¦Ø© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø¹Ù†Ø¯ ØªØ­Ù…ÙŠÙ„ Ø§Ù„ØµÙØ­Ø©
function initializeTerminal() {
    // Ø¥Ø¶Ø§ÙØ© Ù…Ù‚Ø¨Ø¶ ØªØºÙŠÙŠØ± Ø§Ù„Ø­Ø¬Ù…
    addTerminalResizer();

    // Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø­Ø§Ù„Ø© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
    restoreTerminalState();

    // ØªØ­Ø¯ÙŠØ« Ø±Ø£Ø³ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
    updateTerminalHeader();

    // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
    addTerminalToggleButton();

    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ù„Ù…ÙØªØ§Ø­ ESC Ù„Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.getElementById('code-executor').classList.contains('visible')) {
            const executorFooter = document.querySelector('.executor-footer');
            if (executorFooter && !executorFooter.classList.contains('collapsed') && !executorFooter.classList.contains('hidden')) {
                executorFooter.classList.add('collapsed');
                localStorage.setItem('terminalState', 'collapsed');
                e.preventDefault();
            }
        }
    });
}

// ØªØ­Ø¯ÙŠØ« Ù…Ø³ØªÙ…Ø¹ ØªØ­Ù…ÙŠÙ„ Ø§Ù„ØµÙØ­Ø© Ù„ØªÙ‡ÙŠØ¦Ø© Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
window.addEventListener('load', function() {
    loadConversations();

    // Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù† ÙˆØ¬ÙˆØ¯ Ø§Ù„Ø¹Ù†Ø§ØµØ± Ø§Ù„Ø£Ø³Ø§Ø³ÙŠØ© Ù„Ù„ÙˆØ§Ø¬Ù‡Ø©
    const requiredElements = ['file-explorer', 'sidebar', 'pluginbar', 'memorybar', 'taskbar'];
    let missingElements = [];

    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            missingElements.push(id);
            console.error(`Ø¹Ù†ØµØ± Ø£Ø³Ø§Ø³ÙŠ Ù…ÙÙ‚ÙˆØ¯: ${id}`);
        }
    });

    // Ø¥Ù†Ø´Ø§Ø¡ Ø¹Ù†ØµØ± taskbar Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙÙ‚ÙˆØ¯Ø§Ù‹
    if (missingElements.includes('taskbar')) {
        console.log('Ø¬Ø§Ø±ÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø¹Ù†ØµØ± taskbar Ø§Ù„Ù…ÙÙ‚ÙˆØ¯...');
        const taskbar = document.createElement('div');
        taskbar.className = 'taskbar';
        taskbar.id = 'taskbar';
        taskbar.innerHTML = `
                    <div class="taskbar-header">
                        <div class="taskbar-title">Ø§Ù„Ù…Ù‡Ø§Ù…</div>
                        <button class="explorer-action" onclick="toggleTaskbarToolbar()" title="Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„Ù…Ù‡Ø§Ù…">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="taskbar-list" id="taskbar-list"></div>
                `;
        document.body.appendChild(taskbar);
        missingElements = missingElements.filter(el => el !== 'taskbar');
    }

    // Ø¥Ø¶Ø§ÙØ© Ø¹Ù†ØµØ± floating-toolbar Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙÙ‚ÙˆØ¯Ø§Ù‹
    if (!document.getElementById('floating-toolbar')) {
        console.log('Ø¬Ø§Ø±ÙŠ Ø¥Ù†Ø´Ø§Ø¡ Ø¹Ù†ØµØ± floating-toolbar Ø§Ù„Ù…ÙÙ‚ÙˆØ¯...');
        const floatingToolbar = document.createElement('div');
        floatingToolbar.className = 'floating-toolbar';
        floatingToolbar.id = 'floating-toolbar';
        document.body.appendChild(floatingToolbar);
    }

    // Ø¥Ù†Ø´Ø§Ø¡ Ù‡ÙŠÙƒÙ„ Ø§ÙØªØ±Ø§Ø¶ÙŠ Ù„Ù„Ù…Ø´Ø±ÙˆØ¹ Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹
    if (!workspace.folders['root'] || !workspace.folders['root'].children || workspace.folders['root'].children.length === 0) {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
    }

    // ØªÙ‡ÙŠØ¦Ø© Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ø¥Ø°Ø§ Ù„Ù… ØªÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ø©
    if (!localStorage.getItem('openFiles')) {
        localStorage.setItem('openFiles', JSON.stringify([]));
    }

    // Ø§Ø³ØªØ¹Ø§Ø¯Ø© Ø­Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ù…Ù† Ø§Ù„ØªØ®Ø²ÙŠÙ† Ø§Ù„Ù…Ø­Ù„ÙŠ
    const explorerVisible = localStorage.getItem('explorerVisible') === 'true';
    if (explorerVisible) {
        toggleExplorerToolbar(true); // ÙØªØ­ Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ø¨Ø¯ÙˆÙ† ØªØ¨Ø¯ÙŠÙ„ Ø§Ù„Ø­Ø§Ù„Ø©
    } else {
        // Ø§Ù„ØªØ£ÙƒØ¯ Ù…Ù† Ø£Ù† Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ù…ØºÙ„Ù‚
        document.getElementById('file-explorer').classList.remove('visible');
        // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø¹Ù†Ø§ØµØ± Ø§Ù„Ø£Ø®Ø±Ù‰ Ù„Ù„ØªÙƒÙŠÙ Ù…Ø¹ Ø­Ø§Ù„Ø© Ø§Ù„Ù…Ø³ØªÙƒØ´Ù Ø§Ù„Ù…ØºÙ„Ù‚
        document.getElementById('main-content').classList.remove('explorer-visible');
        document.querySelector('.header').classList.remove('explorer-visible');
        document.querySelector('.footer').classList.remove('explorer-visible');
        if (document.getElementById('code-executor')) {
            document.getElementById('code-executor').classList.remove('explorer-visible');
        }
    }

    updateFileExplorer();

    // ØªÙ‡ÙŠØ¦Ø© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
    initializeTerminal();

    // ØªÙ‡ÙŠØ¦Ø© Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ù…ÙØªÙˆØ­Ø©
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar && sidebar.style.display !== 'none') {
        initWebPreviewSidebar();
        sidebar.dataset.initialized = 'true';
    }

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø­Ø¯Ø§Ø« Ø£Ø²Ø±Ø§Ø± Ø§Ù„ÙƒÙˆØ¯
    function addCodeButtonListeners() {
        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø²Ø±Ø§Ø± "ÙØªØ­ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ø³ØªÙ…Ø¹ÙŠÙ† Ù„Ø£Ø²Ø±Ø§Ø± "ØªØ´ØºÙŠÙ„ Ø§Ù„ÙƒÙˆØ¯"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    setTimeout(() => {
        addCodeButtonListeners();
    }, 1000);
});

// ØªØ­Ø¯ÙŠØ« ØªÙ„Ù…ÙŠØ­ Ø§Ù„Ø£Ø¯Ø§Ø© Ù„Ø²Ø± Ø§Ø®ØªÙŠØ§Ø± Ø§Ù„Ø¬Ù‡Ø§Ø²
function updateDeviceButtonTooltip(deviceType) {
    const deviceNames = {
        responsive: 'ØªØ¬Ø§ÙˆØ¨ ÙƒØ§Ù…Ù„',
        desktop: 'Ø³Ø·Ø­ Ø§Ù„Ù…ÙƒØªØ¨',
        laptop: 'Ù„Ø§Ø¨ØªÙˆØ¨',
        tablet: 'ØªØ§Ø¨Ù„Øª',
        mobile: 'Ù…ÙˆØ¨Ø§ÙŠÙ„',
        custom: 'Ù…Ø®ØµØµ'
    };

    const deviceIcon = document.querySelector('.device-select-wrapper i');
    if (deviceIcon) {
        // ØªØºÙŠÙŠØ± Ø§Ù„Ø±Ù…Ø² Ø­Ø³Ø¨ Ù†ÙˆØ¹ Ø§Ù„Ø¬Ù‡Ø§Ø²
        deviceIcon.className = 'fas';
        if (deviceType === 'mobile') {
            deviceIcon.classList.add('fa-mobile-alt');
        } else if (deviceType === 'tablet') {
            deviceIcon.classList.add('fa-tablet-alt');
        } else if (deviceType === 'laptop') {
            deviceIcon.classList.add('fa-laptop');
        } else if (deviceType === 'desktop') {
            deviceIcon.classList.add('fa-desktop');
        } else if (deviceType === 'custom') {
            deviceIcon.classList.add('fa-ruler-combined');
        } else {
            deviceIcon.classList.add('fa-expand-arrows-alt');
        }

        // ØªØ­Ø¯ÙŠØ« Ù†Øµ Ø§Ù„ØªÙ„Ù…ÙŠØ­
        deviceIcon.setAttribute('title', `Ø§Ù„Ø¹Ø±Ø¶: ${deviceNames[deviceType] || 'ØªØ¬Ø§ÙˆØ¨ ÙƒØ§Ù…Ù„'}`);
    }
}

// Ø¯Ø§Ù„Ø© ØªÙ‡ÙŠØ¦Ø© Ù…ÙØªØ´ Ø§Ù„Ù…ØªØµÙØ­ (DevTools)
function initInspector() {
    // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ø¹Ù†Ø§ØµØ±
    const sidebar = document.getElementById('web-preview-sidebar');
    const iframe = document.getElementById('web-preview-iframe');
    const overlay = document.getElementById('inspector-overlay');
    const highlighter = document.getElementById('element-highlighter');
    const elementInfo = document.getElementById('element-info');
    const elementPath = document.getElementById('element-path');
    const sizeInfo = document.getElementById('selected-element-size');
    const mousePosition = document.getElementById('mouse-position');
    const gridOverlay = document.getElementById('grid-overlay');

    if (!sidebar || !iframe || !overlay) return;

    // ØªÙØ¹ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„Ù…ÙØªØ´
    sidebar.classList.add('inspector-mode');

    // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„ØªØ¨Ø¯ÙŠÙ„ Ø¨ÙŠÙ† Ø§Ù„ÙˆØ¶Ø¹ÙŠÙ† Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
    if (!document.getElementById('inspector-mode-toggle')) {
        const toggleBtn = document.createElement('button');
        toggleBtn.id = 'inspector-mode-toggle';
        toggleBtn.className = 'inspector-mode-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-code"></i>';
        toggleBtn.title = 'ØªØ¨Ø¯ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„Ù…ÙØªØ´';
        toggleBtn.onclick = toggleInspectorMode;
        document.querySelector('.preview-container').appendChild(toggleBtn);
    }

    // ØªØ­Ø¯ÙŠØ« Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø¬Ù‡Ø§Ø²
    updateDeviceDimensions();

    // Ø£Ø²Ø±Ø§Ø± Ø´Ø±ÙŠØ· Ø£Ø¯ÙˆØ§Øª Ø§Ù„Ù…ÙØªØ´
    document.getElementById('inspector-cursor').addEventListener('click', () => setInspectorTool('cursor'));
    document.getElementById('inspector-inspect').addEventListener('click', () => setInspectorTool('inspect'));
    document.getElementById('inspector-responsive').addEventListener('click', () => setInspectorTool('responsive'));
    document.getElementById('inspector-ruler').addEventListener('click', () => setInspectorTool('ruler'));
    document.getElementById('inspector-toggle-grid').addEventListener('click', toggleGridOverlay);
    document.getElementById('inspector-toggle-outline').addEventListener('click', toggleElementsOutline);
    document.getElementById('rotate-device').addEventListener('click', rotateDevice);
    document.getElementById('device-zoom').addEventListener('change', changeDeviceZoom);
    document.getElementById('network-throttle').addEventListener('change', changeNetworkThrottle);

    // Ù…Ø­Ø§ÙƒØ§Ø© Ø§Ù„Ù…ÙØªØ´ - ÙØ­Øµ Ø§Ù„Ø¹Ù†Ø§ØµØ±
    let inspectMode = false;

    // Ø¯Ø§Ù„Ø© ØªÙØ¹ÙŠÙ„ Ø£Ø¯Ø§Ø© Ù…Ù† Ø£Ø¯ÙˆØ§Øª Ø§Ù„Ù…ÙØªØ´
    function setInspectorTool(tool) {
        // Ø¥Ù„ØºØ§Ø¡ ØªÙ†Ø´ÙŠØ· Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø£Ø²Ø±Ø§Ø±
        document.querySelectorAll('.inspector-btn[data-active="true"]').forEach(btn => {
            btn.setAttribute('data-active', 'false');
        });

        // ØªÙ†Ø´ÙŠØ· Ø§Ù„Ø²Ø± Ø§Ù„Ù…Ø®ØªØ§Ø±
        document.getElementById(`inspector-${tool}`).setAttribute('data-active', 'true');

        // ØªÙ†ÙÙŠØ° Ø§Ù„Ø¥Ø¬Ø±Ø§Ø¡ Ø§Ù„Ù…Ù†Ø§Ø³Ø¨ Ù„Ù„Ø£Ø¯Ø§Ø©
        switch (tool) {
            case 'cursor':
                disableInspectMode();
                break;
            case 'inspect':
                enableInspectMode();
                break;
            case 'responsive':
                enableResponsiveMode();
                break;
            case 'ruler':
                enableRulerMode();
                break;
        }
    }

    // ØªÙØ¹ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„ÙØ­Øµ
    function enableInspectMode() {
        inspectMode = true;
        overlay.style.display = 'block';
        iframe.style.pointerEvents = 'none';

        // Ø¥Ø²Ø§Ù„Ø© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø§Ù„Ø£Ø­Ø¯Ø§Ø« Ø§Ù„Ø³Ø§Ø¨Ù‚Ø© Ø¥Ù† ÙˆØ¬Ø¯Øª
        document.querySelector('.preview-container').removeEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').removeEventListener('click', handleInspectClick);

        // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø§Ù„Ø£Ø­Ø¯Ø§Ø«
        document.querySelector('.preview-container').addEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').addEventListener('click', handleInspectClick);
    }

    // Ø¥Ù„ØºØ§Ø¡ ÙˆØ¶Ø¹ Ø§Ù„ÙØ­Øµ
    function disableInspectMode() {
        inspectMode = false;
        overlay.style.display = 'none';
        iframe.style.pointerEvents = 'auto';
        highlighter.style.display = 'none';
        elementInfo.style.display = 'none';

        // Ø¥Ø²Ø§Ù„Ø© Ù…Ø³ØªÙ…Ø¹ÙŠ Ø§Ù„Ø£Ø­Ø¯Ø§Ø«
        document.querySelector('.preview-container').removeEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').removeEventListener('click', handleInspectClick);

        // Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙŠÙŠÙ† Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø¹Ù†ØµØ±
        elementPath.textContent = '';
        sizeInfo.textContent = '';
    }

    // Ù…Ø¹Ø§Ù„Ø¬Ø© Ø­Ø¯Ø« ØªØ­Ø±ÙŠÙƒ Ø§Ù„Ù…Ø§ÙˆØ³ ÙÙŠ ÙˆØ¶Ø¹ Ø§Ù„ÙØ­Øµ
    function handleInspectMouseMove(e) {
        if (!inspectMode || !iframe.contentDocument) return;

        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø¯Ø§Ø®Ù„ Ø§Ù„Ø¥Ø·Ø§Ø±
        const rect = iframe.getBoundingClientRect();
        const scale = parseFloat(iframe.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);

        // Ø­Ø³Ø§Ø¨ Ø¥Ø­Ø¯Ø§Ø«ÙŠØ§Øª Ø§Ù„Ù…Ø§ÙˆØ³ Ø¯Ø§Ø®Ù„ Ø§Ù„Ø¥Ø·Ø§Ø± Ù…Ø¹ Ù…Ø±Ø§Ø¹Ø§Ø© Ø§Ù„Ù…Ù‚ÙŠØ§Ø³
        const x = (e.clientX - rect.left) / scale;
        const y = (e.clientY - rect.top) / scale;

        // ØªØ­Ø¯ÙŠØ« Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù…Ø§ÙˆØ³ ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª
        mousePosition.textContent = `${Math.round(x)}px Ã— ${Math.round(y)}px`;

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ø¹Ù†ØµØ± ØªØ­Øª Ø§Ù„Ù…Ø§ÙˆØ³
        const element = getElementFromPoint(x, y);
        if (element) {
            highlightElement(element, x, y);
        }
    }

    // Ù…Ø¹Ø§Ù„Ø¬Ø© Ø­Ø¯Ø« Ø§Ù„Ù†Ù‚Ø± ÙÙŠ ÙˆØ¶Ø¹ Ø§Ù„ÙØ­Øµ
    function handleInspectClick(e) {
        if (!inspectMode || !iframe.contentDocument) return;
        e.preventDefault();

        // Ø­Ø³Ø§Ø¨ Ø§Ù„Ù…ÙˆØ¶Ø¹ Ø¯Ø§Ø®Ù„ Ø§Ù„Ø¥Ø·Ø§Ø±
        const rect = iframe.getBoundingClientRect();
        const scale = parseFloat(iframe.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const x = (e.clientX - rect.left) / scale;
        const y = (e.clientY - rect.top) / scale;

        // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ø¹Ù†ØµØ± ØªØ­Øª Ø§Ù„Ù…Ø§ÙˆØ³
        const element = getElementFromPoint(x, y);
        if (element) {
            selectElement(element);
        }
    }

    // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ø¹Ù†ØµØ± Ø¹Ù†Ø¯ Ù†Ù‚Ø·Ø© Ù…Ø¹ÙŠÙ†Ø© Ø¯Ø§Ø®Ù„ Ø§Ù„Ø¥Ø·Ø§Ø±
    function getElementFromPoint(x, y) {
        try {
            if (!iframe.contentDocument) return null;
            return iframe.contentDocument.elementFromPoint(x, y);
        } catch (e) {
            console.error('Ø®Ø·Ø£ ÙÙŠ Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ø§Ù„Ø¹Ù†ØµØ±:', e);
            return null;
        }
    }

    // ØªØ¸Ù„ÙŠÙ„ Ø§Ù„Ø¹Ù†ØµØ±
    function highlightElement(element, x, y) {
        if (!element || element.nodeType !== 1) return;

        try {
            const rect = element.getBoundingClientRect();

            // Ø¶Ø¨Ø· Ù…ÙˆØ¶Ø¹ ÙˆØ­Ø¬Ù… Ø§Ù„Ù…Ø¸Ù„Ù„
            highlighter.style.display = 'block';
            highlighter.style.left = `${rect.left}px`;
            highlighter.style.top = `${rect.top}px`;
            highlighter.style.width = `${rect.width}px`;
            highlighter.style.height = `${rect.height}px`;

            // Ø¹Ø±Ø¶ Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø¹Ù†ØµØ±
            elementInfo.style.display = 'block';
            elementInfo.style.left = `${x + 10}px`;
            elementInfo.style.top = `${y + 10}px`;

            // Ø¥Ø¹Ø¯Ø§Ø¯ Ù…Ø­ØªÙˆÙ‰ Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø¹Ù†ØµØ±
            const tagName = element.tagName.toLowerCase();
            const classes = Array.from(element.classList).join('.');
            const id = element.id ? `#${element.id}` : '';

            // ØªÙ†Ø³ÙŠÙ‚ Ø§Ù„Ø¹Ø±Ø¶
            elementInfo.textContent = classes.length > 0 ?
                `${tagName}${id}.${classes}` :
                `${tagName}${id}`;

            // ØªØ­Ø¯ÙŠØ« Ù…Ø³Ø§Ø± Ø§Ù„Ø¹Ù†ØµØ± ÙÙŠ Ø´Ø±ÙŠØ· Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª
            elementPath.textContent = getElementPath(element);

            // Ø¹Ø±Ø¶ Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø¹Ù†ØµØ±
            sizeInfo.textContent = `${Math.round(rect.width)} Ã— ${Math.round(rect.height)}`;
        } catch (e) {
            console.error('Ø®Ø·Ø£ ÙÙŠ ØªØ¸Ù„ÙŠÙ„ Ø§Ù„Ø¹Ù†ØµØ±:', e);
        }
    }

    // Ø§Ø®ØªÙŠØ§Ø± Ø¹Ù†ØµØ± Ù„Ù„ÙØ­Øµ
    function selectElement(element) {
        // ÙŠÙ…ÙƒÙ† Ø¥Ø¶Ø§ÙØ© Ù…Ø²ÙŠØ¯ Ù…Ù† Ø§Ù„Ù…Ù†Ø·Ù‚ Ù‡Ù†Ø§
        console.log('Ø§Ù„Ø¹Ù†ØµØ± Ø§Ù„Ù…Ø­Ø¯Ø¯:', element);

        // ØªØ­Ø¯ÙŠØ« Ø´Ø±ÙŠØ· Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª
        updateElementInfo(element);
    }

    // ØªØ­Ø¯ÙŠØ« Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø¹Ù†ØµØ± Ø§Ù„Ù…Ø­Ø¯Ø¯
    function updateElementInfo(element) {
        if (!element || element.nodeType !== 1) return;

        try {
            // ØªØ­Ø¯ÙŠØ« Ù…Ø³Ø§Ø± Ø§Ù„Ø¹Ù†ØµØ±
            elementPath.textContent = getElementPath(element);

            // ØªØ­Ø¯ÙŠØ« Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø­Ø¬Ù…
            const rect = element.getBoundingClientRect();
            sizeInfo.textContent = `${Math.round(rect.width)} Ã— ${Math.round(rect.height)}`;

            // ØªÙ…ÙŠÙŠØ² Ø§Ù„Ø¹Ù†ØµØ± Ø¨Ø­Ø¯ÙˆØ¯
            const originalOutline = element.style.outline;
            element.style.outline = '2px solid #6464ff';

            // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„ØªÙ…ÙŠÙŠØ² Ø¨Ø¹Ø¯ Ø«Ø§Ù†ÙŠØªÙŠÙ†
            setTimeout(() => {
                element.style.outline = originalOutline;
            }, 2000);
        } catch (e) {
            console.error('Ø®Ø·Ø£ ÙÙŠ ØªØ­Ø¯ÙŠØ« Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø¹Ù†ØµØ±:', e);
        }
    }

    // Ø§Ù„Ø­ØµÙˆÙ„ Ø¹Ù„Ù‰ Ù…Ø³Ø§Ø± Ø§Ù„Ø¹Ù†ØµØ± ÙÙŠ DOM
    function getElementPath(element, maxDepth = 3) {
        if (!element || element.nodeType !== 1) return '';

        let path = [];
        let current = element;
        let depth = 0;

        while (current && current.nodeType === 1 && depth < maxDepth) {
            let selector = current.tagName.toLowerCase();

            if (current.id) {
                selector += `#${current.id}`;
            } else if (current.className) {
                const classList = Array.from(current.classList);
                if (classList.length > 0) {
                    selector += `.${classList[0]}`;
                }
            }

            path.unshift(selector);
            current = current.parentElement;
            depth++;
        }

        if (current && current.nodeType === 1) {
            path.unshift('...');
        }

        return path.join(' > ');
    }

    // ØªÙØ¹ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„ØªØ¬Ø§ÙˆØ¨
    function enableResponsiveMode() {
        // ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ø¬Ù‡Ø§Ø² Ø¥Ù„Ù‰ "ØªØ¬Ø§ÙˆØ¨ ÙƒØ§Ù…Ù„"
        const deviceSelector = document.getElementById('device-selector');
        if (deviceSelector) {
            deviceSelector.value = 'responsive';
            changeDeviceView('responsive');
        }
    }

    // ØªÙØ¹ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„Ù‚ÙŠØ§Ø³
    function enableRulerMode() {
        // ØªÙ†ÙÙŠØ° Ù…Ù†Ø·Ù‚ ÙˆØ¶Ø¹ Ø§Ù„Ù‚ÙŠØ§Ø³ Ù‡Ù†Ø§
        alert('ÙˆØ¶Ø¹ Ø§Ù„Ù‚ÙŠØ§Ø³ ØºÙŠØ± Ù…ØªØ§Ø­ Ø­Ø§Ù„ÙŠÙ‹Ø§');
        setInspectorTool('cursor');
    }

    // ØªØ¨Ø¯ÙŠÙ„ Ø¥Ø¸Ù‡Ø§Ø± Ø´Ø¨ÙƒØ© Ø§Ù„ØªÙˆØ¬ÙŠÙ‡
    function toggleGridOverlay() {
        const button = document.getElementById('inspector-toggle-grid');
        const isActive = button.getAttribute('data-active') === 'true';

        button.setAttribute('data-active', !isActive);
        gridOverlay.style.display = isActive ? 'none' : 'block';
    }

    // ØªØ¨Ø¯ÙŠÙ„ Ø¥Ø¸Ù‡Ø§Ø± Ø­Ø¯ÙˆØ¯ Ø§Ù„Ø¹Ù†Ø§ØµØ±
    function toggleElementsOutline() {
        const button = document.getElementById('inspector-toggle-outline');
        const isActive = button.getAttribute('data-active') === 'true';

        button.setAttribute('data-active', !isActive);

        try {
            if (!iframe.contentDocument) return;

            if (isActive) {
                // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„Ø£Ù†Ù…Ø§Ø·
                const style = iframe.contentDocument.getElementById('inspector-outline-style');
                if (style) style.remove();
            } else {
                // Ø¥Ø¶Ø§ÙØ© Ø£Ù†Ù…Ø§Ø· Ù„Ø¥Ø¸Ù‡Ø§Ø± Ø­Ø¯ÙˆØ¯ Ø§Ù„Ø¹Ù†Ø§ØµØ±
                const style = document.createElement('style');
                style.id = 'inspector-outline-style';
                style.textContent = `
                    * {
                        outline: 1px dashed rgba(120, 120, 180, 0.4) !important;
                    }

                    *:hover {
                        outline: 1px dashed rgba(120, 120, 180, 0.8) !important;
                    }
                `;
                iframe.contentDocument.head.appendChild(style);
            }
        } catch (e) {
            console.error('Ø®Ø·Ø£ ÙÙŠ ØªØ¨Ø¯ÙŠÙ„ Ø­Ø¯ÙˆØ¯ Ø§Ù„Ø¹Ù†Ø§ØµØ±:', e);
        }
    }

    // ØªØ¯ÙˆÙŠØ± Ø§Ù„Ø¬Ù‡Ø§Ø²
    function rotateDevice() {
        const deviceType = document.getElementById('device-selector').value;

        // Ù„Ø§ Ù†Ù‚ÙˆÙ… Ø¨Ø§Ù„ØªØ¯ÙˆÙŠØ± ÙÙŠ Ø§Ù„ÙˆØ¶Ø¹ Ø§Ù„Ù…ØªØ¬Ø§ÙˆØ¨
        if (deviceType === 'responsive') return;

        // Ø§Ø³ØªØ¨Ø¯Ø§Ù„ Ø§Ù„Ø¹Ø±Ø¶ Ø¨Ø§Ù„Ø§Ø±ØªÙØ§Ø¹ ÙˆØ§Ù„Ø¹ÙƒØ³ ÙÙŠ Ø§Ù„Ù…Ø­Ø§ÙƒÙŠ
        const deviceFrame = document.getElementById('device-frame');
        const tempWidth = deviceFrame.style.width;
        deviceFrame.style.width = deviceFrame.style.height;
        deviceFrame.style.height = tempWidth;

        // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø£Ø¨Ø¹Ø§Ø¯
        updateDeviceDimensions();

        // Ø£Ø¹Ø¯ Ø­Ø³Ø§Ø¨ Ø§Ù„Ù…Ù‚ÙŠØ§Ø³
        const containerWidth = document.querySelector('.preview-container').clientWidth - 80;
        const containerHeight = document.querySelector('.preview-container').clientHeight - 100;
        const deviceWidth = deviceFrame.offsetWidth;
        const deviceHeight = deviceFrame.offsetHeight;

        let scale = Math.min(
            containerWidth / deviceWidth,
            containerHeight / deviceHeight
        );

        scale = Math.min(Math.max(0.2, scale), 1);

        deviceFrame.style.transform = `scale(${scale})`;
    }

    // ØªØºÙŠÙŠØ± ØªÙƒØ¨ÙŠØ± Ø§Ù„Ø¬Ù‡Ø§Ø²
    function changeDeviceZoom(e) {
        const zoom = parseFloat(e.target.value);
        const deviceFrame = document.getElementById('device-frame');

        deviceFrame.style.transform = `scale(${zoom})`;
    }

    // ØªØºÙŠÙŠØ± Ù…Ø­Ø§ÙƒØ§Ø© Ø³Ø±Ø¹Ø© Ø§Ù„Ø´Ø¨ÙƒØ©
    function changeNetworkThrottle(e) {
        const throttle = e.target.value;
        console.log(`ØªÙ… ØªØºÙŠÙŠØ± Ù…Ø­Ø§ÙƒØ§Ø© Ø§Ù„Ø´Ø¨ÙƒØ© Ø¥Ù„Ù‰: ${throttle}`);

        // ÙŠÙ…ÙƒÙ† Ø¥Ø¶Ø§ÙØ© Ù…Ù†Ø·Ù‚ Ù…Ø­Ø§ÙƒØ§Ø© Ø§Ù„Ø³Ø±Ø¹Ø© Ù‡Ù†Ø§
        const networkIcon = document.querySelector('.network-select i');

        switch (throttle) {
            case 'online':
                networkIcon.className = 'fas fa-wifi';
                break;
            case 'fast3g':
                networkIcon.className = 'fas fa-signal';
                break;
            case 'slow3g':
                networkIcon.className = 'fas fa-signal';
                networkIcon.style.opacity = '0.6';
                break;
            case 'offline':
                networkIcon.className = 'fas fa-ban';
                break;
        }
    }

    // ØªØ­Ø¯ÙŠØ« Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø¬Ù‡Ø§Ø² ÙÙŠ Ø§Ù„Ø¹Ø±Ø¶ (Ø¯Ø§Ù„Ø© Ø¹Ø§Ù…Ø©)
    function updateDeviceDimensions() {
        const deviceFrame = document.getElementById('device-frame');
        const widthSpan = document.getElementById('device-width');
        const heightSpan = document.getElementById('device-height');
        if (deviceFrame && widthSpan && heightSpan) {
            // Ø§Ø³ØªØ®Ø¯Ø§Ù… offsetWidth Ùˆ offsetHeight Ù„Ø£Ù†Ù‡Ø§ ØªØªØ¶Ù…Ù† Ø§Ù„Ø­Ø¯ÙˆØ¯
            const width = deviceFrame.offsetWidth;
            const height = deviceFrame.offsetHeight;
            widthSpan.textContent = Math.round(width);
            heightSpan.textContent = Math.round(height);
        }
    }
}

// Ø¯Ø§Ù„Ø© ØªØ¨Ø¯ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„Ù…ÙØªØ´
function toggleInspectorMode() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // ØªØ¨Ø¯ÙŠÙ„ Ø­Ø§Ù„Ø© ÙˆØ¶Ø¹ Ø§Ù„Ù…ÙØªØ´
    if (sidebar.classList.contains('inspector-mode')) {
        sidebar.classList.remove('inspector-mode');
        document.getElementById('inspector-overlay').style.display = 'none';
    } else {
        sidebar.classList.add('inspector-mode');
        initInspector();
    }
}

// Ø¯Ø§Ù„Ø© Ø¥Ø¸Ù‡Ø§Ø±/Ø¥Ø®ÙØ§Ø¡ Ù…ÙØªØ´ Ø§Ù„Ù…ØªØµÙØ­ ÙÙŠ Ù…Ø¹Ø§ÙŠÙ†Ø© Ø§Ù„ÙˆÙŠØ¨
function toggleWebInspector() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // ØªØ¨Ø¯ÙŠÙ„ ÙˆØ¶Ø¹ Ø§Ù„Ù…ÙØªØ´
    if (sidebar.classList.contains('inspector-mode')) {
        sidebar.classList.remove('inspector-mode');
        document.getElementById('inspector-overlay').style.display = 'none';

        // Ø­ÙØ¸ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯
        localStorage.setItem('inspectorModeActive', 'false');
    } else {
        sidebar.classList.add('inspector-mode');
        initInspector();

        // Ø­ÙØ¸ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯
        localStorage.setItem('inspectorModeActive', 'true');
    }
}

// Ø¯Ø§Ù„Ø© Ù„Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„Ù…ÙØªØ´ Ø¥Ù„Ù‰ Ø´Ø±ÙŠØ· Ø£Ø¯ÙˆØ§Øª Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
function addInspectorButton() {
    const previewControls = document.querySelector('.preview-controls');
    if (!previewControls) return;

    // ØªØ­Ù‚Ù‚ Ù…Ù† Ø¹Ø¯Ù… ÙˆØ¬ÙˆØ¯ Ø§Ù„Ø²Ø± Ù…Ø³Ø¨Ù‚Ù‹Ø§
    if (document.getElementById('preview-inspector')) return;

    // Ø¥Ù†Ø´Ø§Ø¡ Ø²Ø± Ø§Ù„Ù…ÙØªØ´
    const inspectorBtn = document.createElement('button');
    inspectorBtn.id = 'preview-inspector';
    inspectorBtn.className = 'preview-btn';
    inspectorBtn.title = 'Ù…ÙØªØ´ Ø§Ù„Ø¹Ù†Ø§ØµØ±';
    inspectorBtn.innerHTML = '<i class="fas fa-search"></i>';
    inspectorBtn.onclick = toggleWebInspector;

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ø²Ø± Ù‚Ø¨Ù„ Ø²Ø± Ø§Ù„Ø¥ØºÙ„Ø§Ù‚
    const closeBtn = document.getElementById('preview-close');
    if (closeBtn) {
        previewControls.insertBefore(inspectorBtn, closeBtn);
    } else {
        previewControls.appendChild(inspectorBtn);
    }
}

// Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ø­Ø¯Ø« DOMContentLoaded Ù„ØªÙ‡ÙŠØ¦Ø© ÙˆØ§Ø¬Ù‡Ø© Ø§Ù„Ù…ÙØªØ´
document.addEventListener('DOMContentLoaded', function() {
    // Ø¥Ø¶Ø§ÙØ© Ø²Ø± Ø§Ù„Ù…ÙØªØ´ Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ù…ÙØªÙˆØ­Ø©
    setTimeout(() => {
        const previewSidebar = document.getElementById('web-preview-sidebar');
        if (previewSidebar && previewSidebar.style.display !== 'none') {
            addInspectorButton();
        }

        // Ø§Ø³ØªØ¹Ø§Ø¯Ø© ÙˆØ¶Ø¹ Ø§Ù„Ù…ÙØªØ´ Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…ÙØ¹Ù„Ø§Ù‹
        const inspectorMode = localStorage.getItem('inspectorModeActive') === 'true';
        if (inspectorMode && previewSidebar) {
            previewSidebar.classList.add('inspector-mode');
            initInspector();
        }
    }, 1000); // Ø§Ù†ØªØ¸Ø± Ù„ØªØ£ÙƒØ¯ Ù…Ù† ØªØ­Ù…ÙŠÙ„ Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø¹Ù†Ø§ØµØ±
});

// Ø¥Ù†Ø´Ø§Ø¡ ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø¬Ø¯ÙŠØ¯
function createNewTerminal() {
    // Ø­ÙØ¸ Ù…Ø­ØªÙˆÙ‰ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø§Ù„Ø­Ø§Ù„ÙŠ
    saveTerminalContent();

    // Ø¥Ù†Ø´Ø§Ø¡ ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø¬Ø¯ÙŠØ¯
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        terminalElem.innerHTML = '<div class="terminal-welcome">New terminal session started.</div>';
    }

    // ØªÙ†Ø´ÙŠØ· ØªØ¨ÙˆÙŠØ¨ Terminal
    const terminalTab = document.querySelector('.terminal-tab[data-tab="terminal"]');
    if (terminalTab) {
        const allTabs = document.querySelectorAll('.terminal-tab');
        allTabs.forEach(tab => tab.classList.remove('active'));
        terminalTab.classList.add('active');
    }
}

// ØªÙ‚Ø³ÙŠÙ… Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function splitTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // ØªØ­Ù‚Ù‚ Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ù…Ù‚Ø³Ù… Ø¨Ø§Ù„ÙØ¹Ù„
    if (executorFooter.classList.contains('split')) {
        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…Ù‚Ø³Ù… Ø¨Ø§Ù„ÙØ¹Ù„ØŒ Ù†Ø¹ÙŠØ¯Ù‡ Ø¥Ù„Ù‰ Ø§Ù„ÙˆØ¶Ø¹ Ø§Ù„Ø¹Ø§Ø¯ÙŠ
        executorFooter.classList.remove('split');

        // Ø¥Ø²Ø§Ù„Ø© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø§Ù„Ø«Ø§Ù†ÙŠ
        const secondTerminal = document.getElementById('second-terminal');
        if (secondTerminal) {
            secondTerminal.remove();
        }
    } else {
        // Ø¥Ø¶Ø§ÙØ© ØµÙ†Ù split Ù„Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
        executorFooter.classList.add('split');

        // Ø¥Ù†Ø´Ø§Ø¡ ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø«Ø§Ù†ÙŠ
        const terminalContainer = document.createElement('div');
        terminalContainer.className = 'terminal second-terminal';
        terminalContainer.id = 'second-terminal';
        terminalContainer.innerHTML = '<div class="terminal-welcome">Split terminal ready.</div>';

        // Ø¥Ø¶Ø§ÙØ© Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø§Ù„Ø«Ø§Ù†ÙŠ Ø¨Ø¹Ø¯ Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„ Ø§Ù„Ø£ÙˆÙ„
        const firstTerminal = document.getElementById('executor-result');
        if (firstTerminal && firstTerminal.parentNode) {
            firstTerminal.parentNode.insertBefore(terminalContainer, firstTerminal.nextSibling);
        }
    }
}

// Ø¹Ø±Ø¶ Ø®ÙŠØ§Ø±Ø§Øª Ø¥Ø¶Ø§ÙÙŠØ© Ù„Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function showTerminalOptions(event) {
    // Ø¥ÙŠÙ‚Ø§Ù Ø§Ù†ØªØ´Ø§Ø± Ø§Ù„Ø­Ø¯Ø« Ù„Ù…Ù†Ø¹ Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© ÙÙˆØ±Ù‹Ø§
    event.stopPropagation();

    // Ø§Ù„ØªØ­Ù‚Ù‚ Ù…Ù…Ø§ Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ù…ÙˆØ¬ÙˆØ¯Ø© Ø¨Ø§Ù„ÙØ¹Ù„
    let optionsMenu = document.getElementById('terminal-options-menu');

    if (optionsMenu) {
        // Ø¥Ø°Ø§ ÙƒØ§Ù†Øª Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ù…ÙØªÙˆØ­Ø© Ø¨Ø§Ù„ÙØ¹Ù„ØŒ Ù†ØºÙ„Ù‚Ù‡Ø§
        optionsMenu.remove();
        return;
    }

    // Ø¥Ù†Ø´Ø§Ø¡ Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ø®ÙŠØ§Ø±Ø§Øª
    optionsMenu = document.createElement('div');
    optionsMenu.id = 'terminal-options-menu';
    optionsMenu.className = 'terminal-options-menu';

    // Ø¥Ø¶Ø§ÙØ© Ø®ÙŠØ§Ø±Ø§Øª Ø§Ù„Ù‚Ø§Ø¦Ù…Ø©
    optionsMenu.innerHTML = `
        <div class="terminal-option" onclick="changeTerminalFont()">Change Font Size</div>
        <div class="terminal-option" onclick="changeTerminalTheme()">Change Terminal Theme</div>
        <div class="terminal-option" onclick="clearTerminalHistory()">Clear Terminal History</div>
        <div class="terminal-option" onclick="configureTerminal()">Terminal Settings</div>
    `;

    // ØªØ­Ø¯ÙŠØ¯ Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ø¨Ø§Ù„Ù†Ø³Ø¨Ø© Ù„Ù„Ø²Ø±
    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ø¥Ù„Ù‰ DOM
    document.body.appendChild(optionsMenu);

    // ØªØ­Ø¯ÙŠØ¯ Ù…ÙˆØ¶Ø¹ Ø§Ù„Ù‚Ø§Ø¦Ù…Ø©
    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    // Ø¥Ø¶Ø§ÙØ© Ù…Ø³ØªÙ…Ø¹ Ø­Ø¯Ø« Ù„Ø¥ØºÙ„Ø§Ù‚ Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ø¹Ù†Ø¯ Ø§Ù„Ù†Ù‚Ø± ÙÙŠ Ø£ÙŠ Ù…ÙƒØ§Ù† Ø¢Ø®Ø±
    setTimeout(() => {
        document.addEventListener('click', closeTerminalOptions);
    }, 10);
}

// Ø¥ØºÙ„Ø§Ù‚ Ù‚Ø§Ø¦Ù…Ø© Ø®ÙŠØ§Ø±Ø§Øª Ø§Ù„ØªÙŠØ±Ù…Ù†Ø§Ù„
function closeTerminalOptions() {
    const optionsMenu = document.getElementById('terminal-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeTerminalOptions);
}

// ÙˆØ¸Ø§Ø¦Ù Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ø®ÙŠØ§Ø±Ø§Øª
function changeTerminalFont() {
    const fontSize = prompt('Enter terminal font size (px):', '13');
    if (fontSize) {
        const terminal = document.getElementById('executor-result');
        if (terminal) {
            terminal.style.fontSize = `${fontSize}px`;
            localStorage.setItem('terminalFontSize', fontSize);
        }
    }
    closeTerminalOptions();
}

function changeTerminalTheme() {
    const themes = ['Dark (Default)', 'Light', 'Blue', 'Green', 'Amber'];
    const theme = prompt(`Select terminal theme (0-${themes.length - 1}):\n${themes.map((t, i) => `${i}: ${t}`).join('\n')}`, '0');

    if (theme !== null) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            const terminal = document.getElementById('executor-result');
            if (terminal) {
                // Ø¥Ø²Ø§Ù„Ø© Ø¬Ù…ÙŠØ¹ Ø£ØµÙ†Ø§Ù Ø§Ù„Ø³Ù…Ø§Øª Ø§Ù„Ø³Ø§Ø¨Ù‚Ø©
                terminal.classList.remove('theme-dark', 'theme-light', 'theme-blue', 'theme-green', 'theme-amber');

                // Ø¥Ø¶Ø§ÙØ© ØµÙ†Ù Ø§Ù„Ø³Ù…Ø© Ø§Ù„Ø¬Ø¯ÙŠØ¯Ø©
                const themeClass = `theme-${themes[themeIndex].toLowerCase().split(' ')[0]}`;
                terminal.classList.add(themeClass);
                localStorage.setItem('terminalTheme', themeClass);
            }
        }
    }
    closeTerminalOptions();
}

function clearTerminalHistory() {
    if (confirm('Are you sure you want to clear all terminal history?')) {
        window._terminalContent = null;
        localStorage.removeItem('terminalContent');

        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal history cleared.</div>';
        }
    }
    closeTerminalOptions();
}

function configureTerminal() {
    alert('Terminal settings will be available in the next update.');
    closeTerminalOptions();
}

// ØªØ¹Ø¯ÙŠÙ„ ÙˆØ¸ÙŠÙØ© ÙØªØ­ Ø§Ù„Ù…Ù„Ù Ù„Ø¥Ø¶Ø§ÙØ© Ù…Ø³Ø§Ø­Ø© ÙÙŠ Ù†Ù‡Ø§ÙŠØ© Ø§Ù„Ù…Ø­Ø±Ø±
function openFile(fileId) {
    const file = workspace.files[fileId];
    if (!file) return;

    // ... existing code ...

    // ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
    activeFileId = fileId;

    // Ø¥Ø¶Ø§ÙØ© Ø§Ù„Ù…Ù„Ù Ø¥Ù„Ù‰ Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ù„ÙØ§Øª Ø§Ù„Ù…ÙØªÙˆØ­Ø© Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹ Ø¨Ø§Ù„ÙØ¹Ù„
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    // ØªØ­Ø¯ÙŠØ« Ø¹Ù„Ø§Ù…Ø§Øª ØªØ¨ÙˆÙŠØ¨ Ø§Ù„Ù…Ù„ÙØ§Øª
    updateFileTabs();

    // ØªØ­Ø¯ÙŠØ« Ù…Ø³ØªÙƒØ´Ù Ø§Ù„Ù…Ù„ÙØ§Øª Ù„Ø¥Ø¸Ù‡Ø§Ø± Ø§Ù„Ù…Ù„Ù Ø§Ù„Ù†Ø´Ø·
    updateFileExplorer();

    // Ø¥Ø¸Ù‡Ø§Ø± Ø§Ù„Ù…Ø­Ø±Ø± Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…Ø®ÙÙŠØ§Ù‹
    const executor = document.getElementById('code-executor');
    if (executor && !executor.classList.contains('visible')) {
        executor.classList.add('visible');
    }

    // ØªØ­Ø¯ÙŠØ« Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ù…Ø­Ø±Ø±
    const editorContainer = document.getElementById('editor-container');
    if (editorContainer) {
        try {
            // Ø¥Ø°Ø§ ÙƒØ§Ù† Ø§Ù„Ù…Ø­Ø±Ø± Ù…ÙˆØ¬ÙˆØ¯Ø§Ù‹ Ø¨Ø§Ù„ÙØ¹Ù„ØŒ Ù†Ù‚ÙˆÙ… Ø¨ØªØ­Ø¯ÙŠØ« Ø§Ù„Ù…Ø­ØªÙˆÙ‰ ÙÙ‚Ø·
            if (monacoEditor) {
                const model = monaco.editor.createModel(file.content, file.language);
                monacoEditor.setModel(model);
            } else {
                // Ø¥Ù†Ø´Ø§Ø¡ Ù…Ø­Ø±Ø± Ø¬Ø¯ÙŠØ¯
                monacoEditor = monaco.editor.create(editorContainer, {
                    value: file.content,
                    language: file.language,
                    theme: 'vs-dark',
                    automaticLayout: true,
                    minimap: {
                        enabled: true
                    },
                    scrollBeyondLastLine: true, // Ø¥Ø¶Ø§ÙØ© ØªÙ…Ø±ÙŠØ± Ø¨Ø¹Ø¯ Ø§Ù„Ø³Ø·Ø± Ø§Ù„Ø£Ø®ÙŠØ±
                    padding: { // Ø¥Ø¶Ø§ÙØ© ØªØ¨Ø§Ø¹Ø¯ ÙÙŠ Ø§Ù„Ù…Ø­Ø±Ø±
                        top: 10,
                        bottom: 20 // ØªØ¨Ø§Ø¹Ø¯ Ø¥Ø¶Ø§ÙÙŠ ÙÙŠ Ø§Ù„Ø£Ø³ÙÙ„
                    },
                    lineNumbers: 'on',
                    roundedSelection: true,
                    scrollbar: {
                        useShadows: true,
                        verticalHasArrows: true,
                        horizontalHasArrows: true,
                        vertical: 'visible',
                        horizontal: 'visible',
                        verticalScrollbarSize: 12,
                        horizontalScrollbarSize: 12
                    }
                });

                // Set direction to LTR for code
                if (monacoEditor.updateOptions) {
                    monacoEditor.updateOptions({ direction: 'ltr' });
                }

                if (monacoEditor.getDomNode) {
                    monacoEditor.getDomNode().style.direction = 'ltr';
                }

                // Update content when changed
                if (monacoEditor.onDidChangeModelContent) {
                    monacoEditor.onDidChangeModelContent(function () {
                        if (fileId && workspace.files[fileId]) {
                            workspace.files[fileId].content = monacoEditor.getValue();
                        }
                    });
                }

                // Add cursor position tracking (VS Code status bar)
                monacoEditor.onDidChangeCursorPosition(function (e) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 0) {
                        statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                    }
                });

                // Add quick action buttons (like VS Code)
                const quickActions = document.createElement('div');
                quickActions.className = 'quick-actions';
                quickActions.innerHTML = `
                        <div class="quick-action" title="Split Editor" onclick="splitEditor()"><i class="fas fa-columns"></i></div>
                        <div class="quick-action" title="More Options" onclick="showEditorOptions(event)"><i class="fas fa-ellipsis-v"></i></div>
                    `;
                editorContainer.appendChild(quickActions);
            }

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    }

    // ØªØ­Ø¯ÙŠØ« Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
    updateStatusBar(file);
}

// ØªÙ‚Ø³ÙŠÙ… Ø§Ù„Ù…Ø­Ø±Ø±
function splitEditor() {
    alert('Editor split functionality will be available in the next update.');
}

// Ø¹Ø±Ø¶ Ø®ÙŠØ§Ø±Ø§Øª Ø§Ù„Ù…Ø­Ø±Ø±
function showEditorOptions(event) {
    event.stopPropagation();

    let optionsMenu = document.getElementById('editor-options-menu');

    if (optionsMenu) {
        optionsMenu.remove();
        return;
    }

    optionsMenu = document.createElement('div');
    optionsMenu.id = 'editor-options-menu';
    optionsMenu.className = 'editor-options-menu';

    optionsMenu.innerHTML = `
        <div class="editor-option" onclick="changeEditorTheme()">Change Theme</div>
        <div class="editor-option" onclick="changeFontSize()">Change Font Size</div>
        <div class="editor-option" onclick="toggleMinimap()">Toggle Minimap</div>
        <div class="editor-option" onclick="formatDocument()">Format Document</div>
    `;

    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    document.body.appendChild(optionsMenu);

    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    setTimeout(() => {
        document.addEventListener('click', closeEditorOptions);
    }, 10);
}

function closeEditorOptions() {
    const optionsMenu = document.getElementById('editor-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeEditorOptions);
}

// ÙˆØ¸Ø§Ø¦Ù Ø®ÙŠØ§Ø±Ø§Øª Ø§Ù„Ù…Ø­Ø±Ø±
function changeEditorTheme() {
    const themes = ['vs-dark', 'vs', 'hc-black'];
    const theme = prompt(`Select editor theme (0-${themes.length - 1}):\n0: Dark\n1: Light\n2: High Contrast`, '0');

    if (theme !== null && monacoEditor) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            monaco.editor.setTheme(themes[themeIndex]);
            localStorage.setItem('editorTheme', themes[themeIndex]);
        }
    }
    closeEditorOptions();
}

function changeFontSize() {
    const fontSize = prompt('Enter editor font size (px):', '14');
    if (fontSize && monacoEditor) {
        const size = parseInt(fontSize);
        if (!isNaN(size) && size > 0) {
            monacoEditor.updateOptions({ fontSize: size });
            localStorage.setItem('editorFontSize', size);
        }
    }
    closeEditorOptions();
}

function toggleMinimap() {
    if (monacoEditor) {
        const currentState = monacoEditor.getOption(monaco.editor.EditorOption.minimap).enabled;
        monacoEditor.updateOptions({ minimap: { enabled: !currentState } });
        localStorage.setItem('editorMinimapEnabled', !currentState);
    }
    closeEditorOptions();
}

function formatDocument() {
    if (monacoEditor) {
        monacoEditor.getAction('editor.action.formatDocument').run();
    }
    closeEditorOptions();
}

// ØªØ­Ø¯ÙŠØ« Ø´Ø±ÙŠØ· Ø§Ù„Ø­Ø§Ù„Ø©
function updateStatusBar(file) {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const languageIndicator = statusBar.querySelector('.language-indicator .status-item-text');
    if (languageIndicator && file) {
        languageIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
    }
}

// Ø¯Ø§Ù„Ø© ØªØµØºÙŠØ± Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
function minimizeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.classList.toggle('minimized');
        sidebar.classList.remove('maximized');

        // Ø­ÙØ¸ Ø§Ù„Ø­Ø§Ù„Ø©
        localStorage.setItem('webPreviewMinimized', sidebar.classList.contains('minimized'));
        localStorage.setItem('webPreviewMaximized', false);
    }
}

// Ø¯Ø§Ù„Ø© ØªÙƒØ¨ÙŠØ± Ù†Ø§ÙØ°Ø© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø©
function maximizeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        // Ø­ÙØ¸ Ø§Ù„Ù…ÙˆØ¶Ø¹ ÙˆØ§Ù„Ø­Ø¬Ù… Ø§Ù„Ø­Ø§Ù„ÙŠ Ù‚Ø¨Ù„ Ø§Ù„ØªÙƒØ¨ÙŠØ± Ø¥Ø°Ø§ Ù„Ù… ØªÙƒÙ† Ù…ÙƒØ¨Ø±Ø© Ø¨Ø§Ù„ÙØ¹Ù„
        if (!sidebar.classList.contains('maximized')) {
            const rect = sidebar.getBoundingClientRect();
            localStorage.setItem('webPreviewLastPosition', JSON.stringify({
                top: sidebar.style.top,
                left: sidebar.style.left,
                width: sidebar.style.width,
                height: sidebar.style.height
            }));
        }

        sidebar.classList.toggle('maximized');
        sidebar.classList.remove('minimized');

        // Ø­ÙØ¸ Ø§Ù„Ø­Ø§Ù„Ø©
        localStorage.setItem('webPreviewMaximized', sidebar.classList.contains('maximized'));
        localStorage.setItem('webPreviewMinimized', false);
    }
}

// Ø¯Ø§Ù„Ø© ØªØºÙŠÙŠØ± Ø¹Ø±Ø¶ Ø§Ù„Ø¬Ù‡Ø§Ø²
function changeDeviceView(event) {
    const deviceType = event.target ? event.target.value : event;
    const deviceFrame = document.getElementById('device-frame');
    const container = document.getElementById('preview-container');
    const deviceWrapper = document.querySelector('.device-wrapper');
    const iframe = document.getElementById('web-preview-iframe');

    if (!deviceFrame || !container || !iframe) return;

    // Ø¥Ø²Ø§Ù„Ø© Ø¬Ù…ÙŠØ¹ Ø§Ù„ÙØ¦Ø§Øª Ø§Ù„Ø³Ø§Ø¨Ù‚Ø©
    deviceFrame.className = 'device-frame';
    deviceFrame.style.width = '';
    deviceFrame.style.height = '';
    deviceFrame.style.transform = '';
    deviceFrame.style.border = 'none';
    deviceFrame.style.margin = '20px auto';
    deviceFrame.style.position = 'relative';
    container.style.overflow = 'auto';

    // ØªØ¹Ø±ÙŠÙ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª ÙƒÙ„ Ø¬Ù‡Ø§Ø²
    const deviceSettings = {
        responsive: { width: '100%', height: '100%', scale: 1, userAgent: null },
        desktop: { width: '1920px', height: '1080px', scale: 0.5, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        laptop: { width: '1366px', height: '768px', scale: 0.7, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        tablet: { width: '768px', height: '1024px', scale: 0.8, userAgent: "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X)" },
        mobile: { width: '414px', height: '896px', scale: 1, userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)" }
    };

    // ØªØ·Ø¨ÙŠÙ‚ Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ø¬Ù‡Ø§Ø² Ø§Ù„Ù…Ø­Ø¯Ø¯
    if (deviceType !== 'custom') {
        const settings = deviceSettings[deviceType] || deviceSettings.responsive;

        deviceFrame.classList.add(deviceType);

        if (deviceType === 'responsive') {
            // Ø§Ù„ÙˆØ¶Ø¹ Ø§Ù„Ù…ØªØ¬Ø§ÙˆØ¨: Ù…Ù„Ø¡ Ø§Ù„Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ù…ØªØ§Ø­Ø©
            deviceFrame.style.width = '100%';
            deviceFrame.style.height = '100%';
            deviceFrame.style.margin = '0';
            container.style.overflow = 'hidden';

            // Ø¥Ø¹Ø§Ø¯Ø© Ø¶Ø¨Ø· Ø£ÙŠ ØªØ­ÙˆÙŠÙ„Ø§Øª
            deviceFrame.style.transform = 'none';

            if (deviceWrapper) {
                deviceWrapper.style.padding = '0';
                deviceWrapper.style.overflow = 'visible';
            }
        } else {
            // ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ù…Ø­Ø¯Ø¯Ø©
            deviceFrame.style.width = settings.width;
            deviceFrame.style.height = settings.height;

            // ØªØ·Ø¨ÙŠÙ‚ Ø­Ø¯ÙˆØ¯ ÙˆØ¥Ø·Ø§Ø±Ø§Øª Ø®Ø§ØµØ© Ø¨Ø§Ù„Ø¬Ù‡Ø§Ø²
            if (deviceType === 'mobile') {
                deviceFrame.style.borderRadius = '20px';
                deviceFrame.style.border = '5px solid #333';
            } else if (deviceType === 'tablet') {
                deviceFrame.style.borderRadius = '12px';
                deviceFrame.style.border = '5px solid #333';
            } else if (deviceType === 'laptop') {
                deviceFrame.style.borderRadius = '6px 6px 0 0';
                deviceFrame.style.border = '5px solid #333';
                deviceFrame.style.borderBottom = '40px solid #333';
            } else if (deviceType === 'desktop') {
                deviceFrame.style.borderRadius = '6px 6px 0 0';
                deviceFrame.style.border = '5px solid #1e1e1e';
                deviceFrame.style.borderBottom = '25px solid #1e1e1e';
            }

            // Ø­Ø³Ø§Ø¨ Ø§Ù„Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø­Ù‚ÙŠÙ‚ÙŠØ© Ù„Ù„Ø¹Ù†ØµØ± Ù…ØªØ¶Ù…Ù†Ø© Ø§Ù„Ø­Ø¯ÙˆØ¯ ÙˆØ§Ù„Ø­Ø´ÙˆØ§Øª
            const frameTotalWidth = deviceFrame.offsetWidth;
            const frameTotalHeight = deviceFrame.offsetHeight;

            // Ø­Ø³Ø§Ø¨ Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ø±Ø¶ Ø§Ù„Ù…ØªØ§Ø­Ø© Ù…Ø¹ Ù‡ÙˆØ§Ù…Ø´ Ø£Ù…Ø§Ù†
            const containerAvailWidth = container.clientWidth - 40;
            const containerAvailHeight = container.clientHeight - 40;

            // Ø§Ø­ØªØ³Ø§Ø¨ Ø§Ù„Ù…Ù‚ÙŠØ§Ø³ Ø¨Ø´ÙƒÙ„ Ø£ÙƒØ«Ø± Ø¯Ù‚Ø© Ù…Ø¹ Ù…Ø±Ø§Ø¹Ø§Ø© Ø¬Ù…ÙŠØ¹ Ø§Ù„Ø£Ø¨Ø¹Ø§Ø¯
            let scale = Math.min(
                containerAvailWidth / frameTotalWidth,
                containerAvailHeight / frameTotalHeight
            );

            // Ø­Ø¯ Ø£Ø¯Ù†Ù‰ ÙˆØ£Ù‚ØµÙ‰ Ù„Ù„ØªÙƒØ¨ÙŠØ±/Ø§Ù„ØªØµØºÙŠØ±
            scale = Math.min(Math.max(0.2, scale), 1);

            // ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„Ù…Ù‚ÙŠØ§Ø³ Ø§Ù„Ù…Ù†Ø§Ø³Ø¨ Ø¨Ù†Ø§Ø¡Ù‹ Ø¹Ù„Ù‰ Ø­Ø¬Ù… Ø§Ù„Ø´Ø§Ø´Ø©
            const windowWidth = window.innerWidth;
            if (windowWidth <= 576) {
                // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ØµØºÙŠØ±Ø© Ø¬Ø¯Ù‹Ø§
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 0.9);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            } else if (windowWidth <= 992) {
                // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„Ù…ØªÙˆØ³Ø·Ø©
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 1);
                } else if (deviceType === 'tablet') {
                    scale = Math.min(scale, 0.7);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            } else if (windowWidth <= 1400) {
                // Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ÙƒØ¨ÙŠØ±Ø©
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 1);
                } else if (deviceType === 'tablet') {
                    scale = Math.min(scale, 0.8);
                } else if (deviceType === 'laptop') {
                    scale = Math.min(scale, 0.6);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            }

            // ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„ØªØ­ÙˆÙŠÙ„ Ù…Ø¹ Ù…Ø±ÙƒØ²Ø© Ø§Ù„Ø¹Ù†ØµØ±
            deviceFrame.style.transform = `scale(${scale})`;
            deviceFrame.style.transformOrigin = 'center center';

            // Ø¶Ù…Ø§Ù† Ø¹Ø±Ø¶ Ø§Ù„Ø¹Ù†ØµØ± ÙÙŠ Ø§Ù„Ù…Ø±ÙƒØ² ÙˆØ§Ù„Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ù…Ù†Ø§Ø³Ø¨Ø©
            if (deviceWrapper) {
                deviceWrapper.style.display = 'flex';
                deviceWrapper.style.justifyContent = 'center';
                deviceWrapper.style.alignItems = 'center';
                deviceWrapper.style.padding = '20px';
                deviceWrapper.style.overflow = 'visible';
            }

            // Ø²ÙŠØ§Ø¯Ø© Ø§Ù„Ø­Ø´ÙˆØ© ÙÙŠ Ø­Ø§ÙˆÙŠØ© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ù„Ø¶Ù…Ø§Ù† Ø¸Ù‡ÙˆØ± Ø§Ù„Ø¬Ù‡Ø§Ø² Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
            container.style.padding = '20px';
        }

        // Ø¥Ø¶Ø§ÙØ© meta viewport Ø¯ÙŠÙ†Ø§Ù…ÙŠÙƒÙŠ Ù„Ù…Ø­Ø§ÙƒØ§Ø© Ø§Ù„Ø¬Ù‡Ø§Ø²
        try {
            // Ø§Ù†ØªØ¸Ø§Ø± ØªØ­Ù…ÙŠÙ„ iframe
            setTimeout(() => {
                if (iframe.contentDocument) {
                    // Ø§Ù„Ø¨Ø­Ø« Ø¹Ù† meta viewport Ø§Ù„Ø­Ø§Ù„ÙŠ
                    let viewport = iframe.contentDocument.querySelector('meta[name="viewport"]');

                    // Ø¥Ù†Ø´Ø§Ø¡ ÙˆØ§Ø­Ø¯ Ø¬Ø¯ÙŠØ¯ Ø¥Ø°Ø§ Ù„Ù… ÙŠÙƒÙ† Ù…ÙˆØ¬ÙˆØ¯Ù‹Ø§
                    if (!viewport) {
                        viewport = document.createElement('meta');
                        viewport.name = 'viewport';
                        iframe.contentDocument.head.appendChild(viewport);
                    }

                    // ØªØ¹ÙŠÙŠÙ† Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ù…Ù†Ø§Ø³Ø¨ Ù„Ù„Ø¬Ù‡Ø§Ø²
                    if (deviceType === 'mobile') {
                        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
                    } else if (deviceType === 'tablet') {
                        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                    } else if (deviceType === 'responsive') {
                        viewport.content = 'width=device-width, initial-scale=1.0';
                    } else {
                        viewport.content = 'width=device-width, initial-scale=1.0';
                    }

                    // Ù…Ø­Ø§ÙƒØ§Ø© User-Agent Ù„Ù„Ø¬Ù‡Ø§Ø²
                    if (settings.userAgent && iframe.contentWindow.navigator) {
                        try {
                            // Ø¨Ø¯Ù„Ø§Ù‹ Ù…Ù† Ù…Ø­Ø§ÙˆÙ„Ø© ØªØºÙŠÙŠØ± user-agent Ø¨Ø´ÙƒÙ„ Ù…Ø¨Ø§Ø´Ø±ØŒ Ù†ÙƒØªÙÙŠ Ø¨ØªØ³Ø¬ÙŠÙ„Ù‡ ÙÙŠ console
                            console.log('Ù…Ø­Ø§ÙƒØ§Ø© User-Agent:', settings.userAgent);
                            // Ù„Ø§ Ù†Ø­Ø§ÙˆÙ„ ØªØ¹Ø¯ÙŠÙ„ Ø§Ù„Ø®Ø§ØµÙŠØ© Ù„Ø£Ù†Ù‡Ø§ read-only
                            // Object.defineProperty(iframe.contentWindow.navigator, 'userAgent', {
                            //     get: function() { return settings.userAgent; }
                            // });
                        } catch (e) {
                            console.warn('Ø®Ø·Ø£ ÙÙŠ Ù…Ø­Ø§ÙƒØ§Ø© user-agent:', e);
                        }
                    }

                    // Ø¥Ø¶Ø§ÙØ© CSS Ù„Ø¶Ø¨Ø· Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ø¯Ø§Ø®Ù„ iframe
                    if (iframe.contentDocument.head && !iframe.contentDocument.head.querySelector('style[data-device-style]')) {
                        const styleElement = document.createElement('style');
                        styleElement.setAttribute('data-device-style', 'true');
                        styleElement.textContent = `
                            html, body {
                                margin: 0;
                                padding: 0;
                                width: 100%;
                                height: 100%;
                                overflow: auto;
                            }
                            /* Ù„Ù„ØªØ£ÙƒØ¯ Ù…Ù† ØªÙ†Ø§Ø³Ø¨ Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ù…Ø¹ Ø¥Ø·Ø§Ø± Ø§Ù„Ø¬Ù‡Ø§Ø² */
                            * {
                                box-sizing: border-box;
                            }
                        `;
                        iframe.contentDocument.head.appendChild(styleElement);
                    }

                    // Ø¶Ø¨Ø· Ù†Ù…Ø· body Ù„Ù„ØªØ£ÙƒØ¯ Ù…Ù† Ø¹Ø¯Ù… ÙˆØ¬ÙˆØ¯ Ù‡ÙˆØ§Ù…Ø´ Ø¯Ø§Ø®Ù„ÙŠØ©
                    if (iframe.contentDocument.body) {
                        iframe.contentDocument.body.className = `device-${deviceType}`;
                        iframe.contentDocument.body.style.margin = '0';
                        iframe.contentDocument.body.style.padding = '0';
                        iframe.contentDocument.body.style.width = '100%';
                        iframe.contentDocument.body.style.height = '100%';
                        iframe.contentDocument.body.style.overflow = 'auto';
                    }
                }
            }, 300);
        } catch (e) {
            console.warn('Ø®Ø·Ø£ ÙÙŠ Ø¶Ø¨Ø· meta viewport:', e);
        }
    } else {
        // Ø¥Ø°Ø§ ÙƒØ§Ù† Ù…Ø®ØµØµÙ‹Ø§ØŒ Ø§Ø·Ù„Ø¨ Ø§Ù„Ø£Ø¨Ø¹Ø§Ø¯
        const width = prompt('Ø£Ø¯Ø®Ù„ Ø§Ù„Ø¹Ø±Ø¶ Ø¨Ø§Ù„Ø¨ÙƒØ³Ù„:', '1024');
        const height = prompt('Ø£Ø¯Ø®Ù„ Ø§Ù„Ø§Ø±ØªÙØ§Ø¹ Ø¨Ø§Ù„Ø¨ÙƒØ³Ù„:', '768');

        if (width && height) {
            deviceFrame.style.width = `${width}px`;
            deviceFrame.style.height = `${height}px`;

            // Ø¶Ø¨Ø· Ø§Ù„ØªÙƒØ¨ÙŠØ±/Ø§Ù„ØªØµØºÙŠØ± Ù„Ù„Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ù…Ø®ØµØµØ©
            // Ø§Ø­ØªØ³Ø§Ø¨ Ø§Ù„Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø­Ù‚ÙŠÙ‚ÙŠØ© Ù„Ù„Ø¹Ù†ØµØ±
            const frameTotalWidth = parseInt(width) + 20; // Ù‡Ø§Ù…Ø´ 10px Ù…Ù† ÙƒÙ„ Ø¬Ø§Ù†Ø¨
            const frameTotalHeight = parseInt(height) + 20; // Ù‡Ø§Ù…Ø´ 10px Ù…Ù† Ø£Ø¹Ù„Ù‰ ÙˆØ£Ø³ÙÙ„

            // Ø­Ø³Ø§Ø¨ Ù…Ø³Ø§Ø­Ø© Ø§Ù„Ø¹Ø±Ø¶ Ø§Ù„Ù…ØªØ§Ø­Ø© Ù…Ø¹ Ù‡ÙˆØ§Ù…Ø´ Ø£Ù…Ø§Ù†
            const containerAvailWidth = container.clientWidth - 80;
            const containerAvailHeight = container.clientHeight - 100;

            let scale = Math.min(
                containerAvailWidth / frameTotalWidth,
                containerAvailHeight / frameTotalHeight
            );

            // Ø­Ø¯ Ø£Ø¯Ù†Ù‰ ÙˆØ£Ù‚ØµÙ‰ Ù„Ù„ØªÙƒØ¨ÙŠØ±/Ø§Ù„ØªØµØºÙŠØ±
            scale = Math.min(Math.max(0.2, scale), 1);

            if (scale < 1) {
                // ØªØ·Ø¨ÙŠÙ‚ Ø§Ù„ØªØ­ÙˆÙŠÙ„ Ù…Ø¹ Ù…Ø±ÙƒØ²Ø© Ø§Ù„Ø¹Ù†ØµØ±
                deviceFrame.style.transform = `scale(${scale})`;
                deviceFrame.style.transformOrigin = 'center center';
                deviceFrame.style.position = 'relative';
                deviceFrame.style.margin = '40px auto';
                deviceFrame.style.top = '0';

                // Ø¶Ù…Ø§Ù† Ø¹Ø±Ø¶ Ø§Ù„Ø¹Ù†ØµØ± ÙÙŠ Ø§Ù„Ù…Ø±ÙƒØ²
                if (deviceWrapper) {
                    deviceWrapper.style.display = 'flex';
                    deviceWrapper.style.justifyContent = 'center';
                    deviceWrapper.style.alignItems = 'center';
                    deviceWrapper.style.padding = '40px 20px';
                    deviceWrapper.style.overflow = 'visible';
                }

                // Ø²ÙŠØ§Ø¯Ø© Ø§Ù„Ø­Ø´ÙˆØ© ÙÙŠ Ø­Ø§ÙˆÙŠØ© Ø§Ù„Ù…Ø¹Ø§ÙŠÙ†Ø© Ù„Ø¶Ù…Ø§Ù† Ø¸Ù‡ÙˆØ± Ø§Ù„Ø¬Ù‡Ø§Ø² Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
                container.style.padding = '40px 20px';
            }

            // Ø¥Ø¶Ø§ÙØ© meta viewport Ù„Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ù…Ø®ØµØµØ©
            try {
                setTimeout(() => {
                    if (iframe.contentDocument) {
                        // ØªØµØ­ÙŠØ­ Ù…ÙˆØ¶Ø¹ iframe Ù„Ø¶Ù…Ø§Ù† Ø¸Ù‡ÙˆØ±Ù‡ Ø¨Ø§Ù„ÙƒØ§Ù…Ù„
                        iframe.style.position = 'relative';
                        iframe.style.top = '0';

                        let viewport = iframe.contentDocument.querySelector('meta[name="viewport"]');
                        if (!viewport) {
                            viewport = document.createElement('meta');
                            viewport.name = 'viewport';
                            iframe.contentDocument.head.appendChild(viewport);
                        }
                        viewport.content = `width=${width}, initial-scale=1.0`;

                        // Ø¥Ø¶Ø§ÙØ© class Ù…Ø®ØµØµ Ù„Ù„Ù€ body ÙˆØ¶Ø¨Ø· Ø§Ù„Ø£Ù†Ù…Ø§Ø·
                        if (iframe.contentDocument.body) {
                            iframe.contentDocument.body.className = 'device-custom';
                            iframe.contentDocument.body.style.width = `${width}px`;
                            iframe.contentDocument.body.style.height = `${height}px`;
                            iframe.contentDocument.body.style.margin = '0';
                            iframe.contentDocument.body.style.padding = '0';
                            iframe.contentDocument.body.style.overflow = 'auto';

                            // Ø¥Ø¶Ø§ÙØ© CSS Ù„Ø¶Ø¨Ø· Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ø¯Ø§Ø®Ù„ iframe
                            const styleElement = document.createElement('style');
                            styleElement.textContent = `
                                html, body {
                                    margin: 0;
                                    padding: 0;
                                    width: 100%;
                                    height: 100%;
                                    overflow: auto;
                                }
                                /* Ù„Ù„ØªØ£ÙƒØ¯ Ù…Ù† ØªÙ†Ø§Ø³Ø¨ Ø§Ù„Ù…Ø­ØªÙˆÙ‰ Ù…Ø¹ Ø§Ù„Ø¥Ø·Ø§Ø± Ø§Ù„Ù…Ø®ØµØµ */
                                .device-custom * {
                                    box-sizing: border-box;
                                }
                            `;
                            if (!iframe.contentDocument.head.querySelector('style[data-device-style]')) {
                                styleElement.setAttribute('data-device-style', 'true');
                                iframe.contentDocument.head.appendChild(styleElement);
                            }
                        }
                    }
                }, 500);
            } catch (e) {
                console.warn('Ø®Ø·Ø£ ÙÙŠ Ø¶Ø¨Ø· meta viewport Ù„Ù„Ø¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§Ù„Ù…Ø®ØµØµØ©:', e);
            }
        }
    }

    // Ø­ÙØ¸ Ø§Ù„Ø¥Ø¹Ø¯Ø§Ø¯ Ø§Ù„Ù…Ø­Ø¯Ø¯
    localStorage.setItem('webPreviewDeviceType', deviceType);

    // ØªØ­Ø¯ÙŠØ« Ù†Øµ ØªÙ„Ù…ÙŠØ­ Ø§Ù„Ø£Ø¯Ø§Ø© Ù„Ù„Ø²Ø±
    updateDeviceButtonTooltip(deviceType);

    // ØªØ­Ø¯ÙŠØ« Ø£Ø¨Ø¹Ø§Ø¯ Ø§Ù„Ø¬Ù‡Ø§Ø² ÙÙŠ ÙˆØ§Ø¬Ù‡Ø© Ø§Ù„Ù…Ø³ØªØ®Ø¯Ù…
    updateDeviceDimensions();
}

// Ø¥Ø¶Ø§ÙØ© Ø¯Ø§Ù„Ø© updateDeviceDimensions ÙÙŠ Ù†Ù‡Ø§ÙŠØ© Ø§Ù„Ù…Ù„Ù ÙƒØ¯Ø§Ù„Ø© Ø¹Ø§Ù…Ø©
function updateDeviceDimensions() {
    const deviceFrame = document.getElementById('device-frame');
    const widthSpan = document.getElementById('device-width');
    const heightSpan = document.getElementById('device-height');
    if (deviceFrame && widthSpan && heightSpan) {
        // Ø§Ø³ØªØ®Ø¯Ø§Ù… offsetWidth Ùˆ offsetHeight Ù„Ø£Ù†Ù‡Ø§ ØªØªØ¶Ù…Ù† Ø§Ù„Ø­Ø¯ÙˆØ¯
        const width = deviceFrame.offsetWidth;
        const height = deviceFrame.offsetHeight;
        widthSpan.textContent = Math.round(width);
        heightSpan.textContent = Math.round(height);
    }
}
