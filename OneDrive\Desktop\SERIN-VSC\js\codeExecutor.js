// file: /js/codeExecutor.js
// تشغيل الأكواد

function runCode() {
    if (!activeFileId || !workspace.files[activeFileId]) {
        alert('لا يوجد ملف مفتوح للتشغيل');
        return;
    }

    const file = workspace.files[activeFileId];
    const code = monacoEditor ? monacoEditor.getValue() : file.content;

    // تحديث محتوى الملف
    if (monacoEditor) {
        file.content = code;
    }

    // تنشيط تبويب Terminal
    const terminalTab = document.querySelector('.terminal-tab[data-tab="terminal"]');
    if (terminalTab) {
        const allTabs = document.querySelectorAll('.terminal-tab');
        allTabs.forEach(tab => tab.classList.remove('active'));
        terminalTab.classList.add('active');
    }

    // إظهار منطقة النتائج
    const resultElement = document.getElementById('executor-result');
    if (resultElement) {
        resultElement.style.display = 'block';
        resultElement.innerHTML = '<div class="terminal-welcome">جاري تشغيل الكود...</div>';
    }

    // تشغيل الكود حسب نوع الملف
    switch (file.language) {
        case 'javascript':
            runJavaScript(code);
            break;
        case 'html':
            runHTML(code);
            break;
        case 'python':
            runPython(code);
            break;
        case 'css':
            runCSS(code);
            break;
        default:
            if (resultElement) {
                resultElement.innerHTML = `<div class="terminal-error">نوع الملف ${file.language} غير مدعوم للتشغيل</div>`;
            }
    }
}

function runJavaScript(code) {
    const resultElement = document.getElementById('executor-result');
    if (!resultElement) return;

    try {
        // إنشاء iframe منعزل للتنفيذ
        if (window._executorIframe && window._executorIframe.parentNode) {
            window._executorIframe.parentNode.removeChild(window._executorIframe);
        }

        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.sandbox = 'allow-scripts';
        document.body.appendChild(iframe);
        window._executorIframe = iframe;

        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // إعداد البيئة
        const context = iframe.contentWindow;
        context.console = {
            log: (...args) => {
                const output = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                resultElement.innerHTML += `<div class="terminal-output">${output}</div>`;
                resultElement.scrollTop = resultElement.scrollHeight;
            },
            error: (...args) => {
                const output = args.map(arg => String(arg)).join(' ');
                resultElement.innerHTML += `<div class="terminal-error">Error: ${output}</div>`;
                resultElement.scrollTop = resultElement.scrollHeight;
            },
            warn: (...args) => {
                const output = args.map(arg => String(arg)).join(' ');
                resultElement.innerHTML += `<div class="terminal-warning">Warning: ${output}</div>`;
                resultElement.scrollTop = resultElement.scrollHeight;
            }
        };

        // إضافة الملفات المتاحة
        context.__files = {};
        for (const [, f] of Object.entries(workspace.files)) {
            context.__files[f.name] = f.content;
        }

        // تنفيذ الكود
        resultElement.innerHTML = '<div class="terminal-welcome">تم بدء تشغيل JavaScript...</div>';

        try {
            context.eval(code);
            if (resultElement.innerHTML === '<div class="terminal-welcome">تم بدء تشغيل JavaScript...</div>') {
                resultElement.innerHTML += '<div class="terminal-success">تم تشغيل الكود بنجاح (لا توجد مخرجات)</div>';
            }
        } catch (error) {
            resultElement.innerHTML += `<div class="terminal-error">خطأ في التنفيذ: ${error.message}</div>`;
        }

    } catch (error) {
        resultElement.innerHTML = `<div class="terminal-error">خطأ في إعداد بيئة التنفيذ: ${error.message}</div>`;
    }
}

function runHTML(code) {
    const resultElement = document.getElementById('executor-result');
    if (!resultElement) return;

    try {
        // إنشاء iframe للمعاينة
        if (window._executorIframe && window._executorIframe.parentNode) {
            window._executorIframe.parentNode.removeChild(window._executorIframe);
        }

        const iframe = document.createElement('iframe');
        iframe.style.width = '100%';
        iframe.style.height = '400px';
        iframe.style.border = '1px solid #333';
        iframe.style.borderRadius = '4px';
        iframe.style.backgroundColor = '#fff';
        document.body.appendChild(iframe);
        window._executorIframe = iframe;

        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // إعداد console للـ iframe
        iframe.contentWindow.console = {
            log: (...args) => {
                const output = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                resultElement.textContent += `[معاينة]: ${output}\n`;
            },
            error: (...args) => {
                const output = args.map(arg => String(arg)).join(' ');
                resultElement.textContent += `[خطأ في المعاينة]: ${output}\n`;
            },
            warn: (...args) => {
                const output = args.map(arg => String(arg)).join(' ');
                resultElement.textContent += `[تنبيه]: ${output}\n`;
            }
        };

        iframe.contentWindow.onerror = (msg, source, line, col, error) => {
            resultElement.textContent += `[خطأ في المعاينة]: ${msg} (سطر ${line})\n`;
            return true; // منع الظهور في وحدة التحكم
        };

        // كتابة HTML في iframe
        iframeDoc.open();
        iframeDoc.write(code);
        iframeDoc.close();

        // عرض iframe في منطقة النتائج
        resultElement.innerHTML = '<div class="terminal-welcome">معاينة HTML:</div>';
        resultElement.appendChild(iframe);

    } catch (error) {
        resultElement.innerHTML = `<div class="terminal-error">خطأ في معاينة HTML: ${error.message}</div>`;
    }
}

function runPython(code) {
    const resultElement = document.getElementById('executor-result');
    if (!resultElement) return;

    // محاكاة تشغيل Python (يمكن تطويرها لاحقاً لاستخدام Pyodide)
    resultElement.innerHTML = `
        <div class="terminal-warning">تشغيل Python غير مدعوم حالياً في المتصفح</div>
        <div class="terminal-info">الكود المراد تشغيله:</div>
        <pre class="code-preview">${code}</pre>
        <div class="terminal-info">يمكنك نسخ الكود وتشغيله في بيئة Python خارجية</div>
    `;
}

function runCSS(code) {
    const resultElement = document.getElementById('executor-result');
    if (!resultElement) return;

    try {
        // إنشاء معاينة CSS
        const preview = document.createElement('div');
        preview.innerHTML = `
            <div class="css-preview">
                <h4>معاينة CSS:</h4>
                <div class="css-demo">
                    <div class="demo-element">عنصر تجريبي</div>
                    <p class="demo-text">نص تجريبي لعرض التنسيق</p>
                    <button class="demo-button">زر تجريبي</button>
                </div>
            </div>
        `;

        // إضافة الأنماط
        const style = document.createElement('style');
        style.textContent = code;
        preview.appendChild(style);

        resultElement.innerHTML = '<div class="terminal-welcome">معاينة CSS:</div>';
        resultElement.appendChild(preview);

    } catch (error) {
        resultElement.innerHTML = `<div class="terminal-error">خطأ في معاينة CSS: ${error.message}</div>`;
    }
}

function clearTerminal() {
    const resultElement = document.getElementById('executor-result');
    if (resultElement) {
        resultElement.innerHTML = '<div class="terminal-welcome">Terminal cleared.</div>';
        window._terminalContent = resultElement.innerHTML;
    }
}

function saveFile() {
    if (!activeFileId || !workspace.files[activeFileId]) {
        alert('لا يوجد ملف مفتوح للحفظ');
        return;
    }

    const file = workspace.files[activeFileId];
    if (monacoEditor) {
        file.content = monacoEditor.getValue();
    }

    // حفظ في localStorage
    saveWorkspace();

    // إظهار رسالة نجاح
    const statusBar = document.querySelector('.status-bar');
    if (statusBar) {
        const saveIndicator = document.createElement('div');
        saveIndicator.className = 'save-indicator';
        saveIndicator.textContent = 'تم الحفظ';
        saveIndicator.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            z-index: 1000;
            animation: fadeInOut 2s ease-in-out;
        `;

        document.body.appendChild(saveIndicator);

        setTimeout(() => {
            if (saveIndicator.parentNode) {
                saveIndicator.parentNode.removeChild(saveIndicator);
            }
        }, 2000);
    }
}

function downloadFile() {
    if (!activeFileId || !workspace.files[activeFileId]) {
        alert('لا يوجد ملف مفتوح للتحميل');
        return;
    }

    const file = workspace.files[activeFileId];
    const content = monacoEditor ? monacoEditor.getValue() : file.content;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// دالة تبديل التبويبات في منطقة التنفيذ
function switchTerminalTab(tabName) {
    // إزالة الفئة النشطة من جميع التبويبات
    const allTabs = document.querySelectorAll('.terminal-tab');
    allTabs.forEach(tab => tab.classList.remove('active'));

    // إضافة الفئة النشطة للتبويب المحدد
    const activeTab = document.querySelector(`.terminal-tab[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // إظهار المحتوى المناسب
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        switch (tabName) {
            case 'problems':
                terminalElem.innerHTML = '<div class="terminal-welcome">لا توجد مشاكل في الكود حالياً.</div>';
                break;
            case 'output':
                terminalElem.innerHTML = '<div class="terminal-welcome">مخرجات البرنامج ستظهر هنا.</div>';
                break;
            case 'debug':
                terminalElem.innerHTML = '<div class="terminal-welcome">وضع التصحيح غير مفعل.</div>';
                break;
            case 'terminal':
            default:
                // استعادة محتوى التيرمنال الأصلي إذا كان موجوداً
                if (window._terminalContent) {
                    terminalElem.innerHTML = window._terminalContent;
                } else {
                    terminalElem.innerHTML = '<div class="terminal-welcome">Terminal ready.</div>';
                }
                break;
        }
    }
}

// دالة حفظ محتوى التيرمنال
function saveTerminalContent() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem && terminalElem.innerHTML.trim() !== '') {
        window._terminalContent = terminalElem.innerHTML;
    }
}
